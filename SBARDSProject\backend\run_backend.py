#!/usr/bin/env python3
"""
Run the SBARDS Backend Server

This script starts the FastAPI backend server for the SBARDS project.
It handles command-line arguments, environment variables, and server configuration.
"""

import os
import sys
import argparse
import uvicorn
import logging
import dotenv
import platform
import datetime

# Define version information
__version__ = "1.0.0"
__author__ = "SBARDS Team"
__license__ = "MIT"

# Load environment variables from .env file if it exists
dotenv.load_dotenv()

# Configure logging
# Get the directory of this script
script_dir = os.path.dirname(os.path.abspath(__file__))

# Create logs directory if it doesn't exist
logs_dir = os.path.join(script_dir, "logs")
os.makedirs(logs_dir, exist_ok=True)

# Set up log file path
log_file = os.path.join(logs_dir, "backend_server.log")

# Configure logging with an improved format and rotation
from logging.handlers import RotatingFileHandler

# Create handlers
file_handler = RotatingFileHandler(
    log_file,
    maxBytes=5*1024*1024,  # 5 MB
    backupCount=3
)
console_handler = logging.StreamHandler()

# Set formatter for both handlers
formatter = logging.Formatter(
    fmt='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    handlers=[file_handler, console_handler]
)
logger = logging.getLogger("SBARDS.Backend.Server")
logger.info(f"Logging to file: {log_file}")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description=f"SBARDS Backend Server v{__version__}",
        usage="%(prog)s [options]"
    )

    # Server configuration
    server_group = parser.add_argument_group('Server Configuration')
    server_group.add_argument("--host", default="0.0.0.0", help="Host to bind the server to")
    server_group.add_argument("--port", type=int, default=8000, help="Port to bind the server to")
    server_group.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    server_group.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    server_group.add_argument("--ssl-keyfile", help="SSL key file path")
    server_group.add_argument("--ssl-certfile", help="SSL certificate file path")

    # Logging configuration
    logging_group = parser.add_argument_group('Logging Configuration')
    logging_group.add_argument("--log-level", default="info",
                        choices=["debug", "info", "warning", "error", "critical"],
                        help="Logging level")
    logging_group.add_argument("--log-file", help="Custom log file path (default: logs/backend_server.log)")

    # Environment configuration
    env_group = parser.add_argument_group('Environment Configuration')
    env_group.add_argument("--env-file", default=".env", help="Path to .env file")
    env_group.add_argument("--database-url", help="Database URL (overrides environment variable)")
    env_group.add_argument("--virustotal-api-key", help="VirusTotal API key (overrides environment variable)")

    # Utility options
    util_group = parser.add_argument_group('Utility Options')
    util_group.add_argument("--version", action="store_true", help="Show version information and exit")
    util_group.add_argument("--check-port", action="store_true",
                      help="Check if the specified port is available and exit")

    return parser.parse_args()

def check_port_in_use(host, port):
    """Check if a port is already in use."""
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        # Set socket options to allow reuse
        s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        try:
            s.bind((host, port))
            # Close the socket properly to release the port
            s.close()
            return False
        except socket.error:
            return True

def find_free_port(start_port=8000, max_port=8100):
    """Find a free port to use for the server."""
    import socket
    for port in range(start_port, max_port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            # Set socket options to allow reuse
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', port))
                # Close the socket properly to release the port
                s.close()
                logger.info(f"Found free port: {port}")
                return port
            except socket.error:
                logger.debug(f"Port {port} is already in use")
                continue
    return None

def print_version_info():
    """Print version information."""
    print(f"SBARDS Backend Server v{__version__}")
    print(f"Author: {__author__}")
    print(f"License: {__license__}")
    print(f"Python: {platform.python_version()}")
    print(f"Platform: {platform.platform()}")
    print(f"Current time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """Main function to run the backend server."""
    args = parse_arguments()

    # Handle version flag
    if args.version:
        print_version_info()
        return 0

    # Set up custom log file if specified
    if args.log_file:
        global file_handler
        custom_log_file = os.path.abspath(args.log_file)
        file_handler = RotatingFileHandler(
            custom_log_file,
            maxBytes=5*1024*1024,  # 5 MB
            backupCount=3
        )
        file_handler.setFormatter(formatter)
        logger.handlers[0] = file_handler
        logger.info(f"Using custom log file: {custom_log_file}")

    # Set up logging level
    log_level = getattr(logging, args.log_level.upper())
    logger.setLevel(log_level)

    # Log system information
    logger.info(f"SBARDS Backend Server v{__version__} starting up")
    logger.info(f"Python version: {platform.python_version()}")
    logger.info(f"Platform: {platform.platform()}")

    # Load environment variables from specified .env file
    if args.env_file and os.path.exists(args.env_file):
        dotenv.load_dotenv(args.env_file)
        logger.info(f"Loaded environment variables from {args.env_file}")

    # Set environment variables from command line arguments
    if args.database_url:
        os.environ["DATABASE_URL"] = args.database_url
        logger.info(f"Using database URL from command line: {args.database_url}")

    if args.virustotal_api_key:
        os.environ["VIRUSTOTAL_API_KEY"] = args.virustotal_api_key
        logger.info("Using VirusTotal API key from command line")

    # Handle port check flag
    if args.check_port:
        port_in_use = check_port_in_use(args.host, args.port)
        if port_in_use:
            logger.warning(f"Port {args.port} is already in use.")
            print(f"Port {args.port} is already in use.")
            return 1
        else:
            logger.info(f"Port {args.port} is available.")
            print(f"Port {args.port} is available.")
            return 0

    # Check if port is already in use
    if check_port_in_use(args.host, args.port):
        logger.warning(f"Port {args.port} is already in use. Attempting to find an alternative port.")

        # Try to find a free port
        free_port = find_free_port(args.port + 1)
        if free_port:
            logger.info(f"Found alternative port: {free_port}. Using this port instead.")
            args.port = free_port
        else:
            logger.error(f"Could not find a free port. Please stop any running instances of the server.")
            logger.error(f"Try using a different port with --port option or stop the existing server.")
            return 1

    # Log server start
    logger.info(f"Starting SBARDS Backend Server on {args.host}:{args.port}")

    # Prepare Uvicorn configuration
    uvicorn_config = {
        "app": "app.main_new:app",
        "host": args.host,
        "port": args.port,
        "reload": args.reload,
        "workers": args.workers,
        "log_level": args.log_level.lower()
    }

    # Add SSL configuration if provided
    if args.ssl_keyfile and args.ssl_certfile:
        uvicorn_config["ssl_keyfile"] = args.ssl_keyfile
        uvicorn_config["ssl_certfile"] = args.ssl_certfile
        logger.info(f"Using SSL with key file: {args.ssl_keyfile} and cert file: {args.ssl_certfile}")

    try:
        # Run the server
        logger.info(f"Starting Uvicorn with configuration: {uvicorn_config}")
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        logger.info("Server stopped by user (Ctrl+C)")
        return 0
    except OSError as e:
        if "address already in use" in str(e).lower():
            logger.error(f"Port {args.port} is already in use. The server may already be running.")
            logger.error(f"Try using a different port with --port option or stop the existing server.")
        else:
            logger.error(f"OS Error starting server: {e}")
        return 1
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        return 1

    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user (Ctrl+C)")
        sys.exit(0)
