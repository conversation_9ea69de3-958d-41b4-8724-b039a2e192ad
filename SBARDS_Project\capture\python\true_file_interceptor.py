#!/usr/bin/env python3
"""
True File Interceptor for SBARDS Capture Layer
طبقة الالتقاط الحقيقية لنظام SBARDS

هذا الملف يطبق السيناريو الصحيح حسب التوثيق:
1. اعتراض الملفات قبل حفظها في المسار الأصلي
2. نقل آمن لمنطقة تخزين مؤقتة مشفرة
3. حساب هاش SHA-256 فوري
4. إرسال للتحليل الثابت
5. قرار بناءً على نتيجة التحليل
6. إرجاع للمسار الأصلي أو حجر صحي

المسؤوليات:
✅ اعتراض الملفات قبل الحفظ
✅ النقل الآمن للمنطقة المؤقتة
✅ حساب الهاش الفوري
✅ تعطيل صلاحيات الملف
✅ التكامل مع طبقة التحليل الثابت
✅ إدارة المنطقة المؤقتة الآمنة

غير مسؤول عن:
❌ تحليل التهديدات (عمل Static Analysis)
❌ فحص الأنماط المشبوهة (عمل Static Analysis)
❌ قواعد YARA (عمل Static Analysis)
❌ تحليل المحتوى (عمل Static Analysis)
"""

import os
import sys
import time
import hashlib
import shutil
import tempfile
import threading
import queue
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass
import platform

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.logger import get_global_logger

@dataclass
class FileInterceptionEvent:
    """حدث اعتراض ملف - بيانات أساسية فقط"""
    file_path: str
    original_path: str
    temp_path: str
    file_hash: str
    file_size: int
    file_extension: str
    timestamp: float
    source_info: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """تحويل لقاموس للإرسال لطبقات أخرى"""
        return {
            'file_path': self.file_path,
            'original_path': self.original_path,
            'temp_path': self.temp_path,
            'file_hash': self.file_hash,
            'file_size': self.file_size,
            'file_extension': self.file_extension,
            'timestamp': self.timestamp,
            'source_info': self.source_info
        }

class SecureTemporaryStorage:
    """إدارة المنطقة المؤقتة الآمنة"""

    def __init__(self, base_path: str = "capture/temp_storage"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.logger = get_global_logger().get_layer_logger("capture")

        # إنشاء مجلدات فرعية
        self.incoming_path = self.base_path / "incoming"
        self.processing_path = self.base_path / "processing"
        self.quarantine_path = self.base_path / "quarantine"

        for path in [self.incoming_path, self.processing_path, self.quarantine_path]:
            path.mkdir(exist_ok=True)
            self._secure_directory(path)

    def _secure_directory(self, path: Path):
        """تأمين المجلد بصلاحيات محدودة"""
        try:
            if platform.system() == "Windows":
                # Windows: تطبيق ACLs محدودة
                os.system(f'icacls "{path}" /inheritance:r /grant:r "%USERNAME%":F')
            else:
                # Linux: chmod 700 (مالك فقط)
                os.chmod(path, 0o700)
        except Exception as e:
            self.logger.warning(f"Could not secure directory {path}: {e}")

    def store_file(self, file_data: bytes, original_filename: str) -> Tuple[str, str]:
        """
        تخزين ملف في المنطقة الآمنة

        Returns:
            Tuple[str, str]: (temp_path, file_hash)
        """
        # إنشاء اسم ملف فريد
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        safe_filename = self._sanitize_filename(original_filename)
        temp_filename = f"{timestamp}_{safe_filename}"
        temp_path = self.incoming_path / temp_filename

        # كتابة الملف
        with open(temp_path, 'wb') as f:
            f.write(file_data)

        # تأمين الملف
        self._secure_file(temp_path)

        # حساب الهاش
        file_hash = self._calculate_hash(temp_path)

        self.logger.info(f"File stored securely: {temp_filename} (hash: {file_hash[:16]}...)")
        return str(temp_path), file_hash

    def move_to_processing(self, temp_path: str) -> str:
        """نقل ملف للمعالجة"""
        temp_path = Path(temp_path)
        processing_path = self.processing_path / temp_path.name
        shutil.move(str(temp_path), str(processing_path))
        return str(processing_path)

    def move_to_quarantine(self, temp_path: str) -> str:
        """نقل ملف للحجر الصحي"""
        temp_path = Path(temp_path)
        quarantine_path = self.quarantine_path / temp_path.name
        shutil.move(str(temp_path), str(quarantine_path))
        return str(quarantine_path)

    def restore_to_original(self, temp_path: str, original_path: str) -> bool:
        """إرجاع ملف للمسار الأصلي"""
        try:
            # إنشاء مجلد الوجهة إذا لم يكن موجود
            os.makedirs(os.path.dirname(original_path), exist_ok=True)

            # نسخ الملف
            shutil.copy2(temp_path, original_path)

            # حذف النسخة المؤقتة
            os.remove(temp_path)

            self.logger.info(f"File restored to original path: {original_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to restore file to {original_path}: {e}")
            return False

    def cleanup_temp_file(self, temp_path: str):
        """حذف ملف مؤقت"""
        try:
            if os.path.exists(temp_path):
                os.remove(temp_path)
                self.logger.debug(f"Cleaned up temp file: {temp_path}")
        except Exception as e:
            self.logger.error(f"Failed to cleanup {temp_path}: {e}")

    def _secure_file(self, file_path: Path):
        """تأمين ملف بصلاحيات محدودة"""
        try:
            if platform.system() == "Windows":
                # Windows: قراءة فقط
                os.system(f'attrib +R "{file_path}"')
            else:
                # Linux: chmod 600 (مالك فقط، قراءة وكتابة)
                os.chmod(file_path, 0o600)
        except Exception as e:
            self.logger.warning(f"Could not secure file {file_path}: {e}")

    def _calculate_hash(self, file_path: Path) -> str:
        """حساب هاش SHA-256"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()

    def _sanitize_filename(self, filename: str) -> str:
        """تنظيف اسم الملف"""
        # إزالة مكونات المسار
        filename = os.path.basename(filename)

        # استبدال الأحرف الخطيرة
        dangerous_chars = '<>:"/\\|?*'
        for char in dangerous_chars:
            filename = filename.replace(char, '_')

        # تحديد الطول
        if len(filename) > 100:
            name, ext = os.path.splitext(filename)
            filename = name[:95] + ext

        return filename

class TrueFileInterceptor:
    """
    مُعترض الملفات الحقيقي
    يطبق السيناريو الصحيح حسب التوثيق
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_global_logger().get_layer_logger("capture")

        # إعداد التخزين الآمن
        self.secure_storage = SecureTemporaryStorage(
            config.get("temp_storage_path", "capture/temp_storage")
        )

        # طابور الأحداث
        self.event_queue = queue.Queue()

        # حالة النظام
        self.running = False
        self.worker_thread = None

        # إحصائيات
        self.stats = {
            "files_intercepted": 0,
            "files_processed": 0,
            "files_restored": 0,
            "files_quarantined": 0,
            "start_time": None
        }

        # تكامل مع طبقة التحليل الثابت
        self.static_analysis_callback = None

        self.logger.info("TrueFileInterceptor initialized")

    def set_static_analysis_callback(self, callback):
        """تعيين callback للتكامل مع طبقة التحليل الثابت"""
        self.static_analysis_callback = callback
        self.logger.info("Static analysis callback registered")

    def start(self) -> bool:
        """تشغيل المُعترض"""
        if self.running:
            return True

        try:
            self.running = True
            self.stats["start_time"] = time.time()

            # تشغيل worker thread
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()

            self.logger.info("TrueFileInterceptor started successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to start TrueFileInterceptor: {e}")
            self.running = False
            return False

    def stop(self):
        """إيقاف المُعترض"""
        if not self.running:
            return

        self.running = False

        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)

        self.logger.info("TrueFileInterceptor stopped")

    def intercept_file_data(self, file_data: bytes, original_filename: str,
                           source_info: Dict[str, Any]) -> Optional[FileInterceptionEvent]:
        """
        اعتراض بيانات ملف (السيناريو الصحيح)

        Args:
            file_data: بيانات الملف
            original_filename: اسم الملف الأصلي
            source_info: معلومات المصدر (متصفح، تطبيق، إلخ)

        Returns:
            FileInterceptionEvent أو None
        """
        try:
            self.stats["files_intercepted"] += 1

            # تخزين آمن في المنطقة المؤقتة
            temp_path, file_hash = self.secure_storage.store_file(file_data, original_filename)

            # إنشاء حدث الاعتراض
            event = FileInterceptionEvent(
                file_path=temp_path,
                original_path=source_info.get("intended_path", ""),
                temp_path=temp_path,
                file_hash=file_hash,
                file_size=len(file_data),
                file_extension=os.path.splitext(original_filename)[1].lower(),
                timestamp=time.time(),
                source_info=source_info
            )

            # إضافة للطابور للمعالجة
            self.event_queue.put(event)

            self.logger.info(f"File intercepted: {original_filename} -> {temp_path}")
            return event

        except Exception as e:
            self.logger.error(f"Failed to intercept file {original_filename}: {e}")
            return None

    def _worker_loop(self):
        """حلقة المعالجة الرئيسية"""
        self.logger.info("Worker loop started")

        while self.running:
            try:
                # الحصول على حدث من الطابور
                event = self.event_queue.get(timeout=1.0)
                self._process_event(event)
                self.event_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error in worker loop: {e}")

    def _process_event(self, event: FileInterceptionEvent):
        """معالجة حدث اعتراض ملف"""
        try:
            self.stats["files_processed"] += 1

            # نقل للمعالجة
            processing_path = self.secure_storage.move_to_processing(event.temp_path)
            event.temp_path = processing_path

            # إرسال للتحليل الثابت
            if self.static_analysis_callback:
                analysis_result = self.static_analysis_callback(event.to_dict())
                self._handle_analysis_result(event, analysis_result)
            else:
                # إذا لم يكن هناك تحليل ثابت، اعتبر الملف آمن
                self.logger.warning("No static analysis callback - treating file as safe")
                self._restore_safe_file(event)

        except Exception as e:
            self.logger.error(f"Error processing event {event.file_path}: {e}")
            # في حالة الخطأ، نقل للحجر الصحي
            self._quarantine_file(event, "Processing error")

    def _handle_analysis_result(self, event: FileInterceptionEvent, result: Dict[str, Any]):
        """التعامل مع نتيجة التحليل الثابت"""
        try:
            threat_level = result.get("threat_level", "unknown")

            if threat_level == "safe":
                self._restore_safe_file(event)
            elif threat_level in ["suspicious", "malicious"]:
                self._quarantine_file(event, f"Threat detected: {threat_level}")
            else:
                # غير معروف - احتياط
                self._quarantine_file(event, "Unknown threat level")

        except Exception as e:
            self.logger.error(f"Error handling analysis result: {e}")
            self._quarantine_file(event, "Analysis result error")

    def _restore_safe_file(self, event: FileInterceptionEvent):
        """إرجاع ملف آمن للمسار الأصلي"""
        try:
            if event.original_path:
                success = self.secure_storage.restore_to_original(
                    event.temp_path, event.original_path
                )
                if success:
                    self.stats["files_restored"] += 1
                    self.logger.info(f"Safe file restored: {event.original_path}")
                else:
                    self._quarantine_file(event, "Failed to restore")
            else:
                # لا يوجد مسار أصلي - احتفظ في المنطقة الآمنة
                self.logger.warning(f"No original path for file: {event.file_path}")

        except Exception as e:
            self.logger.error(f"Error restoring safe file: {e}")
            self._quarantine_file(event, "Restore error")

    def _quarantine_file(self, event: FileInterceptionEvent, reason: str):
        """نقل ملف للحجر الصحي"""
        try:
            quarantine_path = self.secure_storage.move_to_quarantine(event.temp_path)
            self.stats["files_quarantined"] += 1

            self.logger.warning(f"File quarantined: {event.file_path} - Reason: {reason}")

            # إشعار المستخدم/النظام
            self._notify_quarantine(event, reason, quarantine_path)

        except Exception as e:
            self.logger.error(f"Error quarantining file: {e}")

    def _notify_quarantine(self, event: FileInterceptionEvent, reason: str, quarantine_path: str):
        """إشعار بالحجر الصحي"""
        notification = {
            "type": "quarantine",
            "original_filename": os.path.basename(event.original_path),
            "file_hash": event.file_hash,
            "reason": reason,
            "quarantine_path": quarantine_path,
            "timestamp": datetime.now().isoformat()
        }

        # يمكن إضافة إرسال إشعارات هنا (email, webhook, etc.)
        self.logger.info(f"Quarantine notification: {notification}")

    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات"""
        stats = self.stats.copy()
        if stats["start_time"]:
            stats["runtime_seconds"] = time.time() - stats["start_time"]
        return stats

    def cleanup_old_files(self, max_age_hours: int = 24):
        """تنظيف الملفات القديمة"""
        try:
            cutoff_time = time.time() - (max_age_hours * 3600)

            for directory in [self.secure_storage.incoming_path,
                            self.secure_storage.processing_path]:
                for file_path in directory.glob("*"):
                    if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        self.logger.debug(f"Cleaned up old file: {file_path}")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

# مثال للاستخدام
if __name__ == "__main__":
    # تكوين اختبار
    config = {
        "temp_storage_path": "capture/temp_storage"
    }

    # إنشاء المُعترض
    interceptor = TrueFileInterceptor(config)

    # تعيين callback وهمي للتحليل الثابت
    def mock_static_analysis(file_info: Dict[str, Any]) -> Dict[str, Any]:
        print(f"Mock analysis for: {file_info['file_path']}")
        # محاكاة تحليل - اعتبار جميع الملفات آمنة
        return {"threat_level": "safe", "details": "Mock analysis - safe"}

    interceptor.set_static_analysis_callback(mock_static_analysis)

    # تشغيل المُعترض
    if interceptor.start():
        print("TrueFileInterceptor started successfully")

        # اختبار اعتراض ملف
        test_data = b"This is a test file content"
        test_filename = "test_file.txt"
        test_source = {
            "source": "browser",
            "browser": "Chrome",
            "intended_path": "/tmp/test_file.txt"
        }

        event = interceptor.intercept_file_data(test_data, test_filename, test_source)
        if event:
            print(f"File intercepted successfully: {event.file_hash}")

        # انتظار المعالجة
        time.sleep(2)

        # عرض الإحصائيات
        stats = interceptor.get_statistics()
        print(f"Statistics: {stats}")

        # إيقاف المُعترض
        interceptor.stop()
    else:
        print("Failed to start TrueFileInterceptor")
