/*
    Enhanced YARA Rules for SBARDS - Migrated from Original Project
    
    These are enhanced rules from the original SBARDSProject,
    optimized and reorganized for better performance and accuracy.
    
    Migration: SBARDSProject/rules/enhanced_rules.yar → SBARDS_Project/static_analysis/yara_rules/custom/enhanced_rules.yar
*/

import "pe"
import "hash"
import "math"

rule Ransomware_Encryption_Operations_Enhanced {
    meta:
        description = "Detects common ransomware encryption operations - enhanced version"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "critical"
        version = "2.0"
        migrated_from = "SBARDSProject/rules/enhanced_rules.yar"

    strings:
        // Encryption API calls
        $enc_api1 = "CryptEncrypt" ascii wide
        $enc_api2 = "CryptGenKey" ascii wide
        $enc_api3 = "CryptDeriveKey" ascii wide
        $enc_api4 = "CryptCreateHash" ascii wide
        $enc_api5 = "EVP_EncryptInit" ascii wide
        $enc_api6 = "EVP_EncryptUpdate" ascii wide
        $enc_api7 = "CryptAcquireContext" ascii wide
        $enc_api8 = "CryptImportKey" ascii wide
        
        // Encryption algorithms
        $enc_alg1 = "AES" ascii wide
        $enc_alg2 = "RSA" ascii wide
        $enc_alg3 = "Rijndael" ascii wide
        $enc_alg4 = "Twofish" ascii wide
        $enc_alg5 = "Blowfish" ascii wide
        $enc_alg6 = "ChaCha20" ascii wide
        $enc_alg7 = "Salsa20" ascii wide
        
        // File operations
        $file_op1 = "CreateFile" ascii wide
        $file_op2 = "ReadFile" ascii wide
        $file_op3 = "WriteFile" ascii wide
        $file_op4 = "DeleteFile" ascii wide
        $file_op5 = "MoveFile" ascii wide
        $file_op6 = "FindFirstFile" ascii wide
        $file_op7 = "FindNextFile" ascii wide
        $file_op8 = "SetFileAttributes" ascii wide

    condition:
        (2 of ($enc_api*)) and (1 of ($enc_alg*)) and (3 of ($file_op*)) and
        filesize < 15MB
}

rule Ransomware_File_Extensions_Enhanced {
    meta:
        description = "Detects common ransomware file extensions - enhanced version"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "high"
        version = "2.0"
        migrated_from = "SBARDSProject/rules/enhanced_rules.yar"

    strings:
        // Classic ransomware extensions
        $ext1 = ".locked" nocase
        $ext2 = ".crypt" nocase
        $ext3 = ".encrypted" nocase
        $ext4 = ".crypted" nocase
        $ext5 = ".cerber" nocase
        $ext6 = ".locky" nocase
        $ext7 = ".zepto" nocase
        $ext8 = ".thor" nocase
        $ext9 = ".aesir" nocase
        $ext10 = ".zzzzz" nocase
        $ext11 = ".osiris" nocase
        $ext12 = ".cryptolocker" nocase
        $ext13 = ".cryptowall" nocase
        $ext14 = ".crypz" nocase
        $ext15 = ".cryp1" nocase
        
        // Modern ransomware extensions
        $ext16 = ".ryuk" nocase
        $ext17 = ".maze" nocase
        $ext18 = ".revil" nocase
        $ext19 = ".sodinokibi" nocase
        $ext20 = ".darkside" nocase
        $ext21 = ".conti" nocase
        $ext22 = ".babuk" nocase
        $ext23 = ".avaddon" nocase
        $ext24 = ".ragnar" nocase
        $ext25 = ".egregor" nocase

    condition:
        any of them
}

rule Ransomware_Bitcoin_Addresses_Enhanced {
    meta:
        description = "Detects Bitcoin addresses commonly used in ransomware - enhanced version"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "medium"
        version = "2.0"
        migrated_from = "SBARDSProject/rules/enhanced_rules.yar"

    strings:
        // Bitcoin address regex pattern (enhanced)
        $btc_addr = /[13][a-km-zA-HJ-NP-Z1-9]{25,34}/ ascii wide
        $btc_addr_bc1 = /bc1[a-z0-9]{39,59}/ ascii wide  // Bech32 format
        
        // Common ransom words near Bitcoin addresses
        $ransom1 = "payment" nocase ascii wide
        $ransom2 = "bitcoin" nocase ascii wide
        $ransom3 = "wallet" nocase ascii wide
        $ransom4 = "address" nocase ascii wide
        $ransom5 = "transfer" nocase ascii wide
        $ransom6 = "send" nocase ascii wide
        $ransom7 = "money" nocase ascii wide
        $ransom8 = "pay" nocase ascii wide
        $ransom9 = "BTC" nocase ascii wide
        $ransom10 = "cryptocurrency" nocase ascii wide
        $ransom11 = "crypto" nocase ascii wide
        $ransom12 = "coin" nocase ascii wide

    condition:
        ($btc_addr or $btc_addr_bc1) and 2 of ($ransom*)
}

rule Advanced_Ransomware_Behavior_Enhanced {
    meta:
        description = "Detects advanced ransomware behavior patterns"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "critical"
        version = "2.0"

    strings:
        // Shadow copy deletion
        $shadow1 = "vssadmin delete shadows" nocase ascii wide
        $shadow2 = "wmic shadowcopy delete" nocase ascii wide
        $shadow3 = "bcdedit /set {default} recoveryenabled No" nocase ascii wide
        $shadow4 = "bcdedit /set {default} bootstatuspolicy ignoreallfailures" nocase ascii wide
        
        // Service manipulation
        $service1 = "net stop" ascii wide
        $service2 = "sc stop" ascii wide
        $service3 = "taskkill" ascii wide
        $service4 = "wmic process" ascii wide
        
        // Network discovery
        $network1 = "net view" ascii wide
        $network2 = "ping" ascii wide
        $network3 = "arp -a" ascii wide
        $network4 = "ipconfig" ascii wide
        
        // Persistence mechanisms
        $persist1 = "schtasks" ascii wide
        $persist2 = "reg add" ascii wide
        $persist3 = "startup" nocase ascii wide
        $persist4 = "autorun" nocase ascii wide
        
        // Encryption indicators
        $encrypt1 = "encrypt" nocase ascii wide
        $encrypt2 = "decrypt" nocase ascii wide
        $encrypt3 = "cipher" nocase ascii wide
        $encrypt4 = "key" nocase ascii wide

    condition:
        (
            (2 of ($shadow*)) or
            (2 of ($service*) and 1 of ($network*)) or
            (1 of ($persist*) and 2 of ($encrypt*)) or
            (1 of ($shadow*) and 1 of ($service*) and 1 of ($encrypt*))
        ) and
        filesize < 20MB
}

rule Ransomware_Note_Patterns_Enhanced {
    meta:
        description = "Detects common ransomware note patterns"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "high"
        version = "2.0"

    strings:
        // Common ransom note openings
        $note1 = "Your files have been encrypted" nocase ascii wide
        $note2 = "All your files are encrypted" nocase ascii wide
        $note3 = "Oops, your important files are encrypted" nocase ascii wide
        $note4 = "What happened to my computer?" nocase ascii wide
        $note5 = "Your network has been penetrated" nocase ascii wide
        
        // Payment instructions
        $pay1 = "To decrypt your files" nocase ascii wide
        $pay2 = "Follow these steps" nocase ascii wide
        $pay3 = "Download Tor browser" nocase ascii wide
        $pay4 = "Visit our website" nocase ascii wide
        $pay5 = "Contact us via email" nocase ascii wide
        
        // Threats and warnings
        $threat1 = "Do not try to decrypt" nocase ascii wide
        $threat2 = "Do not contact police" nocase ascii wide
        $threat3 = "Time is running out" nocase ascii wide
        $threat4 = "Files will be deleted" nocase ascii wide
        $threat5 = "Price will increase" nocase ascii wide
        
        // Technical details
        $tech1 = "RSA-2048" ascii wide
        $tech2 = "AES-256" ascii wide
        $tech3 = "private key" nocase ascii wide
        $tech4 = "public key" nocase ascii wide
        $tech5 = "decryption key" nocase ascii wide

    condition:
        (
            (1 of ($note*) and 1 of ($pay*)) or
            (1 of ($note*) and 1 of ($threat*)) or
            (1 of ($pay*) and 1 of ($tech*)) or
            (2 of ($note*))
        ) and
        filesize < 5MB
}

rule Crypto_Mining_Malware_Enhanced {
    meta:
        description = "Detects cryptocurrency mining malware"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "malware"
        severity = "medium"
        version = "2.0"

    strings:
        // Mining pool addresses
        $pool1 = "stratum+tcp://" ascii wide
        $pool2 = "pool." ascii wide
        $pool3 = "mining" nocase ascii wide
        $pool4 = "miner" nocase ascii wide
        
        // Cryptocurrency types
        $crypto1 = "monero" nocase ascii wide
        $crypto2 = "bitcoin" nocase ascii wide
        $crypto3 = "ethereum" nocase ascii wide
        $crypto4 = "zcash" nocase ascii wide
        $crypto5 = "litecoin" nocase ascii wide
        
        // Mining software indicators
        $soft1 = "xmrig" nocase ascii wide
        $soft2 = "cpuminer" nocase ascii wide
        $soft3 = "cgminer" nocase ascii wide
        $soft4 = "bfgminer" nocase ascii wide
        $soft5 = "claymore" nocase ascii wide
        
        // Wallet addresses
        $wallet1 = /4[0-9AB][1-9A-HJ-NP-Za-km-z]{93}/ ascii wide  // Monero
        $wallet2 = /0x[a-fA-F0-9]{40}/ ascii wide  // Ethereum
        
        // Performance indicators
        $perf1 = "hashrate" nocase ascii wide
        $perf2 = "difficulty" nocase ascii wide
        $perf3 = "shares" nocase ascii wide
        $perf4 = "accepted" nocase ascii wide

    condition:
        (
            (1 of ($pool*) and 1 of ($crypto*)) or
            (1 of ($soft*) and 1 of ($crypto*)) or
            (1 of ($wallet*) and 1 of ($perf*)) or
            (2 of ($soft*))
        ) and
        filesize < 25MB
}
