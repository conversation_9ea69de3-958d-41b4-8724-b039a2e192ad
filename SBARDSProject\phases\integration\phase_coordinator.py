"""
Phase Coordinator for SBARDS

This module provides coordination between different phases of the SBARDS project.
"""

import os
import time
import logging
import threading
import json
import queue
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

class SharedState:
    """
    Shared state between phases.

    This class provides a thread-safe way to share state between different phases.
    """

    def __init__(self):
        """Initialize shared state."""
        self._lock = threading.RLock()
        self._prescanning_results = {}
        self._monitoring_alerts = []
        self._processed_alerts = set()
        self._fast_track_requests = []
        self._processed_requests = set()
        self._shared_data = {}

    def add_prescanning_result(self, result: Dict[str, Any]) -> None:
        """
        Add a prescanning result.

        Args:
            result (Dict[str, Any]): Prescanning result
        """
        with self._lock:
            scan_id = result.get("scan_id")
            if scan_id:
                self._prescanning_results[scan_id] = result

    def get_prescanning_result(self, scan_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a prescanning result.

        Args:
            scan_id (str): Scan ID

        Returns:
            Optional[Dict[str, Any]]: Prescanning result or None if not found
        """
        with self._lock:
            return self._prescanning_results.get(scan_id)

    def get_all_prescanning_results(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all prescanning results.

        Returns:
            Dict[str, Dict[str, Any]]: All prescanning results
        """
        with self._lock:
            return self._prescanning_results.copy()

    def add_monitoring_alert(self, alert: Dict[str, Any]) -> None:
        """
        Add a monitoring alert.

        Args:
            alert (Dict[str, Any]): Monitoring alert
        """
        with self._lock:
            self._monitoring_alerts.append(alert)

    def get_new_monitoring_alerts(self) -> List[Dict[str, Any]]:
        """
        Get new monitoring alerts.

        Returns:
            List[Dict[str, Any]]: New monitoring alerts
        """
        with self._lock:
            new_alerts = []
            for alert in self._monitoring_alerts:
                alert_id = alert.get("id")
                if alert_id and alert_id not in self._processed_alerts:
                    new_alerts.append(alert)
            return new_alerts

    def mark_monitoring_alerts_as_processed(self) -> None:
        """Mark all monitoring alerts as processed."""
        with self._lock:
            for alert in self._monitoring_alerts:
                alert_id = alert.get("id")
                if alert_id:
                    self._processed_alerts.add(alert_id)

    def add_fast_track_request(self, request: Dict[str, Any]) -> None:
        """
        Add a fast-track request.

        Args:
            request (Dict[str, Any]): Fast-track request
        """
        with self._lock:
            self._fast_track_requests.append(request)

    def get_fast_track_requests(self) -> List[Dict[str, Any]]:
        """
        Get fast-track requests.

        Returns:
            List[Dict[str, Any]]: Fast-track requests
        """
        with self._lock:
            new_requests = []
            for request in self._fast_track_requests:
                request_id = request.get("id")
                if request_id and request_id not in self._processed_requests:
                    new_requests.append(request)
            return new_requests

    def mark_fast_track_requests_as_processed(self) -> None:
        """Mark all fast-track requests as processed."""
        with self._lock:
            for request in self._fast_track_requests:
                request_id = request.get("id")
                if request_id:
                    self._processed_requests.add(request_id)

    def set_shared_data(self, key: str, value: Any) -> None:
        """
        Set shared data.

        Args:
            key (str): Data key
            value (Any): Data value
        """
        with self._lock:
            self._shared_data[key] = value

    def get_shared_data(self, key: str, default: Any = None) -> Any:
        """
        Get shared data.

        Args:
            key (str): Data key
            default (Any, optional): Default value if key not found

        Returns:
            Any: Data value or default if key not found
        """
        with self._lock:
            return self._shared_data.get(key, default)

class PhaseCoordinator:
    """
    Coordinates different phases of the SBARDS project.

    This class provides mechanisms for:
    1. Coordinating activities between phases
    2. Sharing information between phases
    3. Managing shared resources
    4. Optimizing performance across phases
    """

    def __init__(self, config_path: str = "config.json"):
        """
        Initialize phase coordinator.

        Args:
            config_path (str): Path to the configuration file
        """
        # Load configuration
        from core.config import ConfigLoader
        config_loader = ConfigLoader(config_path)
        self.config = config_loader.get_config()
        self.integration_config = self.config.get("integration", {})
        self.enabled = self.integration_config.get("enabled", True)
        self.coordination_interval = self.integration_config.get("coordination_interval_seconds", 1.0)

        # Set up logging
        self.logger = logging.getLogger("PhaseCoordinator")

        # Initialize shared state
        self.shared_state = SharedState()

        # Initialize phase references
        self.prescanning_orchestrator = None
        self.monitoring_manager = None

        # Initialize coordination thread
        self.coordination_thread = None
        self.stop_event = threading.Event()
        self.is_running = False

        # Initialize metrics
        self.metrics = {
            "coordination_calls": 0,
            "shared_detections": 0,
            "fast_track_requests": 0,
            "response_time_ms": []
        }

        # Initialize task queue
        self.task_queue = queue.PriorityQueue()
        self.task_thread = None

    def set_prescanning_orchestrator(self, orchestrator) -> None:
        """
        Set the prescanning orchestrator.

        Args:
            orchestrator: Prescanning orchestrator
        """
        self.prescanning_orchestrator = orchestrator

    def set_monitoring_manager(self, manager) -> None:
        """
        Set the monitoring manager.

        Args:
            manager: Monitoring manager
        """
        self.monitoring_manager = manager

    def start_coordination(self) -> bool:
        """
        Start coordination between phases.

        Returns:
            bool: True if started successfully, False otherwise
        """
        if not self.enabled:
            self.logger.warning("Phase coordination is not enabled in configuration")
            return False

        if self.is_running:
            self.logger.warning("Phase coordination is already running")
            return True

        self.logger.info("Starting phase coordination")
        self.stop_event.clear()

        # Start coordination thread
        self.coordination_thread = threading.Thread(
            target=self._coordination_loop,
            daemon=True
        )
        self.coordination_thread.start()

        # Start task thread
        self.task_thread = threading.Thread(
            target=self._task_loop,
            daemon=True
        )
        self.task_thread.start()

        self.is_running = True
        self.logger.info("Phase coordination started")
        return True

    def stop_coordination(self) -> bool:
        """
        Stop coordination between phases.

        Returns:
            bool: True if stopped successfully, False otherwise
        """
        if not self.is_running:
            self.logger.warning("Phase coordination is not running")
            return True

        self.logger.info("Stopping phase coordination")
        self.stop_event.set()

        # Wait for threads to stop
        if self.coordination_thread:
            self.coordination_thread.join(timeout=5.0)

        if self.task_thread:
            self.task_thread.join(timeout=5.0)

        self.is_running = False
        self.logger.info("Phase coordination stopped")
        return True

    def _coordination_loop(self) -> None:
        """Coordination loop."""
        check_interval = max(0.1, self.coordination_interval)

        while not self.stop_event.is_set():
            try:
                start_time = time.time()

                # Perform coordination activities
                self._sync_detection_data()
                self._check_fast_track_requests()
                self._update_priorities()

                # Update metrics
                self.metrics["coordination_calls"] += 1
                elapsed_ms = (time.time() - start_time) * 1000
                self.metrics["response_time_ms"].append(elapsed_ms)

                # Keep only the last 100 response times
                if len(self.metrics["response_time_ms"]) > 100:
                    self.metrics["response_time_ms"] = self.metrics["response_time_ms"][-100:]

                # Wait for next coordination cycle
                self.stop_event.wait(check_interval)

            except Exception as e:
                self.logger.error(f"Error during coordination: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)

    def _task_loop(self) -> None:
        """Task loop."""
        while not self.stop_event.is_set():
            try:
                # Get task from queue with timeout
                try:
                    priority, task, args, kwargs = self.task_queue.get(timeout=0.5)

                    # Execute task
                    try:
                        task(*args, **kwargs)
                    except Exception as e:
                        self.logger.error(f"Error executing task: {e}")

                    # Mark task as done
                    self.task_queue.task_done()

                except queue.Empty:
                    # No tasks in queue
                    pass

            except Exception as e:
                self.logger.error(f"Error in task loop: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)

    def _sync_detection_data(self) -> None:
        """Synchronize detection data between phases."""
        if not self.prescanning_orchestrator or not self.monitoring_manager:
            return

        # Get new detections from prescanning phase
        prescanning_results = self.prescanning_orchestrator.get_recent_results()

        # Share with monitoring phase
        if prescanning_results:
            for result in prescanning_results:
                self.shared_state.add_prescanning_result(result)
                self._notify_monitoring_phase(result)

            self.metrics["shared_detections"] += len(prescanning_results)

        # Get new alerts from monitoring phase
        monitoring_alerts = self.monitoring_manager.get_recent_alerts()

        # Share with prescanning phase
        if monitoring_alerts:
            for alert in monitoring_alerts:
                self.shared_state.add_monitoring_alert(alert)

            self.shared_state.mark_monitoring_alerts_as_processed()
            self._notify_prescanning_phase(monitoring_alerts)

    def _check_fast_track_requests(self) -> None:
        """Check for fast-track requests."""
        fast_track_requests = self.shared_state.get_fast_track_requests()

        if fast_track_requests:
            self.metrics["fast_track_requests"] += len(fast_track_requests)
            self._process_fast_track_requests(fast_track_requests)
            self.shared_state.mark_fast_track_requests_as_processed()

    def _update_priorities(self) -> None:
        """Update priorities based on shared information."""
        # This would implement priority adjustment logic based on
        # information from both phases
        pass

    def _notify_monitoring_phase(self, detection: Dict[str, Any]) -> None:
        """
        Notify monitoring phase of a detection.

        Args:
            detection (Dict[str, Any]): Detection data
        """
        if self.monitoring_manager:
            # Add task to queue
            self.task_queue.put((
                1,  # Priority (lower is higher)
                self.monitoring_manager.handle_detection,
                (detection,),
                {}
            ))

    def _notify_prescanning_phase(self, alerts: List[Dict[str, Any]]) -> None:
        """
        Notify prescanning phase of alerts.

        Args:
            alerts (List[Dict[str, Any]]): Alert data
        """
        if self.prescanning_orchestrator:
            # Add task to queue
            self.task_queue.put((
                1,  # Priority (lower is higher)
                self.prescanning_orchestrator.handle_alerts,
                (alerts,),
                {}
            ))

    def _process_fast_track_requests(self, requests: List[Dict[str, Any]]) -> None:
        """
        Process fast-track requests.

        Args:
            requests (List[Dict[str, Any]]): Fast-track requests
        """
        for request in requests:
            request_type = request.get("type")

            if request_type == "file":
                # Process file request
                self._process_file_request(request)
            elif request_type == "process":
                # Process process request
                self._process_process_request(request)

    def _process_file_request(self, request: Dict[str, Any]) -> None:
        """
        Process a file fast-track request.

        Args:
            request (Dict[str, Any]): Fast-track request
        """
        file_path = request.get("path")
        if not file_path or not os.path.exists(file_path):
            return

        # Add task to queue with high priority
        if self.prescanning_orchestrator:
            self.task_queue.put((
                0,  # Priority (lower is higher)
                self.prescanning_orchestrator.scan_file,
                (file_path,),
                {"priority": request.get("priority", 10)}
            ))

    def _process_process_request(self, request: Dict[str, Any]) -> None:
        """
        Process a process fast-track request.

        Args:
            request (Dict[str, Any]): Fast-track request
        """
        process_id = request.get("process_id")
        if not process_id:
            return

        # Add task to queue with high priority
        if self.monitoring_manager:
            self.task_queue.put((
                0,  # Priority (lower is higher)
                self.monitoring_manager.monitor_process,
                (process_id,),
                {"priority": request.get("priority", 10)}
            ))

    def add_task(self, task: Callable, args: tuple = (), kwargs: dict = {}, priority: int = 5) -> None:
        """
        Add a task to the task queue.

        Args:
            task (Callable): Task function
            args (tuple, optional): Task arguments
            kwargs (dict, optional): Task keyword arguments
            priority (int, optional): Task priority (lower is higher)
        """
        self.task_queue.put((priority, task, args, kwargs))

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get coordination metrics.

        Returns:
            Dict[str, Any]: Metrics dictionary
        """
        metrics = self.metrics.copy()

        # Calculate average response time
        if metrics["response_time_ms"]:
            metrics["avg_response_time_ms"] = sum(metrics["response_time_ms"]) / len(metrics["response_time_ms"])
        else:
            metrics["avg_response_time_ms"] = 0.0

        return metrics

def notify_error(error_message, phase):
    # Enhanced error notification including phase info and timestamp
    from datetime import datetime
    notification = {
        "phase": phase,
        "error": error_message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }
    # For example, this could be sent to a monitoring service or logged
    from scanner_core.utils import logger
    logger.error("Error in phase", context=notification)

def coordinate_phases():
    # ...existing code...
    try:
        # ...pre-scanning, monitoring, integration logic...
        pass
    except Exception as e:
        notify_error(str(e), phase="integration")
