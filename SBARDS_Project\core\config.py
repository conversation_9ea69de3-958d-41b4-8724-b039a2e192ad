"""
Enhanced Configuration Loader for SBARDS New Architecture

This module provides advanced configuration loading and validation for the new SBARDS project.
Supports multi-layer architecture with C++/Python integration.
"""

import os
import json
import platform
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

class ConfigLoader:
    """
    Enhanced Configuration loader for the new SBARDS Project.
    Loads and validates configuration for multi-layer architecture.
    """

    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the enhanced configuration loader.

        Args:
            config_path (str): Path to the configuration file
        """
        self.config_path = os.path.abspath(config_path)
        self.config = self._load_config()
        self._validate_config()
        self._setup_platform_specifics()
        self._setup_layer_configs()
        self._runtime_checks()

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from the JSON file.

        Returns:
            dict: Configuration dictionary
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            logging.error(f"Configuration file not found: {self.config_path}")
            return self._create_default_config()
        except json.JSONDecodeError:
            logging.error(f"Invalid JSON in configuration file: {self.config_path}")
            return self._create_default_config()

    def _create_default_config(self) -> Dict[str, Any]:
        """
        Create a default configuration for new architecture.

        Returns:
            dict: Default configuration dictionary
        """
        default_config = {
            # Core Configuration
            "core": {
                "project_name": "SBARDS",
                "version": "2.0.0",
                "debug_mode": False,
                "log_level": "info"
            },

            # Capture Layer Configuration
            "capture": {
                "enabled": True,
                "target_directories": ["samples", "Downloads"],
                "recursive": True,
                "max_depth": 5,
                "exclude_dirs": [".git", "__pycache__", "node_modules"],
                "exclude_extensions": [".tmp", ".log"],
                "max_file_size_mb": 100,
                "temp_storage_path": "capture/temp_storage",
                "cpp_monitor_enabled": True,
                "python_interceptor_enabled": True,
                "redis_queue_enabled": False
            },

            # Static Analysis Layer Configuration
            "static_analysis": {
                "enabled": True,
                "cpp_analyzers": {
                    "signature_checker": True,
                    "permission_analyzer": True,
                    "entropy_checker": True,
                    "hash_generator": True
                },
                "python_analyzers": {
                    "yara_scanner": True,
                    "virus_total": False,
                    "report_generator": True
                },
                "yara_rules": {
                    "custom_enabled": True,
                    "malware_enabled": True,
                    "ransomware_enabled": True,
                    "permissions_enabled": True,
                    "rule_paths": [
                        "static_analysis/yara_rules/custom/",
                        "static_analysis/yara_rules/malware/",
                        "static_analysis/yara_rules/ransomware/",
                        "static_analysis/yara_rules/permissions/"
                    ]
                },
                "hash_algorithms": ["sha256", "sha512", "md5", "ssdeep"],
                "entropy_threshold": 7.5,
                "parallel_processing": True,
                "max_threads": 4
            },

            # Dynamic Analysis Layer Configuration
            "dynamic_analysis": {
                "enabled": False,
                "sandbox_enabled": False,
                "honeypot_enabled": False,
                "ml_analysis_enabled": False,
                "timeout_seconds": 300
            },

            # Response Layer Configuration
            "response": {
                "enabled": True,
                "quarantine_enabled": True,
                "alert_system_enabled": True,
                "auto_response": False,
                "quarantine_path": "quarantine/"
            },

            # External Integration Configuration
            "external_integration": {
                "enabled": False,
                "virus_total": {
                    "enabled": False,
                    "api_key": "",
                    "rate_limit": 4,
                    "timeout_seconds": 30
                },
                "blockchain": {
                    "enabled": False,
                    "network": "hyperledger",
                    "fabric_config": {
                        "network_name": "sbards-network",
                        "channel_name": "sbards-channel",
                        "chaincode_name": "sbards-chaincode",
                        "peer_endpoint": "localhost:7051",
                        "orderer_endpoint": "localhost:7050"
                    }
                },
                "threat_intel": {
                    "enabled": False,
                    "sources": ["virustotal", "alienvault_otx", "misp"],
                    "update_interval_hours": 24,
                    "cache_enabled": True
                },
                "mongodb": {
                    "enabled": False,
                    "host": "localhost",
                    "port": 27017,
                    "database": "sbards_security",
                    "username": "",
                    "password": "",
                    "ssl_enabled": False
                }
            },

            # Memory Protection Configuration
            "memory_protection": {
                "enabled": False,
                "disk_encryption": False,
                "cold_boot_protection": False
            },

            # Monitoring Configuration
            "monitoring": {
                "enabled": True,
                "system_monitor": True,
                "threat_tracker": True,
                "audit_logger": True,
                "event_correlator": True,
                "check_interval_seconds": 1.0,
                "alert_threshold": 0.7
            },

            # Redis Configuration
            "redis": {
                "enabled": False,
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": "",
                "ssl": False,
                "connection_pool_size": 10,
                "timeout_seconds": 5
            },

            # API Configuration
            "api": {
                "host": "127.0.0.1",
                "port": 8000,
                "enable_cors": True,
                "allowed_origins": ["*"],
                "enable_docs": True,
                "enable_websockets": True,
                "max_upload_size_mb": 100
            },

            # UI Configuration
            "ui": {
                "dashboard_enabled": True,
                "cli_enabled": True,
                "gui_enabled": False
            },

            # Data Configuration
            "data": {
                "local_db_path": "data/virus_hashes/local_db/",
                "backup_enabled": True,
                "backup_path": "data/backups/",
                "whitelist_enabled": True
            },

            # Security Configuration
            "security": {
                "integrity_check": True,
                "threat_intel": True,
                "privacy_handler": True,
                "encryption_enabled": False
            },

            # Performance Configuration
            "performance": {
                "threads": 4,
                "batch_size": 20,
                "timeout_seconds": 30,
                "adaptive_threading": True,
                "memory_limit_mb": 1024,
                "cpu_limit_percent": 80
            }
        }

        # Save the default configuration
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=4)

        return default_config

    def _validate_config(self) -> None:
        """
        Validate the configuration for new architecture.
        """
        # Ensure required sections exist
        required_sections = [
            "core", "capture", "static_analysis", "dynamic_analysis",
            "response", "external_integration", "memory_protection",
            "monitoring", "api", "ui", "data", "security", "performance"
        ]

        for section in required_sections:
            if section not in self.config:
                logging.warning(f"Missing configuration section: {section}")
                self.config[section] = self._create_default_config()[section]

        # Validate paths
        self._validate_paths()

    def _validate_paths(self) -> None:
        """
        Validate and create necessary paths.
        """
        paths_to_create = [
            self.config["capture"]["temp_storage_path"],
            self.config["response"]["quarantine_path"],
            self.config["data"]["local_db_path"],
            self.config["data"]["backup_path"],
            "logs/",
            "output/"
        ]

        for path in paths_to_create:
            os.makedirs(path, exist_ok=True)

    def _setup_platform_specifics(self) -> None:
        """
        Set up platform-specific configuration for new architecture.
        """
        system = platform.system()

        if "platform_specific" not in self.config:
            self.config["platform_specific"] = {}

        if system == "Windows":
            self.config["platform_specific"]["windows"] = {
                "cpp_compiler": "msvc",
                "shared_lib_extension": ".dll",
                "executable_extension": ".exe",
                "path_separator": "\\",
                "use_defender": True,
                "enable_etw": True
            }
        elif system == "Linux":
            self.config["platform_specific"]["linux"] = {
                "cpp_compiler": "gcc",
                "shared_lib_extension": ".so",
                "executable_extension": "",
                "path_separator": "/",
                "use_inotify": True,
                "enable_auditd": True
            }

    def _setup_layer_configs(self) -> None:
        """
        Setup configuration for each layer.
        """
        # Capture layer specific setup
        if self.config["capture"]["enabled"]:
            self._setup_capture_layer()

        # Static analysis layer specific setup
        if self.config["static_analysis"]["enabled"]:
            self._setup_static_analysis_layer()

    def _setup_capture_layer(self) -> None:
        """
        Setup capture layer configuration.
        """
        # Ensure target directories exist
        for target_dir in self.config["capture"]["target_directories"]:
            if not os.path.exists(target_dir):
                logging.warning(f"Target directory does not exist: {target_dir}")
                os.makedirs(target_dir, exist_ok=True)

    def _setup_static_analysis_layer(self) -> None:
        """
        Setup static analysis layer configuration.
        """
        # Ensure YARA rule directories exist
        for rule_path in self.config["static_analysis"]["yara_rules"]["rule_paths"]:
            os.makedirs(rule_path, exist_ok=True)

    def _runtime_checks(self) -> None:
        """
        Perform runtime checks for critical configuration values.
        """
        # Validate log level
        valid_log_levels = ["debug", "info", "warning", "error", "critical"]
        log_level = self.config["core"].get("log_level", "info")
        if log_level not in valid_log_levels:
            raise ValueError(f"Invalid log level: {log_level}. Must be one of {valid_log_levels}")

        # Validate performance settings
        if self.config["performance"]["threads"] < 1:
            raise ValueError("Thread count must be at least 1")

        if self.config["performance"]["memory_limit_mb"] < 256:
            raise ValueError("Memory limit must be at least 256 MB")

    def get_config(self) -> Dict[str, Any]:
        """
        Get the complete configuration.

        Returns:
            dict: Configuration dictionary
        """
        return self.config

    def get_layer_config(self, layer: str) -> Dict[str, Any]:
        """
        Get configuration for a specific layer.

        Args:
            layer (str): Layer name (capture, static_analysis, etc.)

        Returns:
            dict: Layer configuration dictionary
        """
        return self.config.get(layer, {})

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get a specific section of the configuration.

        Args:
            section (str): Section name

        Returns:
            dict: Section configuration dictionary
        """
        return self.config.get(section, {})

    def save_config(self) -> None:
        """
        Save the current configuration to the file.
        """
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=4)

    def update_config(self, section: str, key: str, value: Any) -> None:
        """
        Update a configuration value.

        Args:
            section (str): Configuration section
            key (str): Configuration key
            value (Any): New value
        """
        if section not in self.config:
            self.config[section] = {}

        self.config[section][key] = value
        self.save_config()

# Alias for backward compatibility
ConfigManager = ConfigLoader

class ConfigManager:
    """
    Configuration Manager for SBARDS Project.
    Provides simple interface for configuration management.
    """

    def __init__(self, config_path: str = "config.json"):
        """
        Initialize configuration manager.

        Args:
            config_path (str): Path to configuration file
        """
        self.config_path = config_path
        self.config = {}
        self.loaded = False

    def load_config(self) -> bool:
        """
        Load configuration from file.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            self.loaded = True
            return True
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logging.error(f"Failed to load config: {e}")
            self.config = {}
            self.loaded = False
            return False

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.

        Args:
            key (str): Configuration key (e.g., "core.project_name")
            default (Any): Default value if key not found

        Returns:
            Any: Configuration value or default
        """
        if not self.loaded:
            return default

        keys = key.split('.')
        value = self.config

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.

        Args:
            section (str): Section name

        Returns:
            dict: Section configuration
        """
        if not self.loaded:
            return {}

        return self.config.get(section, {})

    def has_section(self, section: str) -> bool:
        """
        Check if section exists.

        Args:
            section (str): Section name

        Returns:
            bool: True if section exists
        """
        return section in self.config

    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value using dot notation.

        Args:
            key (str): Configuration key (e.g., "core.project_name")
            value (Any): Value to set
        """
        keys = key.split('.')
        config = self.config

        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        # Set the value
        config[keys[-1]] = value

    def save_config(self) -> bool:
        """
        Save configuration to file.

        Returns:
            bool: True if successful
        """
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            logging.error(f"Failed to save config: {e}")
            return False

# Global configuration instance
_global_config = None

def get_config(config_path: str = "config.json") -> Dict[str, Any]:
    """
    Get global configuration instance.

    Args:
        config_path (str): Path to configuration file

    Returns:
        dict: Configuration dictionary
    """
    global _global_config
    if _global_config is None:
        _global_config = ConfigLoader(config_path)
    return _global_config.get_config()

def get_config_loader(config_path: str = "config.json") -> ConfigLoader:
    """
    Get global configuration loader instance.

    Args:
        config_path (str): Path to configuration file

    Returns:
        ConfigLoader: Configuration loader instance
    """
    global _global_config
    if _global_config is None:
        _global_config = ConfigLoader(config_path)
    return _global_config
