2025-05-26 04:43:35,359 - layer.capture - INFO - [true_file_interceptor.py:240] - __init__() - TrueFileInterceptor initialized
2025-05-26 04:43:35,360 - layer.capture - INFO - [integrated_capture_layer.py:64] - __init__() - IntegratedCaptureLayer initialized
2025-05-26 04:43:35,361 - layer.capture - INFO - [true_file_interceptor.py:245] - set_static_analysis_callback() - Static analysis callback registered
2025-05-26 04:43:35,362 - layer.capture - INFO - [integrated_capture_layer.py:70] - set_static_analysis_callback() - Static analysis callback registered
2025-05-26 04:43:35,362 - layer.capture - INFO - [integrated_capture_layer.py:125] - start() - Starting Integrated Capture Layer...
2025-05-26 04:43:35,363 - layer.capture - INFO - [cpp_integration.py:216] - initialize() - <PERSON>ck C++ interceptor initialized
2025-05-26 04:43:35,365 - layer.capture - INFO - [cpp_integration.py:221] - set_python_callback() - Mock Python callback registered
2025-05-26 04:43:35,366 - layer.capture - INFO - [true_file_interceptor.py:322] - _worker_loop() - Worker loop started
2025-05-26 04:43:35,366 - layer.capture - INFO - [true_file_interceptor.py:260] - start() - TrueFileInterceptor started successfully
2025-05-26 04:43:35,367 - layer.capture - INFO - [cpp_integration.py:225] - start() - Mock C++ interceptor started
2025-05-26 04:43:35,368 - layer.capture - INFO - [integrated_capture_layer.py:149] - start() - Integrated Capture Layer started successfully
2025-05-26 04:43:35,596 - layer.capture - INFO - [true_file_interceptor.py:240] - __init__() - TrueFileInterceptor initialized
2025-05-26 04:43:35,597 - layer.capture - INFO - [integrated_capture_layer.py:64] - __init__() - IntegratedCaptureLayer initialized
2025-05-26 04:43:35,598 - layer.capture - INFO - [true_file_interceptor.py:245] - set_static_analysis_callback() - Static analysis callback registered
2025-05-26 04:43:35,599 - layer.capture - INFO - [integrated_capture_layer.py:70] - set_static_analysis_callback() - Static analysis callback registered
2025-05-26 04:43:35,599 - layer.capture - INFO - [integrated_capture_layer.py:125] - start() - Starting Integrated Capture Layer...
2025-05-26 04:43:35,600 - layer.capture - INFO - [cpp_integration.py:216] - initialize() - Mock C++ interceptor initialized
2025-05-26 04:43:35,600 - layer.capture - INFO - [cpp_integration.py:221] - set_python_callback() - Mock Python callback registered
2025-05-26 04:43:35,601 - layer.capture - INFO - [true_file_interceptor.py:322] - _worker_loop() - Worker loop started
2025-05-26 04:43:35,601 - layer.capture - INFO - [true_file_interceptor.py:260] - start() - TrueFileInterceptor started successfully
2025-05-26 04:43:35,602 - layer.capture - INFO - [cpp_integration.py:225] - start() - Mock C++ interceptor started
2025-05-26 04:43:35,603 - layer.capture - INFO - [integrated_capture_layer.py:149] - start() - Integrated Capture Layer started successfully
2025-05-26 04:43:37,368 - layer.capture - INFO - [integrated_capture_layer.py:95] - _cpp_callback() - File intercepted by C++: /tmp/mock_intercepted_file.txt
2025-05-26 04:43:37,427 - layer.capture - INFO - [true_file_interceptor.py:125] - store_file() - File stored securely: 20250526_044337_369110_mock_file.txt (hash: 65c92b167a9717fa...)
2025-05-26 04:43:37,428 - layer.capture - INFO - [true_file_interceptor.py:313] - intercept_file_data() - File intercepted: mock_file.txt -> capture\temp_storage\incoming\20250526_044337_369110_mock_file.txt
2025-05-26 04:43:37,428 - layer.capture - INFO - [integrated_capture_layer.py:110] - _cpp_callback() - File passed to Python processing: 65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699
2025-05-26 04:43:37,433 - layer.capture - ERROR - [true_file_interceptor.py:157] - restore_to_original() - Failed to restore file to /home/<USER>/Downloads/mock_file.txt: [Errno 13] Permission denied: '/home/<USER>/Downloads/mock_file.txt'
2025-05-26 04:43:37,434 - layer.capture - WARNING - [true_file_interceptor.py:402] - _quarantine_file() - File quarantined: capture\temp_storage\incoming\20250526_044337_369110_mock_file.txt - Reason: Failed to restore
2025-05-26 04:43:37,435 - layer.capture - INFO - [true_file_interceptor.py:422] - _notify_quarantine() - Quarantine notification: {'type': 'quarantine', 'original_filename': 'mock_file.txt', 'file_hash': '65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699', 'reason': 'Failed to restore', 'quarantine_path': 'capture\\temp_storage\\quarantine\\20250526_044337_369110_mock_file.txt', 'timestamp': '2025-05-26T04:43:37.435267'}
2025-05-26 04:43:37,603 - layer.capture - INFO - [integrated_capture_layer.py:95] - _cpp_callback() - File intercepted by C++: /tmp/mock_intercepted_file.txt
2025-05-26 04:43:37,666 - layer.capture - INFO - [true_file_interceptor.py:125] - store_file() - File stored securely: 20250526_044337_604075_mock_file.txt (hash: 65c92b167a9717fa...)
2025-05-26 04:43:37,667 - layer.capture - INFO - [true_file_interceptor.py:313] - intercept_file_data() - File intercepted: mock_file.txt -> capture\temp_storage\incoming\20250526_044337_604075_mock_file.txt
2025-05-26 04:43:37,669 - layer.capture - INFO - [integrated_capture_layer.py:110] - _cpp_callback() - File passed to Python processing: 65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699
2025-05-26 04:43:37,673 - layer.capture - ERROR - [true_file_interceptor.py:157] - restore_to_original() - Failed to restore file to /home/<USER>/Downloads/mock_file.txt: [Errno 13] Permission denied: '/home/<USER>/Downloads/mock_file.txt'
2025-05-26 04:43:37,675 - layer.capture - WARNING - [true_file_interceptor.py:402] - _quarantine_file() - File quarantined: capture\temp_storage\incoming\20250526_044337_604075_mock_file.txt - Reason: Failed to restore
2025-05-26 04:43:37,676 - layer.capture - INFO - [true_file_interceptor.py:422] - _notify_quarantine() - Quarantine notification: {'type': 'quarantine', 'original_filename': 'mock_file.txt', 'file_hash': '65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699', 'reason': 'Failed to restore', 'quarantine_path': 'capture\\temp_storage\\quarantine\\20250526_044337_604075_mock_file.txt', 'timestamp': '2025-05-26T04:43:37.676325'}
