"""
Reports service for SBARDS API

This module provides business logic for managing scan reports.
"""

import json
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from core.logger import get_global_logger
from api.db.models import ScanReport, FileResult
from api.schemas.reports import ScanReportCreate, ScanReportResponse

# Configure logging
logger = get_global_logger().get_layer_logger("api.services.reports")


class ReportsService:
    """Service for managing scan reports."""
    
    def __init__(self):
        """Initialize the reports service."""
        self.logger = logger
    
    def create_scan_report(self, db: Session, report: ScanReportCreate) -> ScanReport:
        """
        Create a new scan report.
        
        Args:
            db (Session): Database session.
            report (ScanReportCreate): Scan report data.
            
        Returns:
            ScanReport: Created scan report.
        """
        try:
            self.logger.info(f"Creating scan report with ID: {report.scan_id}")
            
            # Create scan report
            db_report = ScanReport(
                scan_id=report.scan_id,
                scan_path=report.scan_path,
                files_scanned=report.files_scanned,
                threats_found=report.threats_found,
                report_path=report.report_path,
                report_content=report.report_content,
                scan_type=report.scan_type,
                performance_metrics=json.dumps(report.performance_metrics) if report.performance_metrics else None
            )
            db.add(db_report)
            db.commit()
            db.refresh(db_report)
            
            # Add file results
            for file_result in report.file_results:
                db_file_result = FileResult(
                    scan_report_id=db_report.id,
                    file_path=file_result.file_path,
                    file_hash=file_result.file_hash,
                    file_size=file_result.file_size,
                    file_type=file_result.file_type,
                    is_threat=file_result.is_threat,
                    threat_type=file_result.threat_type,
                    threat_level=file_result.threat_level,
                    entropy_score=file_result.entropy_score,
                    signature_info=file_result.signature_info
                )
                db.add(db_file_result)
            
            db.commit()
            self.logger.info(f"Successfully created scan report with ID: {report.scan_id}")
            return db_report
            
        except Exception as e:
            self.logger.error(f"Error creating scan report: {e}")
            db.rollback()
            raise
    
    def get_scan_reports(
        self, 
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        include_content: bool = False,
        scan_type: Optional[str] = None
    ) -> List[ScanReport]:
        """
        Get scan reports with pagination.
        
        Args:
            db (Session): Database session.
            skip (int): Number of records to skip.
            limit (int): Maximum number of records to return.
            include_content (bool): Whether to include report content.
            scan_type (str, optional): Filter by scan type.
            
        Returns:
            List[ScanReport]: List of scan reports.
        """
        try:
            # Query reports ordered by timestamp (newest first)
            query = db.query(ScanReport).order_by(ScanReport.timestamp.desc())
            
            # Filter by scan type if specified
            if scan_type:
                query = query.filter(ScanReport.scan_type == scan_type)
            
            # Apply pagination
            reports = query.offset(skip).limit(limit).all()
            
            # If include_content is False, remove the report_content field
            if not include_content:
                for report in reports:
                    report.report_content = None
            
            self.logger.debug(f"Retrieved {len(reports)} scan reports")
            return reports
            
        except Exception as e:
            self.logger.error(f"Error retrieving scan reports: {e}")
            raise
    
    def get_scan_report(
        self, 
        db: Session, 
        scan_id: str, 
        include_content: bool = True
    ) -> Optional[ScanReport]:
        """
        Get a specific scan report by ID.
        
        Args:
            db (Session): Database session.
            scan_id (str): Scan ID.
            include_content (bool): Whether to include report content.
            
        Returns:
            ScanReport or None: Scan report if found.
        """
        try:
            report = db.query(ScanReport).filter(ScanReport.scan_id == scan_id).first()
            
            if report and not include_content:
                report.report_content = None
            
            if report:
                self.logger.debug(f"Retrieved scan report: {scan_id}")
            else:
                self.logger.warning(f"Scan report not found: {scan_id}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error retrieving scan report {scan_id}: {e}")
            raise
    
    def delete_scan_report(self, db: Session, scan_id: str) -> bool:
        """
        Delete a scan report.
        
        Args:
            db (Session): Database session.
            scan_id (str): Scan ID.
            
        Returns:
            bool: True if deleted successfully.
        """
        try:
            report = db.query(ScanReport).filter(ScanReport.scan_id == scan_id).first()
            
            if report:
                db.delete(report)
                db.commit()
                self.logger.info(f"Deleted scan report: {scan_id}")
                return True
            else:
                self.logger.warning(f"Scan report not found for deletion: {scan_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error deleting scan report {scan_id}: {e}")
            db.rollback()
            raise
    
    def get_reports_count(self, db: Session, scan_type: Optional[str] = None) -> int:
        """
        Get total count of reports.
        
        Args:
            db (Session): Database session.
            scan_type (str, optional): Filter by scan type.
            
        Returns:
            int: Total count of reports.
        """
        try:
            query = db.query(ScanReport)
            
            if scan_type:
                query = query.filter(ScanReport.scan_type == scan_type)
            
            count = query.count()
            self.logger.debug(f"Total reports count: {count}")
            return count
            
        except Exception as e:
            self.logger.error(f"Error getting reports count: {e}")
            raise
