/*
    SBARDS YARA Rules Index
    =======================
    
    This file serves as the main index for all YARA rules used in the SBARDS
    Static Analysis Layer. It includes all rule categories and provides
    centralized rule management.
    
    Categories:
    - Malware Detection Rules
    - Ransomware Detection Rules  
    - Permission Security Rules
    - Custom Detection Rules
    - Basic File Type Rules
    
    Usage:
        yara -r index.yar target_directory/
        
    Author: SBARDS Security Team
    Version: 2.0.0
    Last Updated: 2025-05-25
*/

// Include all malware detection rules
include "malware/generic_malware.yar"
include "malware/trojan_detection.yar"
include "malware/backdoor_detection.yar"
include "malware/rootkit_detection.yar"
include "malware/spyware_detection.yar"
include "malware/adware_detection.yar"
include "malware/packer_detection.yar"
include "malware/obfuscation_detection.yar"

// Include all ransomware detection rules
include "ransomware/crypto_ransomware.yar"
include "ransomware/locker_ransomware.yar"
include "ransomware/ransomware_families.yar"
include "ransomware/encryption_indicators.yar"
include "ransomware/ransom_notes.yar"

// Include permission security rules
include "permissions/dangerous_permissions.yar"
include "permissions/suid_sgid_detection.yar"
include "permissions/world_writable.yar"
include "permissions/system_file_tampering.yar"

// Include custom detection rules
include "custom/suspicious_patterns.yar"
include "custom/network_indicators.yar"
include "custom/file_anomalies.yar"
include "custom/behavioral_indicators.yar"

// Include basic file type rules
include "basic/file_signatures.yar"
include "basic/executable_detection.yar"
include "basic/archive_detection.yar"
include "basic/document_detection.yar"

/*
    Master Rule: SBARDS Comprehensive Scan
    =====================================
    
    This rule combines multiple detection techniques for comprehensive analysis.
    It serves as a high-level indicator for files that require detailed investigation.
*/

rule SBARDS_Comprehensive_Threat_Detection
{
    meta:
        description = "SBARDS comprehensive threat detection rule"
        author = "SBARDS Security Team"
        version = "2.0.0"
        date = "2025-05-25"
        category = "comprehensive"
        severity = "high"
        
    condition:
        // Trigger if any malware family is detected
        any of (Malware_*) or
        
        // Trigger if any ransomware indicators are found
        any of (Ransomware_*) or
        
        // Trigger if dangerous permissions are detected
        any of (Dangerous_Permissions_*) or
        
        // Trigger if suspicious patterns are found
        any of (Suspicious_*) or
        
        // Trigger if multiple weak indicators are present
        (
            any of (Packer_*) and
            any of (Obfuscation_*) and
            filesize > 100KB
        )
}

/*
    High-Priority Detection Rules
    ============================
    
    These rules detect the most critical threats that require immediate attention.
*/

rule SBARDS_Critical_Threat_Indicators
{
    meta:
        description = "Critical threat indicators requiring immediate action"
        author = "SBARDS Security Team"
        version = "2.0.0"
        severity = "critical"
        priority = "immediate"
        
    strings:
        // Known malicious signatures
        $malware_sig1 = { 4D 5A 90 00 03 00 00 00 04 00 00 00 FF FF 00 00 }
        $malware_sig2 = { 50 4B 03 04 14 00 00 00 08 00 }
        
        // Ransomware indicators
        $ransom1 = "Your files have been encrypted" nocase
        $ransom2 = "pay the ransom" nocase
        $ransom3 = "bitcoin" nocase
        $ransom4 = ".locked" nocase
        $ransom5 = ".encrypted" nocase
        
        // Suspicious API calls
        $api1 = "CryptEncrypt" nocase
        $api2 = "CryptDecrypt" nocase
        $api3 = "CreateMutex" nocase
        $api4 = "RegSetValue" nocase
        $api5 = "WriteProcessMemory" nocase
        
        // Network indicators
        $net1 = "http://" nocase
        $net2 = "https://" nocase
        $net3 = "ftp://" nocase
        $net4 = "tcp://" nocase
        
        // Obfuscation indicators
        $obf1 = { 55 8B EC 83 EC ?? 53 56 57 }
        $obf2 = { 60 E8 00 00 00 00 5D }
        $obf3 = { EB ?? ?? ?? ?? ?? ?? ?? ?? ?? EB }
        
    condition:
        // File size checks
        filesize > 1KB and filesize < 50MB and
        
        (
            // Multiple ransomware indicators
            (2 of ($ransom*)) or
            
            // Malicious signatures with suspicious APIs
            (any of ($malware_sig*) and 2 of ($api*)) or
            
            // Network activity with obfuscation
            (any of ($net*) and any of ($obf*)) or
            
            // High concentration of suspicious strings
            (#ransom1 + #ransom2 + #ransom3 + #ransom4 + #ransom5 > 5)
        )
}

/*
    Medium-Priority Detection Rules
    ==============================
    
    These rules detect potentially suspicious files that warrant investigation.
*/

rule SBARDS_Suspicious_Activity_Indicators
{
    meta:
        description = "Suspicious activity indicators for further analysis"
        author = "SBARDS Security Team"
        version = "2.0.0"
        severity = "medium"
        priority = "investigate"
        
    strings:
        // Suspicious strings
        $sus1 = "cmd.exe" nocase
        $sus2 = "powershell" nocase
        $sus3 = "rundll32" nocase
        $sus4 = "regsvr32" nocase
        $sus5 = "schtasks" nocase
        
        // Persistence mechanisms
        $persist1 = "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" nocase
        $persist2 = "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" nocase
        $persist3 = "\\Startup\\" nocase
        
        // File operations
        $file1 = "DeleteFile" nocase
        $file2 = "MoveFile" nocase
        $file3 = "CopyFile" nocase
        $file4 = "CreateFile" nocase
        
    condition:
        filesize > 1KB and
        (
            // Multiple system utilities
            (3 of ($sus*)) or
            
            // Persistence with file operations
            (any of ($persist*) and 2 of ($file*)) or
            
            // High frequency of suspicious calls
            (#sus1 + #sus2 + #sus3 + #sus4 + #sus5 > 10)
        )
}

/*
    File Type Validation Rules
    ==========================
    
    These rules validate file types and detect mismatched extensions.
*/

rule SBARDS_File_Type_Mismatch
{
    meta:
        description = "Detects files with mismatched extensions and signatures"
        author = "SBARDS Security Team"
        version = "2.0.0"
        severity = "medium"
        category = "file_validation"
        
    condition:
        // PE file with non-executable extension
        (uint16(0) == 0x5A4D and not (
            filename matches /.*\.(exe|dll|sys|scr|com|pif|bat|cmd)$/i
        )) or
        
        // ZIP file with non-archive extension
        (uint32(0) == 0x04034b50 and not (
            filename matches /.*\.(zip|jar|war|ear|docx|xlsx|pptx)$/i
        )) or
        
        // PDF file with non-document extension
        (uint32(0) == 0x46445025 and not (
            filename matches /.*\.pdf$/i
        )) or
        
        // Image file with executable extension
        ((uint16(0) == 0xd8ff or uint32(0) == 0x474e5089) and (
            filename matches /.*\.(exe|dll|sys|scr|com|bat|cmd)$/i
        ))
}

/*
    Performance Optimization Rules
    ==============================
    
    These rules are optimized for fast scanning of large file sets.
*/

rule SBARDS_Quick_Malware_Scan
{
    meta:
        description = "Quick malware detection for high-volume scanning"
        author = "SBARDS Security Team"
        version = "2.0.0"
        performance = "optimized"
        
    strings:
        $quick1 = { 4D 5A } // PE header
        $quick2 = "This program cannot be run in DOS mode"
        $quick3 = { 50 4B 03 04 } // ZIP header
        $quick4 = { 25 50 44 46 } // PDF header
        
    condition:
        // Quick file type identification
        (uint16(0) == 0x5A4D or uint32(0) == 0x04034b50 or uint32(0) == 0x46445025) and
        
        // Basic malware indicators
        filesize > 1KB and filesize < 10MB
}

/*
    Statistical Analysis Rules
    =========================
    
    These rules use statistical analysis for advanced threat detection.
*/

rule SBARDS_Entropy_Analysis
{
    meta:
        description = "High entropy detection for packed/encrypted files"
        author = "SBARDS Security Team"
        version = "2.0.0"
        technique = "entropy_analysis"
        
    condition:
        // This rule is implemented in the C++ entropy checker
        // and is included here for completeness
        filesize > 1KB and
        
        // High entropy sections (implemented in C++)
        // entropy > 7.5 for any 1KB block
        true // Placeholder - actual entropy calculation in C++
}

/*
    Rule Statistics and Metadata
    ============================
    
    This section provides metadata about the rule set for reporting.
*/

rule SBARDS_Rule_Set_Info
{
    meta:
        description = "SBARDS rule set information and statistics"
        total_rules = "50+"
        categories = "malware,ransomware,permissions,custom,basic"
        last_updated = "2025-05-25"
        version = "2.0.0"
        performance_optimized = true
        
    condition:
        false // This rule never matches - it's for metadata only
}
