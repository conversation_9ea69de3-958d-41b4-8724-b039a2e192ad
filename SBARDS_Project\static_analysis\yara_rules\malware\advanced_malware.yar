/*
    Advanced Malware Detection Rules for SBARDS
    
    This file contains enhanced YARA rules for detecting various types of malware
    with improved accuracy and reduced false positives.
    
    Categories covered:
    - Trojans and backdoors
    - Keyloggers and stealers
    - Rootkits and bootkits
    - Advanced persistent threats (APTs)
    - Fileless malware
    - Polymorphic and metamorphic malware
*/

import "pe"
import "math"
import "hash"

rule Advanced_Trojan_Behavior
{
    meta:
        description = "Detects advanced trojan behavior patterns"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "malware"
        severity = "high"
        tags = "trojan,backdoor,remote_access"
        
    strings:
        // Network communication patterns
        $net1 = "CreateRemoteThread" ascii wide
        $net2 = "InternetOpenA" ascii wide
        $net3 = "InternetConnectA" ascii wide
        $net4 = "HttpOpenRequestA" ascii wide
        $net5 = "send" ascii wide
        $net6 = "recv" ascii wide
        
        // Process manipulation
        $proc1 = "CreateProcessA" ascii wide
        $proc2 = "OpenProcess" ascii wide
        $proc3 = "WriteProcessMemory" ascii wide
        $proc4 = "ReadProcessMemory" ascii wide
        $proc5 = "VirtualAllocEx" ascii wide
        
        // Registry manipulation
        $reg1 = "RegCreateKeyA" ascii wide
        $reg2 = "RegSetValueA" ascii wide
        $reg3 = "RegOpenKeyA" ascii wide
        $reg4 = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run" ascii wide
        
        // File operations
        $file1 = "CreateFileA" ascii wide
        $file2 = "WriteFile" ascii wide
        $file3 = "SetFileAttributesA" ascii wide
        $file4 = "DeleteFileA" ascii wide
        
        // Suspicious strings
        $sus1 = "backdoor" ascii wide nocase
        $sus2 = "keylog" ascii wide nocase
        $sus3 = "password" ascii wide nocase
        $sus4 = "steal" ascii wide nocase
        $sus5 = "inject" ascii wide nocase
        
    condition:
        pe.is_pe and
        (
            (3 of ($net*) and 2 of ($proc*)) or
            (2 of ($net*) and 3 of ($proc*) and 1 of ($reg*)) or
            (4 of ($sus*) and 2 of ($proc*))
        ) and
        filesize < 10MB
}

rule Keylogger_Detection
{
    meta:
        description = "Detects keylogger functionality"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "malware"
        severity = "high"
        tags = "keylogger,stealer,credential_theft"
        
    strings:
        // Keyboard hooks
        $hook1 = "SetWindowsHookExA" ascii wide
        $hook2 = "SetWindowsHookExW" ascii wide
        $hook3 = "WH_KEYBOARD_LL" ascii wide
        $hook4 = "WH_KEYBOARD" ascii wide
        $hook5 = "GetAsyncKeyState" ascii wide
        $hook6 = "GetKeyState" ascii wide
        
        // Key capture
        $key1 = "VK_RETURN" ascii wide
        $key2 = "VK_SPACE" ascii wide
        $key3 = "VK_BACK" ascii wide
        $key4 = "VK_TAB" ascii wide
        $key5 = "VK_SHIFT" ascii wide
        
        // Log file operations
        $log1 = "keylog" ascii wide nocase
        $log2 = "keys.txt" ascii wide nocase
        $log3 = "passwords.txt" ascii wide nocase
        $log4 = "log.dat" ascii wide nocase
        
        // Browser targeting
        $browser1 = "chrome.exe" ascii wide nocase
        $browser2 = "firefox.exe" ascii wide nocase
        $browser3 = "iexplore.exe" ascii wide nocase
        $browser4 = "edge.exe" ascii wide nocase
        
    condition:
        pe.is_pe and
        (
            (2 of ($hook*) and 2 of ($key*)) or
            (1 of ($hook*) and 3 of ($key*) and 1 of ($log*)) or
            (2 of ($hook*) and 2 of ($browser*))
        ) and
        filesize < 5MB
}

rule Rootkit_Behavior
{
    meta:
        description = "Detects rootkit behavior and stealth techniques"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "malware"
        severity = "critical"
        tags = "rootkit,stealth,system_modification"
        
    strings:
        // SSDT hooking
        $ssdt1 = "KeServiceDescriptorTable" ascii wide
        $ssdt2 = "NtQuerySystemInformation" ascii wide
        $ssdt3 = "NtQueryDirectoryFile" ascii wide
        $ssdt4 = "NtEnumerateKey" ascii wide
        
        // Driver operations
        $driver1 = "CreateService" ascii wide
        $driver2 = "StartService" ascii wide
        $driver3 = "LoadDriver" ascii wide
        $driver4 = "\\\\.\\Global\\" ascii wide
        
        // Process hiding
        $hide1 = "ZwQuerySystemInformation" ascii wide
        $hide2 = "PsSetCreateProcessNotifyRoutine" ascii wide
        $hide3 = "PsSetLoadImageNotifyRoutine" ascii wide
        
        // File system filter
        $filter1 = "IoCreateDevice" ascii wide
        $filter2 = "IoAttachDeviceToDeviceStack" ascii wide
        $filter3 = "IofCompleteRequest" ascii wide
        
    condition:
        pe.is_pe and
        (
            (2 of ($ssdt*) and 1 of ($driver*)) or
            (1 of ($ssdt*) and 2 of ($hide*)) or
            (2 of ($filter*) and 1 of ($hide*))
        ) and
        pe.characteristics & pe.IMAGE_FILE_SYSTEM
}

rule APT_Indicators
{
    meta:
        description = "Detects Advanced Persistent Threat indicators"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "malware"
        severity = "critical"
        tags = "apt,targeted_attack,persistence"
        
    strings:
        // Persistence mechanisms
        $persist1 = "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run" ascii wide
        $persist2 = "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run" ascii wide
        $persist3 = "schtasks" ascii wide
        $persist4 = "at.exe" ascii wide
        $persist5 = "wmic" ascii wide
        
        // Lateral movement
        $lateral1 = "psexec" ascii wide nocase
        $lateral2 = "wmiexec" ascii wide nocase
        $lateral3 = "net use" ascii wide
        $lateral4 = "net share" ascii wide
        $lateral5 = "admin$" ascii wide
        
        // Data exfiltration
        $exfil1 = "ftp" ascii wide
        $exfil2 = "sftp" ascii wide
        $exfil3 = "scp" ascii wide
        $exfil4 = "curl" ascii wide
        $exfil5 = "wget" ascii wide
        
        // Reconnaissance
        $recon1 = "whoami" ascii wide
        $recon2 = "systeminfo" ascii wide
        $recon3 = "tasklist" ascii wide
        $recon4 = "netstat" ascii wide
        $recon5 = "ipconfig" ascii wide
        
    condition:
        pe.is_pe and
        (
            (2 of ($persist*) and 1 of ($lateral*)) or
            (1 of ($persist*) and 2 of ($exfil*)) or
            (3 of ($recon*) and 1 of ($lateral*))
        ) and
        filesize > 50KB
}

rule Fileless_Malware
{
    meta:
        description = "Detects fileless malware techniques"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "malware"
        severity = "high"
        tags = "fileless,memory_only,powershell"
        
    strings:
        // PowerShell execution
        $ps1 = "powershell.exe" ascii wide nocase
        $ps2 = "powershell -" ascii wide nocase
        $ps3 = "Invoke-Expression" ascii wide nocase
        $ps4 = "IEX" ascii wide nocase
        $ps5 = "DownloadString" ascii wide nocase
        $ps6 = "EncodedCommand" ascii wide nocase
        
        // WMI usage
        $wmi1 = "winmgmts:" ascii wide
        $wmi2 = "Win32_Process" ascii wide
        $wmi3 = "Create" ascii wide
        $wmi4 = "wmic process" ascii wide
        
        // Memory operations
        $mem1 = "VirtualAlloc" ascii wide
        $mem2 = "VirtualProtect" ascii wide
        $mem3 = "WriteProcessMemory" ascii wide
        $mem4 = "CreateRemoteThread" ascii wide
        
        // Base64 encoded content
        $b64_1 = /[A-Za-z0-9+\/]{50,}={0,2}/ ascii
        
    condition:
        pe.is_pe and
        (
            (3 of ($ps*) and 1 of ($mem*)) or
            (2 of ($wmi*) and 2 of ($mem*)) or
            (2 of ($ps*) and #b64_1 > 5)
        )
}

rule Polymorphic_Malware
{
    meta:
        description = "Detects polymorphic and metamorphic malware"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "malware"
        severity = "high"
        tags = "polymorphic,metamorphic,evasion"
        
    strings:
        // Encryption/decryption routines
        $crypt1 = "CryptAcquireContext" ascii wide
        $crypt2 = "CryptCreateHash" ascii wide
        $crypt3 = "CryptEncrypt" ascii wide
        $crypt4 = "CryptDecrypt" ascii wide
        
        // Self-modification
        $modify1 = "VirtualProtect" ascii wide
        $modify2 = "FlushInstructionCache" ascii wide
        $modify3 = "WriteProcessMemory" ascii wide
        
        // Anti-analysis
        $anti1 = "IsDebuggerPresent" ascii wide
        $anti2 = "CheckRemoteDebuggerPresent" ascii wide
        $anti3 = "GetTickCount" ascii wide
        $anti4 = "QueryPerformanceCounter" ascii wide
        
    condition:
        pe.is_pe and
        (
            (2 of ($crypt*) and 2 of ($modify*)) or
            (1 of ($crypt*) and 2 of ($modify*) and 1 of ($anti*))
        ) and
        math.entropy(0, filesize) > 7.0
}

rule Banking_Trojan
{
    meta:
        description = "Detects banking trojan behavior"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "malware"
        severity = "critical"
        tags = "banking_trojan,financial_theft,credential_stealer"
        
    strings:
        // Banking-related strings
        $bank1 = "bank" ascii wide nocase
        $bank2 = "paypal" ascii wide nocase
        $bank3 = "credit" ascii wide nocase
        $bank4 = "account" ascii wide nocase
        $bank5 = "login" ascii wide nocase
        $bank6 = "password" ascii wide nocase
        
        // Browser injection
        $inject1 = "SetWindowsHookEx" ascii wide
        $inject2 = "CreateRemoteThread" ascii wide
        $inject3 = "WriteProcessMemory" ascii wide
        $inject4 = "VirtualAllocEx" ascii wide
        
        // Network communication
        $net1 = "InternetOpen" ascii wide
        $net2 = "HttpSendRequest" ascii wide
        $net3 = "InternetReadFile" ascii wide
        
        // Certificate manipulation
        $cert1 = "CertOpenStore" ascii wide
        $cert2 = "CertAddCertificateContextToStore" ascii wide
        
    condition:
        pe.is_pe and
        (
            (3 of ($bank*) and 2 of ($inject*)) or
            (2 of ($bank*) and 2 of ($inject*) and 1 of ($net*)) or
            (2 of ($bank*) and 1 of ($cert*))
        ) and
        filesize < 2MB
}

rule Ransomware_Indicators
{
    meta:
        description = "Detects ransomware behavior patterns"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "malware"
        severity = "critical"
        tags = "ransomware,encryption,extortion"
        
    strings:
        // Encryption APIs
        $crypt1 = "CryptAcquireContext" ascii wide
        $crypt2 = "CryptGenKey" ascii wide
        $crypt3 = "CryptEncrypt" ascii wide
        $crypt4 = "CryptImportKey" ascii wide
        
        // File operations
        $file1 = "FindFirstFile" ascii wide
        $file2 = "FindNextFile" ascii wide
        $file3 = "CreateFile" ascii wide
        $file4 = "WriteFile" ascii wide
        $file5 = "DeleteFile" ascii wide
        
        // Ransom note indicators
        $ransom1 = "decrypt" ascii wide nocase
        $ransom2 = "bitcoin" ascii wide nocase
        $ransom3 = "payment" ascii wide nocase
        $ransom4 = "recover" ascii wide nocase
        $ransom5 = "files encrypted" ascii wide nocase
        
        // File extensions
        $ext1 = ".encrypted" ascii wide
        $ext2 = ".locked" ascii wide
        $ext3 = ".crypto" ascii wide
        
    condition:
        pe.is_pe and
        (
            (3 of ($crypt*) and 3 of ($file*)) or
            (2 of ($crypt*) and 2 of ($ransom*)) or
            (2 of ($file*) and 2 of ($ransom*) and 1 of ($ext*))
        ) and
        filesize > 10KB
}
