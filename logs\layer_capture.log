2025-05-26 05:50:22,540 - layer.capture - INFO - [true_file_interceptor.py:240] - __init__() - TrueFileInterceptor initialized
2025-05-26 05:50:22,541 - layer.capture - INFO - [integrated_capture_layer.py:64] - __init__() - IntegratedCaptureLayer initialized
2025-05-26 05:50:22,542 - layer.capture - INFO - [true_file_interceptor.py:245] - set_static_analysis_callback() - Static analysis callback registered
2025-05-26 05:50:22,543 - layer.capture - INFO - [integrated_capture_layer.py:70] - set_static_analysis_callback() - Static analysis callback registered
2025-05-26 05:50:22,544 - layer.capture - INFO - [integrated_capture_layer.py:125] - start() - Starting Integrated Capture Layer...
2025-05-26 05:50:22,545 - layer.capture - INFO - [cpp_integration.py:216] - initialize() - <PERSON>ck C++ interceptor initialized
2025-05-26 05:50:22,546 - layer.capture - INFO - [cpp_integration.py:221] - set_python_callback() - Mock Python callback registered
2025-05-26 05:50:22,547 - layer.capture - INFO - [true_file_interceptor.py:322] - _worker_loop() - Worker loop started
2025-05-26 05:50:22,548 - layer.capture - INFO - [true_file_interceptor.py:260] - start() - TrueFileInterceptor started successfully
2025-05-26 05:50:22,549 - layer.capture - INFO - [cpp_integration.py:225] - start() - Mock C++ interceptor started
2025-05-26 05:50:22,551 - layer.capture - INFO - [integrated_capture_layer.py:149] - start() - Integrated Capture Layer started successfully
2025-05-26 05:50:22,846 - layer.capture - INFO - [true_file_interceptor.py:240] - __init__() - TrueFileInterceptor initialized
2025-05-26 05:50:22,847 - layer.capture - INFO - [integrated_capture_layer.py:64] - __init__() - IntegratedCaptureLayer initialized
2025-05-26 05:50:22,847 - layer.capture - INFO - [true_file_interceptor.py:245] - set_static_analysis_callback() - Static analysis callback registered
2025-05-26 05:50:22,849 - layer.capture - INFO - [integrated_capture_layer.py:70] - set_static_analysis_callback() - Static analysis callback registered
2025-05-26 05:50:22,850 - layer.capture - INFO - [integrated_capture_layer.py:125] - start() - Starting Integrated Capture Layer...
2025-05-26 05:50:22,851 - layer.capture - INFO - [cpp_integration.py:216] - initialize() - Mock C++ interceptor initialized
2025-05-26 05:50:22,851 - layer.capture - INFO - [cpp_integration.py:221] - set_python_callback() - Mock Python callback registered
2025-05-26 05:50:22,852 - layer.capture - INFO - [true_file_interceptor.py:322] - _worker_loop() - Worker loop started
2025-05-26 05:50:22,853 - layer.capture - INFO - [true_file_interceptor.py:260] - start() - TrueFileInterceptor started successfully
2025-05-26 05:50:22,854 - layer.capture - INFO - [cpp_integration.py:225] - start() - Mock C++ interceptor started
2025-05-26 05:50:22,855 - layer.capture - INFO - [integrated_capture_layer.py:149] - start() - Integrated Capture Layer started successfully
2025-05-26 05:50:24,551 - layer.capture - INFO - [integrated_capture_layer.py:95] - _cpp_callback() - File intercepted by C++: /tmp/mock_intercepted_file.txt
2025-05-26 05:50:24,646 - layer.capture - INFO - [true_file_interceptor.py:125] - store_file() - File stored securely: 20250526_055024_553945_mock_file.txt (hash: 65c92b167a9717fa...)
2025-05-26 05:50:24,647 - layer.capture - INFO - [true_file_interceptor.py:313] - intercept_file_data() - File intercepted: mock_file.txt -> capture\temp_storage\incoming\20250526_055024_553945_mock_file.txt
2025-05-26 05:50:24,647 - layer.capture - INFO - [integrated_capture_layer.py:110] - _cpp_callback() - File passed to Python processing: 65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699
2025-05-26 05:50:24,652 - layer.capture - ERROR - [true_file_interceptor.py:157] - restore_to_original() - Failed to restore file to /home/<USER>/Downloads/mock_file.txt: [Errno 13] Permission denied: '/home/<USER>/Downloads/mock_file.txt'
2025-05-26 05:50:24,653 - layer.capture - WARNING - [true_file_interceptor.py:402] - _quarantine_file() - File quarantined: capture\temp_storage\incoming\20250526_055024_553945_mock_file.txt - Reason: Failed to restore
2025-05-26 05:50:24,654 - layer.capture - INFO - [true_file_interceptor.py:422] - _notify_quarantine() - Quarantine notification: {'type': 'quarantine', 'original_filename': 'mock_file.txt', 'file_hash': '65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699', 'reason': 'Failed to restore', 'quarantine_path': 'capture\\temp_storage\\quarantine\\20250526_055024_553945_mock_file.txt', 'timestamp': '2025-05-26T05:50:24.654047'}
2025-05-26 05:50:24,855 - layer.capture - INFO - [integrated_capture_layer.py:95] - _cpp_callback() - File intercepted by C++: /tmp/mock_intercepted_file.txt
2025-05-26 05:50:24,964 - layer.capture - INFO - [true_file_interceptor.py:125] - store_file() - File stored securely: 20250526_055024_856087_mock_file.txt (hash: 65c92b167a9717fa...)
2025-05-26 05:50:24,965 - layer.capture - INFO - [true_file_interceptor.py:313] - intercept_file_data() - File intercepted: mock_file.txt -> capture\temp_storage\incoming\20250526_055024_856087_mock_file.txt
2025-05-26 05:50:24,966 - layer.capture - INFO - [integrated_capture_layer.py:110] - _cpp_callback() - File passed to Python processing: 65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699
2025-05-26 05:50:24,971 - layer.capture - ERROR - [true_file_interceptor.py:157] - restore_to_original() - Failed to restore file to /home/<USER>/Downloads/mock_file.txt: [Errno 13] Permission denied: '/home/<USER>/Downloads/mock_file.txt'
2025-05-26 05:50:24,972 - layer.capture - WARNING - [true_file_interceptor.py:402] - _quarantine_file() - File quarantined: capture\temp_storage\incoming\20250526_055024_856087_mock_file.txt - Reason: Failed to restore
2025-05-26 05:50:24,973 - layer.capture - INFO - [true_file_interceptor.py:422] - _notify_quarantine() - Quarantine notification: {'type': 'quarantine', 'original_filename': 'mock_file.txt', 'file_hash': '65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699', 'reason': 'Failed to restore', 'quarantine_path': 'capture\\temp_storage\\quarantine\\20250526_055024_856087_mock_file.txt', 'timestamp': '2025-05-26T05:50:24.973035'}
