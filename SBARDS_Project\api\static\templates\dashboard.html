<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBARDS - Advanced Security Dashboard</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- External Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom Styles -->
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <link href="/static/css/themes.css" rel="stylesheet">
    <link href="/static/css/responsive.css" rel="stylesheet">

    <!-- Meta Tags -->
    <meta name="description" content="SBARDS Advanced Security Dashboard - Real-time monitoring and threat detection">
    <meta name="keywords" content="security, dashboard, monitoring, threat detection, SBARDS">
    <meta name="author" content="SBARDS Team">
</head>
<body>
    <!-- Theme Toggle Button -->
    <button id="themeToggle" class="theme-toggle" title="Toggle Dark/Light Mode (Ctrl+D)">
        <i class="theme-toggle-icon fas fa-moon"></i>
        <span>Dark Mode</span>
    </button>

    <!-- Dashboard Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <h1 class="header-title">
                <i class="fas fa-shield-alt"></i>
                SBARDS Security Dashboard
            </h1>
            <p class="header-subtitle">
                Advanced Real-time Security Monitoring & Threat Detection System
            </p>

            <!-- Header Statistics -->
            <div class="header-stats">
                <div class="header-stat">
                    <span class="header-stat-value" id="totalFilesHeader">0</span>
                    <span class="header-stat-label">Files Processed</span>
                </div>
                <div class="header-stat">
                    <span class="header-stat-value" id="threatsDetectedHeader">0</span>
                    <span class="header-stat-label">Threats Detected</span>
                </div>
                <div class="header-stat">
                    <span class="header-stat-value" id="systemUptimeHeader">0h</span>
                    <span class="header-stat-label">System Uptime</span>
                </div>
                <div class="header-stat">
                    <span class="header-stat-value" id="processingSpeedHeader">0</span>
                    <span class="header-stat-label">Files/Min</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard Container -->
    <main class="dashboard-container">

        <!-- System Status Row -->
        <div class="dashboard-grid">
            <!-- Capture Layer Status -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-download"></i>
                        Capture Layer
                    </h3>
                    <div id="captureStatus" class="status-indicator status-info">
                        <i class="fas fa-spinner fa-spin"></i>
                        Loading...
                    </div>
                </div>
                <div class="card-content">
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>File Interception Rate</span>
                            <span id="captureRate">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="captureProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <p class="mt-3">
                        <strong>Active Monitoring:</strong> Downloads, USB, Email, Cloud Storage
                    </p>
                </div>
            </div>

            <!-- Static Analysis Status -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-search"></i>
                        Static Analysis
                    </h3>
                    <div id="staticStatus" class="status-indicator status-info">
                        <i class="fas fa-spinner fa-spin"></i>
                        Loading...
                    </div>
                </div>
                <div class="card-content">
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>Analysis Completion</span>
                            <span id="analysisRate">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="analysisProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <p class="mt-3">
                        <strong>YARA Rules:</strong> Malware, Ransomware, Suspicious Patterns
                    </p>
                </div>
            </div>

            <!-- API Layer Status -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-server"></i>
                        API Layer
                    </h3>
                    <div id="apiStatus" class="status-indicator status-active">
                        <i class="fas fa-check-circle"></i>
                        Active
                    </div>
                </div>
                <div class="card-content">
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>Response Time</span>
                            <span id="responseTime">< 100ms</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="responseProgress" style="width: 95%"></div>
                        </div>
                    </div>
                    <p class="mt-3">
                        <strong>Endpoints:</strong> Dashboard, Upload, Monitoring, Analytics
                    </p>
                </div>
            </div>

            <!-- System Health -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-heartbeat"></i>
                        System Health
                    </h3>
                    <div id="systemStatus" class="status-indicator status-active">
                        <i class="fas fa-heart"></i>
                        Excellent
                    </div>
                </div>
                <div class="card-content">
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>Overall Health</span>
                            <span id="healthScore">98%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="healthProgress" style="width: 98%"></div>
                        </div>
                    </div>
                    <p class="mt-3">
                        <strong>Monitoring:</strong> CPU, Memory, Disk, Network
                    </p>
                </div>
            </div>
        </div>

        <!-- Performance Metrics Row -->
        <div class="dashboard-grid">
            <!-- CPU Usage Chart -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-microchip"></i>
                        CPU Usage
                    </h3>
                    <span class="card-badge">Real-time</span>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <canvas id="cpuChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>

            <!-- Memory Usage Chart -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-memory"></i>
                        Memory Usage
                    </h3>
                    <span class="card-badge">Real-time</span>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <canvas id="memoryChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Row -->
        <div class="dashboard-grid">
            <!-- File Processing Statistics -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-file-alt"></i>
                        File Processing
                    </h3>
                    <span class="card-badge">Live Data</span>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <canvas id="fileChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>

            <!-- Threat Detection Analysis -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-shield-virus"></i>
                        Threat Detection
                    </h3>
                    <span class="card-badge">Analysis</span>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <canvas id="threatChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Metrics Row -->
        <div class="dashboard-grid dashboard-grid-wide">
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="card-icon fas fa-chart-line"></i>
                        Key Performance Indicators
                    </h3>
                    <span class="card-badge">Live Metrics</span>
                </div>
                <div class="card-content">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <span class="metric-value" id="totalFilesMetric">0</span>
                                <span class="metric-label">Total Files Processed</span>
                                <div class="metric-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+12% from yesterday</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <span class="metric-value" id="threatsDetectedMetric">0</span>
                                <span class="metric-label">Threats Detected</span>
                                <div class="metric-change negative">
                                    <i class="fas fa-arrow-down"></i>
                                    <span>-5% from yesterday</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <span class="metric-value" id="systemUptimeMetric">0</span>
                                <span class="metric-label">System Uptime (Hours)</span>
                                <div class="metric-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>99.9% availability</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <span class="metric-value" id="processingSpeedMetric">0</span>
                                <span class="metric-label">Processing Speed (Files/Min)</span>
                                <div class="metric-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+8% performance boost</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Information -->
        <div class="dashboard-grid dashboard-grid-wide">
            <div class="dashboard-card">
                <div class="card-content text-center">
                    <p class="mb-2">
                        <strong>SBARDS v2.0.0</strong> - Advanced Security Monitoring System
                    </p>
                    <p class="text-muted mb-0" id="lastUpdate">
                        Last updated: Loading...
                    </p>
                    <div class="mt-3">
                        <span class="status-indicator status-active me-3">
                            <i class="fas fa-wifi"></i>
                            Connected
                        </span>
                        <span class="status-indicator status-info">
                            <i class="fas fa-clock"></i>
                            Auto-refresh: 3s
                        </span>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <p>Loading Dashboard...</p>
    </div>

    <!-- External Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>

    <!-- Custom Scripts -->
    <script src="/static/js/navigation.js"></script>
    <script src="/static/js/interactive-components.js"></script>
    <script src="/static/js/dashboard.js"></script>

    <!-- Additional Styles for Notifications -->
    <style>
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        }

        .notification {
            background: var(--theme-bg-card);
            border: 1px solid var(--theme-border-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px var(--theme-shadow);
            animation: slideInRight 0.3s ease;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--theme-text-muted);
            cursor: pointer;
            margin-left: auto;
            padding: 5px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            color: white;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</body>
</html>
