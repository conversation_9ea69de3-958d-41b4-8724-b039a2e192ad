# SBARDS New Architecture Requirements
# Core dependencies for multi-layer security analysis system
#
# Migration Status: ✅ SUCCESSFULLY ENHANCED from SBARDSProject
# Migration Date: 2025-05-25
# Enhanced with new architecture requirements while preserving all original functionality

# Core Python Libraries
yara-python>=4.3.0
psutil>=5.9.0
pathlib>=1.0.1
typing-extensions>=4.0.0

# Data Processing and Analysis
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# File and System Operations
watchdog>=3.0.0
chardet>=5.0.0
python-magic>=0.4.27
pydeep>=0.4  # For SSDEEP hashing

# Web Framework and API
fastapi>=0.100.0
uvicorn[standard]>=0.22.0
pydantic>=2.0.0
python-multipart>=0.0.6
aiofiles>=23.0.0
websockets>=11.0.0
starlette>=0.27.0

# HTTP Client Libraries
requests>=2.31.0
httpx>=0.24.0
aiohttp>=3.8.0

# Database and Caching
sqlalchemy>=2.0.0
alembic>=1.11.0
redis>=4.5.0
sqlite3  # Built-in, but listed for clarity
pymongo>=4.5.0  # MongoDB support for security logging
motor>=3.3.0  # Async MongoDB driver

# Security and Cryptography
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0
bcrypt>=4.0.0

# Monitoring and Logging
prometheus-client>=0.17.0
structlog>=23.0.0
colorlog>=6.7.0

# Task Queue and Background Processing
celery>=5.3.0
kombu>=5.3.0
billiard>=4.1.0

# Template Engine and UI
jinja2>=3.1.0
flask>=2.3.0  # For dashboard compatibility
bootstrap-flask>=2.2.0

# Data Visualization
matplotlib>=3.7.0
plotly>=5.15.0
seaborn>=0.12.0

# Machine Learning (Optional - for dynamic analysis)
scikit-learn>=1.3.0
tensorflow>=2.13.0; python_version >= "3.8"
torch>=2.0.0; python_version >= "3.8"

# Network Analysis
scapy>=2.5.0
netaddr>=0.8.0

# File Format Analysis
python-docx>=0.8.11
PyPDF2>=3.0.0
openpyxl>=3.1.0
python-pptx>=0.6.21

# Archive Handling
py7zr>=0.20.0
rarfile>=4.0
zipfile36>=0.1.3

# Configuration and Environment
python-dotenv>=1.0.0
configparser>=5.3.0
toml>=0.10.2
pyyaml>=6.0

# Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
coverage>=7.2.0

# Development Tools
black>=23.0.0
flake8>=6.0.0
mypy>=1.4.0
pre-commit>=3.3.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.2.0

# Performance and Profiling
memory-profiler>=0.60.0
line-profiler>=4.0.0
py-spy>=0.3.14

# C++ Integration
pybind11>=2.11.0
cython>=3.0.0
cffi>=1.15.0

# Blockchain Integration (Optional)
web3>=6.8.0
eth-account>=0.9.0

# External API Integration
virustotal-api>=1.1.11
shodan>=1.29.0

# Platform-Specific Dependencies
pywin32>=306; platform_system == "Windows"
pywin32-ctypes>=0.2.2; platform_system == "Windows"
wmi>=1.5.1; platform_system == "Windows"

# Linux-Specific
python-systemd>=234; platform_system == "Linux"
pyinotify>=0.9.6; platform_system == "Linux"

# macOS-Specific
pyobjc>=9.2; platform_system == "Darwin"
fsevents>=0.2.3; platform_system == "Darwin"

# Memory Protection and Encryption
keyring>=24.0.0
keyrings.alt>=4.2.0

# Sandboxing and Isolation
docker>=6.1.0
kubernetes>=27.0.0

# Threat Intelligence
stix2>=3.0.0
taxii2-client>=2.3.0

# Additional Security Tools
yara-python-dex>=1.0.0  # For Android DEX files
pefile>=2023.2.7  # For PE file analysis
pyelftools>=0.29  # For ELF file analysis

# GUI Framework (Optional)
tkinter  # Built-in
PyQt5>=5.15.0; python_version >= "3.7"
PySide6>=6.5.0; python_version >= "3.8"

# CLI Enhancement
click>=8.1.0
rich>=13.4.0
typer>=0.9.0

# Async and Concurrency
asyncio  # Built-in
concurrent.futures  # Built-in
threading  # Built-in
multiprocessing  # Built-in

# Email and Notifications
smtplib  # Built-in
email  # Built-in
plyer>=2.1.0  # Cross-platform notifications

# Image Processing (for steganography detection)
Pillow>=10.0.0
opencv-python>=4.8.0

# Audio Analysis (for audio file threats)
librosa>=0.10.0
soundfile>=0.12.0

# Video Analysis (for video file threats)
opencv-python-headless>=4.8.0
imageio>=2.31.0

# Natural Language Processing (for text analysis)
nltk>=3.8.0
spacy>=3.6.0

# Time and Date Handling
python-dateutil>=2.8.0
pytz>=2023.3

# URL and Domain Analysis
tldextract>=3.4.0
publicsuffix2>=2.20191221

# Memory Forensics (Optional)
volatility3>=2.4.0

# Reverse Engineering Tools (Optional)
capstone>=5.0.0
keystone-engine>=0.9.2
unicorn>=2.0.0

# Additional Utilities
tqdm>=4.65.0  # Progress bars
humanize>=4.7.0  # Human-readable numbers
tabulate>=0.9.0  # Table formatting
colorama>=0.4.6  # Cross-platform colored terminal text

# Capture Layer Specific Dependencies
ctypes  # Built-in - for C++ integration
dataclasses  # Built-in - for data structures
queue  # Built-in - for thread-safe queues
tempfile  # Built-in - for temporary file handling
shutil  # Built-in - for file operations
