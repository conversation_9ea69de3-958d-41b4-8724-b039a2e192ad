"""
Validation utilities for SBARDS API

This module provides validation functions for API inputs.
"""

import os
import re
from typing import Dict, Any, List

from core.logger import get_global_logger

# Configure logging
logger = get_global_logger().get_layer_logger("api.utils.validators")


def validate_file_path(file_path: str) -> Dict[str, Any]:
    """
    Validate file path.
    
    Args:
        file_path (str): Path to validate.
        
    Returns:
        dict: Validation result.
    """
    result = {
        "valid": True,
        "errors": [],
        "warnings": []
    }
    
    try:
        # Check if path is provided
        if not file_path:
            result["valid"] = False
            result["errors"].append("File path is required")
            return result
        
        # Check for path traversal attempts
        if ".." in file_path or file_path.startswith("/"):
            result["valid"] = False
            result["errors"].append("Invalid file path: path traversal detected")
            return result
        
        # Check if file exists
        if not os.path.exists(file_path):
            result["valid"] = False
            result["errors"].append(f"File not found: {file_path}")
            return result
        
        # Check if it's actually a file
        if not os.path.isfile(file_path):
            result["valid"] = False
            result["errors"].append(f"Path is not a file: {file_path}")
            return result
        
        # Check file size (warn if too large)
        file_size = os.path.getsize(file_path)
        if file_size > 100 * 1024 * 1024:  # 100MB
            result["warnings"].append(f"Large file size: {file_size} bytes")
        
        # Check file permissions
        if not os.access(file_path, os.R_OK):
            result["valid"] = False
            result["errors"].append(f"File not readable: {file_path}")
            return result
        
        return result
        
    except Exception as e:
        logger.error(f"Error validating file path: {e}")
        result["valid"] = False
        result["errors"].append(f"Validation error: {str(e)}")
        return result


def validate_scan_request(scan_request: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate scan request data.
    
    Args:
        scan_request (dict): Scan request to validate.
        
    Returns:
        dict: Validation result.
    """
    result = {
        "valid": True,
        "errors": [],
        "warnings": []
    }
    
    try:
        # Required fields
        required_fields = ["scan_id", "target_path"]
        for field in required_fields:
            if field not in scan_request:
                result["valid"] = False
                result["errors"].append(f"Missing required field: {field}")
        
        # Validate scan_id format
        if "scan_id" in scan_request:
            scan_id = scan_request["scan_id"]
            if not re.match(r"^[a-zA-Z0-9_-]+$", scan_id):
                result["valid"] = False
                result["errors"].append("Invalid scan_id format")
        
        # Validate target_path
        if "target_path" in scan_request:
            path_validation = validate_file_path(scan_request["target_path"])
            if not path_validation["valid"]:
                result["valid"] = False
                result["errors"].extend(path_validation["errors"])
            result["warnings"].extend(path_validation["warnings"])
        
        # Validate analysis_types if provided
        if "analysis_types" in scan_request:
            valid_types = {"signature", "entropy", "hash", "yara"}
            analysis_types = scan_request["analysis_types"]
            
            if not isinstance(analysis_types, list):
                result["valid"] = False
                result["errors"].append("analysis_types must be a list")
            else:
                for analysis_type in analysis_types:
                    if analysis_type not in valid_types:
                        result["warnings"].append(f"Unknown analysis type: {analysis_type}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error validating scan request: {e}")
        result["valid"] = False
        result["errors"].append(f"Validation error: {str(e)}")
        return result


def validate_hash(hash_value: str, algorithm: str = "sha256") -> bool:
    """
    Validate hash format.
    
    Args:
        hash_value (str): Hash to validate.
        algorithm (str): Hash algorithm.
        
    Returns:
        bool: True if valid hash format.
    """
    try:
        if not hash_value:
            return False
        
        # Expected lengths for different algorithms
        expected_lengths = {
            "md5": 32,
            "sha1": 40,
            "sha256": 64,
            "sha512": 128
        }
        
        expected_length = expected_lengths.get(algorithm.lower())
        if not expected_length:
            return False
        
        # Check length and hex format
        if len(hash_value) != expected_length:
            return False
        
        # Check if it's valid hex
        try:
            int(hash_value, 16)
            return True
        except ValueError:
            return False
            
    except Exception as e:
        logger.error(f"Error validating hash: {e}")
        return False


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe usage.
    
    Args:
        filename (str): Filename to sanitize.
        
    Returns:
        str: Sanitized filename.
    """
    try:
        # Remove path components
        filename = os.path.basename(filename)
        
        # Replace dangerous characters
        dangerous_chars = r'[<>:"/\\|?*]'
        filename = re.sub(dangerous_chars, '_', filename)
        
        # Remove leading/trailing dots and spaces
        filename = filename.strip('. ')
        
        # Ensure it's not empty
        if not filename:
            filename = "unnamed_file"
        
        # Limit length
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename
        
    except Exception as e:
        logger.error(f"Error sanitizing filename: {e}")
        return "sanitized_file"
