import os
import sys
import subprocess
import json
import platform
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the orchestrator
from scanner_core.python.orchestrator import Orchestrator
from scanner_core.utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>

def get_python_command():
    """Get the appropriate Python command for the current platform"""
    if platform.system() == "Windows":
        return "python"
    else:
        return "python3"

def get_pipenv_command():
    """Get the appropriate pipenv command for the current platform"""
    if platform.system() == "Windows":
        return "pipenv"
    else:
        return "pipenv"

def run_cpp_scanner(rules_file, target_file):
    """Run the C++ scanner (mock version) on a single file with enhanced Unicode support"""
    print(f"Running C++ scanner on {target_file}...")

    python_cmd = get_python_command()

    # Use platform-specific path separators
    scanner_path = os.path.join("scanner_core", "cpp", "mock_scanner.py")

    # Create a temporary file with the target file path for cross-platform compatibility
    temp_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_path.txt")
    with open(temp_path, "w", encoding="utf-8") as f:
        f.write(target_file)

    # Create a temporary output file to avoid console encoding issues
    temp_output = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_output.json")

    # Set environment variables for proper Unicode handling
    env_vars = dict(os.environ)
    env_vars["PYTHONIOENCODING"] = "utf-8"

    if os.name == 'nt':  # Windows
        # Force UTF-8 mode on Windows
        env_vars["PYTHONUTF8"] = "1"
        # Set console code page to UTF-8
        os.system("chcp 65001 > nul")

    # Use the Python mock scanner with enhanced Unicode support
    try:
        # Use --output-file parameter to write results to a file instead of stdout
        cmd = [
            python_cmd,
            scanner_path,
            rules_file,
            "--path-file", temp_path,
            "--output-file", temp_output
        ]

        # Run the process without capturing output to avoid encoding issues
        process = subprocess.run(
            cmd,
            capture_output=False,  # Don't capture output to avoid encoding issues
            check=False,  # Don't raise exception on non-zero exit code
            env=env_vars
        )

        # Read results from the output file instead of stdout
        matches = []
        if os.path.exists(temp_output):
            try:
                with open(temp_output, "r", encoding="utf-8") as f:
                    output_data = json.load(f)
                    if "matches" in output_data:
                        for match in output_data["matches"]:
                            if "rule" in match:
                                matches.append(match["rule"])
            except Exception as e:
                print(f"Error reading output file: {e}")

        # Clean up temporary files
        for temp_file in [temp_path, temp_output]:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except Exception:
                    pass  # Ignore errors during cleanup

        return matches
    except Exception as e:
        print(f"Unexpected error running C++ scanner: {e}")

        # Clean up temporary files
        for temp_file in [temp_path, temp_output]:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except Exception:
                    pass  # Ignore errors during cleanup

        return []

def run_enhanced_scan(enable_download_monitor=True, enable_monitoring=True, enable_integration=True):
    """
    Run the enhanced pre-scanning phase with the new orchestrator

    Args:
        enable_download_monitor (bool): Whether to enable download monitoring as part of the pre-scanning phase
        enable_monitoring (bool): Whether to enable the monitoring layer as part of the pre-scanning phase
        enable_integration (bool): Whether to enable the integration layer as part of the pre-scanning phase
    """
    print(f"Running enhanced pre-scanning phase on {platform.system()}...")

    # Initialize the orchestrator
    orchestrator = Orchestrator("config.json")

    # Check if components are enabled in config
    config_loader = ConfigLoader("config.json")
    config = config_loader.get_config()
    download_monitoring_enabled = config.get("features", {}).get("monitor_downloads", False)
    monitoring_enabled = config.get("monitoring", {}).get("enabled", False)
    integration_enabled = config.get("integration", {}).get("enabled", False)

    if enable_download_monitor and download_monitoring_enabled:
        print("Download monitoring is enabled as part of the pre-scanning phase")
    else:
        print("Download monitoring is disabled or not configured")

    if enable_monitoring and monitoring_enabled:
        print("Monitoring layer is enabled as part of the pre-scanning phase")
    else:
        print("Monitoring layer is disabled or not configured")

    if enable_integration and integration_enabled:
        print("Integration layer is enabled as part of the pre-scanning phase")
    else:
        print("Integration layer is disabled or not configured")

    # Temporarily disable monitoring if requested
    if not enable_monitoring and monitoring_enabled:
        # Save the original value
        original_monitoring_enabled = config["monitoring"]["enabled"]
        # Disable monitoring
        config["monitoring"]["enabled"] = False
        # Update the config
        with open("config.json", "w") as f:
            json.dump(config, f, indent=4)
        print("Monitoring layer temporarily disabled for this scan")

    # Temporarily disable integration if requested
    if not enable_integration and integration_enabled:
        # Save the original value
        original_integration_enabled = config["integration"]["enabled"]
        # Disable integration
        config["integration"]["enabled"] = False
        # Update the config
        with open("config.json", "w") as f:
            json.dump(config, f, indent=4)
        print("Integration layer temporarily disabled for this scan")

    # Run the scan (components will be started automatically if enabled in config)
    scan_results = orchestrator.run_scan()

    # Restore original monitoring setting if we changed it
    if not enable_monitoring and monitoring_enabled:
        # Restore the original value
        config["monitoring"]["enabled"] = original_monitoring_enabled
        # Update the config
        with open("config.json", "w") as f:
            json.dump(config, f, indent=4)
        print("Monitoring layer setting restored")

    # Restore original integration setting if we changed it
    if not enable_integration and integration_enabled:
        # Restore the original value
        config["integration"]["enabled"] = original_integration_enabled
        # Update the config
        with open("config.json", "w") as f:
            json.dump(config, f, indent=4)
        print("Integration layer setting restored")

    # Return success if scan completed
    return scan_results is not None

def run_legacy_scan():
    """Run the legacy integrated scan for backward compatibility"""
    print(f"Running legacy integrated pre-scanning phase on {platform.system()}...")

    # Use absolute paths with os.path.join for cross-platform compatibility
    base_dir = os.path.dirname(os.path.abspath(__file__))
    rule_path = os.path.join(base_dir, "rules", "custom_rules.yar")

    # Load configuration
    config_loader = ConfigLoader("config.json")
    config = config_loader.get_config()

    # Get target directory from config
    target_dir = os.path.abspath(config["scanner"]["target_directory"])
    log_dir = os.path.join(base_dir, "logs")
    out_dir = os.path.join(base_dir, "output")

    # Create directories if they don't exist
    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(out_dir, exist_ok=True)

    print(f"Target directory: {target_dir}")

    # Check if target directory exists
    if not os.path.exists(target_dir):
        print(f"Error: Target directory does not exist: {target_dir}")
        return False

    # Set up logging
    logger = Logger(log_dir, "info").get_logger()
    logger.info(f"Starting legacy scan of {target_dir}")

    # Count files for progress reporting
    total_files = sum(len(files) for _, _, files in os.walk(target_dir))
    processed_files = 0

    # Scan each file in the target directory using the C++ scanner
    for root, _, files in os.walk(target_dir):
        for file in files:
            full_path = os.path.join(root, file)
            processed_files += 1

            # Skip files that exceed the maximum size
            try:
                file_size_mb = os.path.getsize(full_path) / (1024 * 1024)
                if file_size_mb > config["scanner"]["max_file_size_mb"]:
                    logger.info(f"Skipping large file: {full_path} ({file_size_mb:.2f} MB)")
                    continue
            except Exception as e:
                logger.warning(f"Error checking file size: {e}")

            # Print progress
            print(f"[{processed_files}/{total_files}] Scanning: {full_path}")

            # Scan the file
            matches = run_cpp_scanner(rule_path, full_path)

            # Log the results with proper encoding for cross-platform compatibility
            log_file = os.path.join(log_dir, "integrated_scan_log.txt")
            with open(log_file, "a", encoding="utf-8") as f:
                timestamp = datetime.now().isoformat()
                f.write(f"{timestamp} - Scanned: {full_path} - Matches: {matches}\n")

            # Save JSON output with proper encoding for cross-platform compatibility
            out_file = os.path.join(out_dir, f"{os.path.basename(full_path)}_integrated.json")
            with open(out_file, "w", encoding="utf-8") as f:
                json.dump({"file": full_path, "matches": matches}, f, indent=2)

            if matches:
                print(f"[ALERT] Found matches in {file}: {matches}")
            else:
                print(f"[INFO] No matches in {file}")

    logger.info(f"Legacy scan complete. Processed {processed_files} files.")
    return True

if __name__ == "__main__":
    import argparse

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="SBARDS Project - Pre-scanning Phase")
    parser.add_argument("--config", default="config.json", help="Path to configuration file")
    parser.add_argument("--skip-legacy", action="store_true", help="Skip legacy scan")
    parser.add_argument("--skip-cpp-test", action="store_true", help="Skip C++ scanner test")
    parser.add_argument("--download-monitor-only", action="store_true",
                        help="Run only the download monitoring component of pre-scanning")
    parser.add_argument("--no-download-monitor", action="store_true",
                        help="Disable download monitoring during pre-scanning")
    parser.add_argument("--monitoring-only", action="store_true",
                        help="Run only the monitoring layer without scanning")
    parser.add_argument("--no-monitoring", action="store_true",
                        help="Disable the monitoring layer during pre-scanning")
    parser.add_argument("--integration-only", action="store_true",
                        help="Run only the integration layer without scanning")
    parser.add_argument("--no-integration", action="store_true",
                        help="Disable the integration layer during pre-scanning")
    args = parser.parse_args()

    print(f"=== SBARDS PROJECT - PRE-SCANNING PHASE ({platform.system()}) ===")

    # Check if config file exists
    if not os.path.exists(args.config):
        print(f"Configuration file not found: {args.config}. Creating default configuration...")
        config_loader = ConfigLoader()
        config = config_loader.get_config()
        print(f"Default configuration created. Target directory: {config['scanner']['target_directory']}")
    else:
        # Load configuration
        config_loader = ConfigLoader(args.config)
        config = config_loader.get_config()

    # Initialize the orchestrator
    orchestrator = Orchestrator(args.config)

    # Check if we're only running the download monitor
    if args.download_monitor_only:
        print("\n=== Running Download Monitoring as part of Pre-scanning Phase ===")
        if config.get("features", {}).get("monitor_downloads", False):
            print("Starting download monitoring. Press Ctrl+C to stop.")
            try:
                orchestrator.start_download_monitoring()
                # Keep the script running
                import time
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\nStopping download monitoring...")
                orchestrator.stop_download_monitoring()
                print("Download monitoring stopped.")
        else:
            print("Download monitoring is not enabled in configuration.")
            print("To enable it, set 'monitor_downloads: true' in the 'features' section of config.json")
        sys.exit(0)

    # Check if we're only running the integration layer
    if args.integration_only:
        print("\n=== Running Integration Layer ===")
        if config.get("integration", {}).get("enabled", False):
            print("Starting integration layer. Press Ctrl+C to stop.")
            try:
                orchestrator.start_integration()
                # Keep the script running
                import time
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\nStopping integration layer...")
                orchestrator.stop_integration()
                print("Integration layer stopped.")
        else:
            print("Integration layer is not enabled in configuration.")
            print("To enable it, set 'enabled: true' in the 'integration' section of config.json")
        sys.exit(0)

    # Check if we're only running the monitoring layer
    if args.monitoring_only:
        print("\n=== Running Monitoring Layer ===")
        if config.get("monitoring", {}).get("enabled", False):
            print("Starting monitoring layer. Press Ctrl+C to stop.")
            try:
                # Start monitoring
                orchestrator.start_monitoring()

                # Start integration layer if available and not disabled
                if config.get("integration", {}).get("enabled", False) and not args.no_integration:
                    print("Starting integration layer to work with monitoring layer.")
                    orchestrator.start_integration()

                # Keep the script running
                import time
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\nStopping monitoring layer...")

                # Stop integration layer if it was started
                if orchestrator.layer_coordinator and orchestrator.layer_coordinator.is_running:
                    print("Stopping integration layer...")
                    orchestrator.stop_integration()

                orchestrator.stop_monitoring()
                print("Monitoring layer stopped.")
        else:
            print("Monitoring layer is not enabled in configuration.")
            print("To enable it, set 'enabled: true' in the 'monitoring' section of config.json")
        sys.exit(0)

    # Run the normal pre-scanning process
    if not args.skip_cpp_test:
        print("\n=== STEP 1: Running C++ Scanner Component ===")
        # Get a sample file to test the C++ scanner
        target_dir = os.path.abspath(config["scanner"]["target_directory"])
        rule_file = os.path.abspath(config["rules"]["rule_files"][0])

        # Find a sample file
        sample_file = None
        for root, _, files in os.walk(target_dir):
            if files:
                sample_file = os.path.join(root, files[0])
                break

        if sample_file:
            cpp_result = run_cpp_scanner(rule_file, sample_file)
            print(f"C++ Scanner Result for {sample_file}: {cpp_result}")
        else:
            print(f"No files found in {target_dir} to test the C++ scanner")

    print("\n=== STEP 2: Running Enhanced Pre-scanning Phase ===")
    # Run enhanced scan with components based on arguments
    enhanced_result = run_enhanced_scan(
        enable_download_monitor=not args.no_download_monitor,
        enable_monitoring=not args.no_monitoring,
        enable_integration=not args.no_integration
    )
    print(f"Enhanced Scan Result: {enhanced_result}")

    if not args.skip_legacy:
        print("\n=== STEP 3: Running Legacy Integrated Pre-scanning Phase ===")
        legacy_result = run_legacy_scan()
        print(f"Legacy Scan Result: {legacy_result}")

    print("\n=== Pre-scanning Phase Complete ===")
