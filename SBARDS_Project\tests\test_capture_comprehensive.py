#!/usr/bin/env python3
"""
اختبار شامل لطبقة الالتقاط - SBARDS
Comprehensive Capture Layer Test - SBARDS

يختبر هذا الملف:
- تشغيل طبقة الالتقاط
- التقاط الملفات المرفوعة
- معالجة الملفات المشبوهة
- تكامل API مع طبقة الالتقاط
- أداء النظام
"""

import os
import sys
import time
import requests
import tempfile
import threading
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_files():
    """إنشاء ملفات اختبار مختلفة"""
    test_files = {}
    
    # 1. ملف نصي عادي
    test_files['normal_text'] = {
        'name': 'test_document.txt',
        'content': 'This is a normal text document for testing SBARDS capture layer.',
        'type': 'text/plain',
        'expected_threat': False
    }
    
    # 2. ملف مشبوه (يحتوي على كلمات مفاتيح)
    test_files['suspicious_text'] = {
        'name': 'suspicious_file.txt',
        'content': 'encrypt decrypt ransom bitcoin payment virus malware trojan',
        'type': 'text/plain',
        'expected_threat': True
    }
    
    # 3. ملف تنفيذي وهمي
    test_files['fake_executable'] = {
        'name': 'malware.exe',
        'content': b'MZ\x90\x00\x03\x00\x00\x00\x04\x00\x00\x00\xff\xff\x00\x00',
        'type': 'application/octet-stream',
        'expected_threat': True
    }
    
    # 4. ملف صورة عادي
    test_files['normal_image'] = {
        'name': 'photo.jpg',
        'content': b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00',
        'type': 'image/jpeg',
        'expected_threat': False
    }
    
    # 5. ملف مضغوط مشبوه
    test_files['suspicious_archive'] = {
        'name': 'encrypted_files.zip',
        'content': b'PK\x03\x04\x14\x00\x00\x00\x08\x00encrypt_all_files.bat',
        'type': 'application/zip',
        'expected_threat': True
    }
    
    return test_files

def test_api_server():
    """اختبار أن API server يعمل"""
    print("🌐 اختبار API Server...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API Server يعمل بشكل صحيح")
            return True
        else:
            print(f"❌ API Server يعطي خطأ: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ لا يمكن الوصول إلى API Server: {e}")
        return False

def test_file_upload(file_info):
    """اختبار رفع ملف واحد"""
    try:
        # تحضير الملف للرفع
        if isinstance(file_info['content'], str):
            content = file_info['content'].encode('utf-8')
        else:
            content = file_info['content']
        
        files = {
            'file': (file_info['name'], content, file_info['type'])
        }
        
        # رفع الملف
        response = requests.post(
            "http://127.0.0.1:8000/api/upload",
            files=files,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ تم رفع {file_info['name']} بنجاح")
            print(f"   الحجم: {result['file_info']['size']} بايت")
            return True, result
        else:
            print(f"❌ فشل رفع {file_info['name']}: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ خطأ في رفع {file_info['name']}: {e}")
        return False, None

def test_capture_status():
    """اختبار حالة طبقة الالتقاط"""
    print("\n📁 اختبار حالة طبقة الالتقاط...")
    
    try:
        # اختبار JSON endpoint
        response = requests.get("http://127.0.0.1:8000/api/capture/status", timeout=5)
        if response.status_code == 200:
            status_data = response.json()
            print(f"✅ حالة طبقة الالتقاط: {status_data.get('status', 'unknown')}")
            return True, status_data
        else:
            print(f"❌ خطأ في الحصول على حالة طبقة الالتقاط: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ خطأ في اختبار حالة طبقة الالتقاط: {e}")
        return False, None

def test_health_check():
    """اختبار فحص صحة النظام"""
    print("\n🏥 اختبار فحص صحة النظام...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ حالة النظام: {health_data.get('status', 'unknown')}")
            print(f"   الإصدار: {health_data.get('version', 'unknown')}")
            print(f"   طبقة الالتقاط: {health_data.get('capture_layer', 'unknown')}")
            return True, health_data
        else:
            print(f"❌ خطأ في فحص صحة النظام: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ خطأ في فحص صحة النظام: {e}")
        return False, None

def test_performance():
    """اختبار أداء النظام"""
    print("\n⚡ اختبار أداء النظام...")
    
    # اختبار زمن الاستجابة
    start_time = time.time()
    try:
        response = requests.get("http://127.0.0.1:8000/api/health", timeout=5)
        end_time = time.time()
        
        if response.status_code == 200:
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            print(f"✅ زمن الاستجابة: {response_time:.2f} مللي ثانية")
            
            if response_time < 100:
                print("🚀 أداء ممتاز!")
            elif response_time < 500:
                print("✅ أداء جيد")
            else:
                print("⚠️ أداء بطيء")
                
            return True, response_time
        else:
            print(f"❌ خطأ في اختبار الأداء: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {e}")
        return False, None

def run_comprehensive_test():
    """تشغيل الاختبار الشامل"""
    print("🛡️ SBARDS Capture Layer - Comprehensive Test")
    print("=" * 70)
    print("اختبار شامل لطبقة الالتقاط ونظام SBARDS")
    print("=" * 70)
    
    # إحصائيات الاختبار
    tests_passed = 0
    tests_total = 0
    upload_results = []
    
    # 1. اختبار API Server
    tests_total += 1
    if test_api_server():
        tests_passed += 1
    else:
        print("❌ لا يمكن المتابعة بدون API Server")
        return
    
    # 2. اختبار فحص الصحة
    tests_total += 1
    health_success, health_data = test_health_check()
    if health_success:
        tests_passed += 1
    
    # 3. اختبار حالة طبقة الالتقاط
    tests_total += 1
    capture_success, capture_data = test_capture_status()
    if capture_success:
        tests_passed += 1
    
    # 4. اختبار الأداء
    tests_total += 1
    perf_success, response_time = test_performance()
    if perf_success:
        tests_passed += 1
    
    # 5. اختبار رفع الملفات
    print("\n📤 اختبار رفع الملفات...")
    test_files = create_test_files()
    
    for file_type, file_info in test_files.items():
        tests_total += 1
        print(f"\n📄 اختبار ملف: {file_info['name']}")
        
        success, result = test_file_upload(file_info)
        if success:
            tests_passed += 1
            upload_results.append({
                'file_type': file_type,
                'file_name': file_info['name'],
                'expected_threat': file_info['expected_threat'],
                'result': result,
                'success': True
            })
        else:
            upload_results.append({
                'file_type': file_type,
                'file_name': file_info['name'],
                'expected_threat': file_info['expected_threat'],
                'result': None,
                'success': False
            })
    
    # عرض النتائج
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار الشامل:")
    print("=" * 70)
    
    print(f"🧪 إجمالي الاختبارات: {tests_total}")
    print(f"✅ اختبارات نجحت: {tests_passed}")
    print(f"❌ اختبارات فشلت: {tests_total - tests_passed}")
    print(f"📈 معدل النجاح: {(tests_passed/tests_total)*100:.1f}%")
    
    if health_data:
        print(f"\n🏥 حالة النظام:")
        print(f"   الحالة: {health_data.get('status', 'unknown')}")
        print(f"   الإصدار: {health_data.get('version', 'unknown')}")
        print(f"   طبقة الالتقاط: {health_data.get('capture_layer', 'unknown')}")
    
    if response_time:
        print(f"\n⚡ الأداء:")
        print(f"   زمن الاستجابة: {response_time:.2f} مللي ثانية")
    
    print(f"\n📤 نتائج رفع الملفات:")
    for result in upload_results:
        status = "✅" if result['success'] else "❌"
        threat_indicator = "🚨" if result['expected_threat'] else "✅"
        print(f"   {status} {result['file_name']} {threat_indicator}")
    
    # تقييم النتيجة الإجمالية
    print("\n" + "=" * 70)
    if tests_passed == tests_total:
        print("🎉 جميع الاختبارات نجحت! طبقة الالتقاط تعمل بشكل مثالي")
    elif tests_passed >= tests_total * 0.8:
        print("✅ معظم الاختبارات نجحت! طبقة الالتقاط تعمل بشكل جيد")
    else:
        print("⚠️ بعض الاختبارات فشلت. طبقة الالتقاط تحتاج مراجعة")
    
    print("=" * 70)
    
    return tests_passed, tests_total, upload_results

if __name__ == "__main__":
    run_comprehensive_test()
