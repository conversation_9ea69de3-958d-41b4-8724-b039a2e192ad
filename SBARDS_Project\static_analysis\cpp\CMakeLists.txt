# CMakeLists.txt for SBARDS Static Analysis Layer C++ Components
# High-performance signature checking, entropy analysis, and hash generation

cmake_minimum_required(VERSION 3.16)
project(SBARDS_StaticAnalysis VERSION 2.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    # Visual Studio
    add_compile_options(/W4 /WX /permissive-)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
else()
    # GCC/Clang
    add_compile_options(-Wall -Wextra -Werror -pedantic)
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG -march=native")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g3 -fsanitize=address")
endif()

# Find required packages
find_package(Threads REQUIRED)
find_package(OpenSSL REQUIRED)

# Platform-specific libraries
if(WIN32)
    set(PLATFORM_LIBS shlwapi advapi32 crypt32 ws2_32)
else()
    set(PLATFORM_LIBS pthread dl)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${OPENSSL_INCLUDE_DIR})

# Signature Checker Library
add_library(sbards_signature_checker SHARED
    signature_checker.cpp
)

target_link_libraries(sbards_signature_checker
    ${PLATFORM_LIBS}
    Threads::Threads
)

set_target_properties(sbards_signature_checker PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
    PUBLIC_HEADER "signature_checker.h"
)

# Entropy Checker Library
add_library(sbards_entropy_checker SHARED
    entropy_checker.cpp
)

target_link_libraries(sbards_entropy_checker
    ${PLATFORM_LIBS}
    Threads::Threads
)

set_target_properties(sbards_entropy_checker PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
    PUBLIC_HEADER "entropy_checker.h"
)

# Permission Analyzer Library
add_library(sbards_permission_analyzer SHARED
    permission_analyzer.cpp
)

target_link_libraries(sbards_permission_analyzer
    ${PLATFORM_LIBS}
    Threads::Threads
)

set_target_properties(sbards_permission_analyzer PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
    PUBLIC_HEADER "permission_analyzer.h"
)

# Hash Generator Library
add_library(sbards_hash_generator SHARED
    hash_generator.cpp
)

target_link_libraries(sbards_hash_generator
    ${PLATFORM_LIBS}
    ${OPENSSL_LIBRARIES}
    Threads::Threads
)

set_target_properties(sbards_hash_generator PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
    PUBLIC_HEADER "hash_generator.h"
)

# Combined Static Analysis Library
add_library(sbards_static_analysis SHARED
    signature_checker.cpp
    permission_analyzer.cpp
    entropy_checker.cpp
    hash_generator.cpp
)

target_link_libraries(sbards_static_analysis
    ${PLATFORM_LIBS}
    ${OPENSSL_LIBRARIES}
    Threads::Threads
)

set_target_properties(sbards_static_analysis PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
)

# Test Executables
add_executable(signature_checker_test
    signature_checker.cpp
)

target_link_libraries(signature_checker_test
    ${PLATFORM_LIBS}
    Threads::Threads
)

target_compile_definitions(signature_checker_test PRIVATE
    STANDALONE_EXECUTABLE
)

add_executable(entropy_checker_test
    entropy_checker.cpp
)

target_link_libraries(entropy_checker_test
    ${PLATFORM_LIBS}
    Threads::Threads
)

target_compile_definitions(entropy_checker_test PRIVATE
    STANDALONE_EXECUTABLE
)

add_executable(permission_analyzer_test
    permission_analyzer.cpp
)

target_link_libraries(permission_analyzer_test
    ${PLATFORM_LIBS}
    Threads::Threads
)

target_compile_definitions(permission_analyzer_test PRIVATE
    STANDALONE_EXECUTABLE
)

add_executable(hash_generator_test
    hash_generator.cpp
)

target_link_libraries(hash_generator_test
    ${PLATFORM_LIBS}
    ${OPENSSL_LIBRARIES}
    Threads::Threads
)

target_compile_definitions(hash_generator_test PRIVATE
    STANDALONE_EXECUTABLE
)

# Benchmark executable
add_executable(static_analysis_benchmark
    signature_checker.cpp
    entropy_checker.cpp
    hash_generator.cpp
    benchmark.cpp
)

target_link_libraries(static_analysis_benchmark
    ${PLATFORM_LIBS}
    ${OPENSSL_LIBRARIES}
    Threads::Threads
)

target_compile_definitions(static_analysis_benchmark PRIVATE
    BENCHMARK_MODE
)

# Installation rules
install(TARGETS
    sbards_signature_checker
    sbards_entropy_checker
    sbards_hash_generator
    sbards_static_analysis
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    PUBLIC_HEADER DESTINATION include/sbards
)

install(TARGETS
    signature_checker_test
    entropy_checker_test
    hash_generator_test
    static_analysis_benchmark
    RUNTIME DESTINATION bin
)

# Create header files for libraries
file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/signature_checker.h
"#ifndef SBARDS_SIGNATURE_CHECKER_H
#define SBARDS_SIGNATURE_CHECKER_H

#ifdef __cplusplus
extern \"C\" {
#endif

// C interface for Python integration
void* create_signature_checker();
void destroy_signature_checker(void* checker);
const char* check_file_signature(void* checker, const char* file_path);

#ifdef __cplusplus
}
#endif

#endif // SBARDS_SIGNATURE_CHECKER_H
")

file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/entropy_checker.h
"#ifndef SBARDS_ENTROPY_CHECKER_H
#define SBARDS_ENTROPY_CHECKER_H

#ifdef __cplusplus
extern \"C\" {
#endif

// C interface for Python integration
void* create_entropy_checker();
void destroy_entropy_checker(void* checker);
const char* analyze_file_entropy(void* checker, const char* file_path);

#ifdef __cplusplus
}
#endif

#endif // SBARDS_ENTROPY_CHECKER_H
")

file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/hash_generator.h
"#ifndef SBARDS_HASH_GENERATOR_H
#define SBARDS_HASH_GENERATOR_H

#ifdef __cplusplus
extern \"C\" {
#endif

// C interface for Python integration
void* create_hash_generator();
void destroy_hash_generator(void* generator);
const char* calculate_sha256(void* generator, const char* file_path);
const char* calculate_md5(void* generator, const char* file_path);
const char* calculate_all_hashes(void* generator, const char* file_path);

#ifdef __cplusplus
}
#endif

#endif // SBARDS_HASH_GENERATOR_H
")

# Create benchmark source file
file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/benchmark.cpp
"#include <iostream>
#include <chrono>
#include <vector>
#include <string>

// Include the analysis components
// Note: In practice, these would be proper includes
extern void* create_signature_checker();
extern void destroy_signature_checker(void* checker);
extern const char* check_file_signature(void* checker, const char* file_path);

extern void* create_entropy_checker();
extern void destroy_entropy_checker(void* checker);
extern const char* analyze_file_entropy(void* checker, const char* file_path);

extern void* create_hash_generator();
extern void destroy_hash_generator(void* generator);
extern const char* calculate_all_hashes(void* generator, const char* file_path);

int main() {
    std::cout << \"SBARDS Static Analysis Benchmark\" << std::endl;
    std::cout << \"=================================\" << std::endl;

    // Test files
    std::vector<std::string> test_files = {
        \"test.txt\", \"test.exe\", \"test.pdf\", \"test.zip\"
    };

    // Initialize components
    void* sig_checker = create_signature_checker();
    void* entropy_checker = create_entropy_checker();
    void* hash_generator = create_hash_generator();

    for (const auto& file : test_files) {
        std::cout << \"\\nBenchmarking file: \" << file << std::endl;

        // Signature checking benchmark
        auto start = std::chrono::high_resolution_clock::now();
        const char* sig_result = check_file_signature(sig_checker, file.c_str());
        auto end = std::chrono::high_resolution_clock::now();
        auto sig_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        std::cout << \"Signature check: \" << sig_time.count() << \" μs\" << std::endl;

        // Entropy analysis benchmark
        start = std::chrono::high_resolution_clock::now();
        const char* entropy_result = analyze_file_entropy(entropy_checker, file.c_str());
        end = std::chrono::high_resolution_clock::now();
        auto entropy_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        std::cout << \"Entropy analysis: \" << entropy_time.count() << \" μs\" << std::endl;

        // Hash generation benchmark
        start = std::chrono::high_resolution_clock::now();
        const char* hash_result = calculate_all_hashes(hash_generator, file.c_str());
        end = std::chrono::high_resolution_clock::now();
        auto hash_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        std::cout << \"Hash generation: \" << hash_time.count() << \" μs\" << std::endl;

        // Total time
        auto total_time = sig_time + entropy_time + hash_time;
        std::cout << \"Total analysis time: \" << total_time.count() << \" μs\" << std::endl;
    }

    // Cleanup
    destroy_signature_checker(sig_checker);
    destroy_entropy_checker(entropy_checker);
    destroy_hash_generator(hash_generator);

    return 0;
}
")

# Copy generated files
configure_file(${CMAKE_CURRENT_BINARY_DIR}/signature_checker.h
               ${CMAKE_CURRENT_SOURCE_DIR}/signature_checker.h COPYONLY)
configure_file(${CMAKE_CURRENT_BINARY_DIR}/entropy_checker.h
               ${CMAKE_CURRENT_SOURCE_DIR}/entropy_checker.h COPYONLY)
configure_file(${CMAKE_CURRENT_BINARY_DIR}/hash_generator.h
               ${CMAKE_CURRENT_SOURCE_DIR}/hash_generator.h COPYONLY)
configure_file(${CMAKE_CURRENT_BINARY_DIR}/benchmark.cpp
               ${CMAKE_CURRENT_SOURCE_DIR}/benchmark.cpp COPYONLY)

# Testing
enable_testing()

add_test(NAME signature_checker_basic_test
    COMMAND signature_checker_test
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

add_test(NAME entropy_checker_basic_test
    COMMAND entropy_checker_test
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

add_test(NAME hash_generator_basic_test
    COMMAND hash_generator_test
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

add_test(NAME static_analysis_benchmark_test
    COMMAND static_analysis_benchmark
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Custom targets for development
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --verbose
    DEPENDS signature_checker_test entropy_checker_test hash_generator_test
)

add_custom_target(benchmark
    COMMAND echo \"Running performance benchmarks...\"
    COMMAND static_analysis_benchmark
    DEPENDS static_analysis_benchmark
)

add_custom_target(performance_test
    COMMAND echo \"Running performance tests...\"
    COMMAND signature_checker_test
    COMMAND entropy_checker_test
    COMMAND hash_generator_test
    COMMAND static_analysis_benchmark
    DEPENDS signature_checker_test entropy_checker_test hash_generator_test static_analysis_benchmark
)

# Package configuration
set(CPACK_PACKAGE_NAME \"SBARDS-StaticAnalysis\")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY \"SBARDS Static Analysis Layer - High Performance File Analysis\")
set(CPACK_PACKAGE_VENDOR \"SBARDS Project\")

if(WIN32)
    set(CPACK_GENERATOR \"ZIP;NSIS\")
else()
    set(CPACK_GENERATOR \"TGZ;DEB\")
endif()

include(CPack)

# Print configuration summary
message(STATUS \"\")
message(STATUS \"SBARDS Static Analysis Layer Configuration Summary:\")
message(STATUS \"=================================================\")
message(STATUS \"Build type: ${CMAKE_BUILD_TYPE}\")
message(STATUS \"C++ standard: ${CMAKE_CXX_STANDARD}\")
message(STATUS \"Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}\")
message(STATUS \"Platform: ${CMAKE_SYSTEM_NAME} ${CMAKE_SYSTEM_VERSION}\")
message(STATUS \"Architecture: ${CMAKE_SYSTEM_PROCESSOR}\")
message(STATUS \"Install prefix: ${CMAKE_INSTALL_PREFIX}\")
message(STATUS \"OpenSSL version: ${OPENSSL_VERSION}\")
message(STATUS \"\")
message(STATUS \"Components to build:\")
message(STATUS \"  - Signature Checker Library: sbards_signature_checker\")
message(STATUS \"  - Entropy Checker Library: sbards_entropy_checker\")
message(STATUS \"  - Hash Generator Library: sbards_hash_generator\")
message(STATUS \"  - Combined Static Analysis Library: sbards_static_analysis\")
message(STATUS \"  - Test Executables: signature_checker_test, entropy_checker_test, hash_generator_test\")
message(STATUS \"  - Benchmark Executable: static_analysis_benchmark\")
message(STATUS \"\")
message(STATUS \"Build commands:\")
message(STATUS \"  mkdir build && cd build\")
message(STATUS \"  cmake ..\")
message(STATUS \"  cmake --build . --config ${CMAKE_BUILD_TYPE}\")
message(STATUS \"  ctest --verbose\")
message(STATUS \"  cmake --build . --target benchmark\")
message(STATUS \"\")
