2025-05-24 09:54:11,080 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: custom
2025-05-24 09:54:11,080 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: malware
2025-05-24 09:54:11,081 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: ransomware
2025-05-24 09:54:11,082 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: permissions
2025-05-24 09:54:11,083 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: network
2025-05-24 09:54:11,084 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: crypto
2025-05-24 09:54:11,084 - layer.static_analysis - INFO - [yara_scanner.py:121] - _load_all_rules() - Loading YARA rules...
2025-05-24 09:54:11,085 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: custom
2025-05-24 09:54:11,086 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: malware
2025-05-24 09:54:11,087 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: ransomware
2025-05-24 09:54:11,087 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: permissions
2025-05-24 09:54:11,088 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: network
2025-05-24 09:54:11,088 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: crypto
2025-05-24 09:54:11,089 - layer.static_analysis - INFO - [yara_scanner.py:135] - _load_all_rules() - Total YARA rules loaded: 0
2025-05-24 09:54:11,089 - layer.static_analysis - INFO - [yara_scanner.py:308] - _initialize_cpp_integration() - C++ integration initialized (placeholder)
2025-05-24 09:54:11,089 - layer.static_analysis - INFO - [yara_scanner.py:301] - __init__() - YaraScanner initialized with enhanced capabilities
2025-05-24 09:54:11,115 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: custom
2025-05-24 09:54:11,116 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: malware
2025-05-24 09:54:11,117 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: ransomware
2025-05-24 09:54:11,117 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: permissions
2025-05-24 09:54:11,118 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: network
2025-05-24 09:54:11,119 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: crypto
2025-05-24 09:54:11,120 - layer.static_analysis - INFO - [yara_scanner.py:121] - _load_all_rules() - Loading YARA rules...
2025-05-24 09:54:11,120 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: custom
2025-05-24 09:54:11,121 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: malware
2025-05-24 09:54:11,122 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: ransomware
2025-05-24 09:54:11,122 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: permissions
2025-05-24 09:54:11,123 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: network
2025-05-24 09:54:11,123 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: crypto
2025-05-24 09:54:11,140 - layer.static_analysis - INFO - [yara_scanner.py:135] - _load_all_rules() - Total YARA rules loaded: 1
2025-05-24 09:54:11,140 - layer.static_analysis - INFO - [yara_scanner.py:308] - _initialize_cpp_integration() - C++ integration initialized (placeholder)
2025-05-24 09:54:11,140 - layer.static_analysis - INFO - [yara_scanner.py:301] - __init__() - YaraScanner initialized with enhanced capabilities
2025-05-24 09:54:11,151 - layer.static_analysis - ERROR - [yara_scanner.py:437] - scan_file() - Error scanning file C:\Users\<USER>\AppData\Local\Temp\tmp9ms8l3pi\clean_file.txt: '>=' not supported between instances of 'ThreatLevel' and 'ThreatLevel'
2025-05-24 09:54:35,713 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: custom
2025-05-24 09:54:35,714 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: malware
2025-05-24 09:54:35,716 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: ransomware
2025-05-24 09:54:35,717 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: permissions
2025-05-24 09:54:35,717 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: network
2025-05-24 09:54:35,718 - layer.static_analysis - INFO - [yara_scanner.py:113] - _initialize_categories() - Created category directory: crypto
2025-05-24 09:54:35,718 - layer.static_analysis - INFO - [yara_scanner.py:121] - _load_all_rules() - Loading YARA rules...
2025-05-24 09:54:35,719 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: custom
2025-05-24 09:54:35,720 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: malware
2025-05-24 09:54:35,721 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: ransomware
2025-05-24 09:54:35,722 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: permissions
2025-05-24 09:54:35,722 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: network
2025-05-24 09:54:35,723 - layer.static_analysis - INFO - [yara_scanner.py:129] - _load_all_rules() - Loaded 0 rules for category: crypto
2025-05-24 09:54:35,751 - layer.static_analysis - INFO - [yara_scanner.py:135] - _load_all_rules() - Total YARA rules loaded: 2
2025-05-24 09:54:35,751 - layer.static_analysis - INFO - [yara_scanner.py:308] - _initialize_cpp_integration() - C++ integration initialized (placeholder)
2025-05-24 09:54:35,752 - layer.static_analysis - INFO - [yara_scanner.py:301] - __init__() - YaraScanner initialized with enhanced capabilities
2025-05-24 09:54:35,762 - layer.static_analysis - ERROR - [yara_scanner.py:437] - scan_file() - Error scanning file C:\Users\<USER>\AppData\Local\Temp\tmp21hgl_w5\clean_file.txt: '>=' not supported between instances of 'ThreatLevel' and 'ThreatLevel'
2025-05-24 09:54:35,815 - layer.static_analysis - ERROR - [yara_scanner.py:437] - scan_file() - Error scanning file C:\Users\<USER>\AppData\Local\Temp\tmp21hgl_w5\large_file.txt: '>=' not supported between instances of 'ThreatLevel' and 'ThreatLevel'
