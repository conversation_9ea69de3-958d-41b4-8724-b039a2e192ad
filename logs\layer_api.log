2025-05-26 05:50:22,265 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 05:50:22,335 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 05:50:22,631 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 05:50:22,857 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 05:50:22,858 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
