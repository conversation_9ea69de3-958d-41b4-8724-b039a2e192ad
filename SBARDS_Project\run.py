#!/usr/bin/env python3
"""
🚀 SBARDS v2.0 - Main Entry Point for Enhanced Architecture

This is the primary execution file for the new multi-layer SBARDS project.
Supports all layers: capture, static_analysis, dynamic_analysis, response,
external_integration, memory_protection, monitoring, api, ui, data, security.

Migration Status: ✅ ENHANCED for new architecture
Migration Date: 2025-05-25
Features: Multi-layer coordination, enhanced performance, comprehensive monitoring.
"""

import os
import sys
import argparse
import asyncio
import signal
import time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

# Import core components
from core.config import ConfigLoader
from core.logger import get_global_logger
from core.utils import SystemUtils, PerformanceUtils
from core.constants import PROJECT_NAME, PROJECT_VERSION

class SBARDSRunner:
    """
    Main runner for the SBARDS system.
    Manages all layers and their interactions.
    """

    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the SBARDS runner.

        Args:
            config_path (str): Path to configuration file
        """
        self.config_path = config_path
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.get_config()

        # Setup logging
        self.logger = get_global_logger()
        self.log = self.logger.get_layer_logger("main")

        # Initialize layer managers
        self.layer_managers = {}
        self.running = False

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.log.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False

    def _initialize_layers(self, enabled_layers: List[str] = None):
        """
        Initialize enabled layers.

        Args:
            enabled_layers (List[str]): List of layers to enable
        """
        if enabled_layers is None:
            enabled_layers = self._get_enabled_layers()

        self.log.info(f"Initializing layers: {enabled_layers}")

        for layer in enabled_layers:
            try:
                self._initialize_layer(layer)
                self.log.info(f"Successfully initialized {layer} layer")
            except Exception as e:
                self.log.error(f"Failed to initialize {layer} layer: {e}")

    def _get_enabled_layers(self) -> List[str]:
        """Get list of enabled layers from configuration."""
        enabled_layers = []

        layer_configs = [
            ("capture", self.config.get("capture", {}).get("enabled", False)),
            ("static_analysis", self.config.get("static_analysis", {}).get("enabled", False)),
            ("dynamic_analysis", self.config.get("dynamic_analysis", {}).get("enabled", False)),
            ("response", self.config.get("response", {}).get("enabled", False)),
            ("external_integration", self.config.get("external_integration", {}).get("enabled", False)),
            ("memory_protection", self.config.get("memory_protection", {}).get("enabled", False)),
            ("monitoring", self.config.get("monitoring", {}).get("enabled", False)),
            ("api", True),  # API is always enabled
            ("ui", self.config.get("ui", {}).get("dashboard_enabled", False)),
            ("data", True),  # Data layer is always enabled
            ("security", self.config.get("security", {}).get("integrity_check", False))
        ]

        for layer_name, is_enabled in layer_configs:
            if is_enabled:
                enabled_layers.append(layer_name)

        return enabled_layers

    def _initialize_layer(self, layer_name: str):
        """
        Initialize a specific layer.

        Args:
            layer_name (str): Name of the layer to initialize
        """
        self.log.info(f"Initializing {layer_name} layer...")

        if layer_name == "capture":
            self._initialize_capture_layer()
        elif layer_name == "static_analysis":
            self._initialize_static_analysis_layer()
        elif layer_name == "dynamic_analysis":
            self._initialize_dynamic_analysis_layer()
        elif layer_name == "response":
            self._initialize_response_layer()
        elif layer_name == "external_integration":
            self._initialize_external_integration_layer()
        elif layer_name == "memory_protection":
            self._initialize_memory_protection_layer()
        elif layer_name == "monitoring":
            self._initialize_monitoring_layer()
        elif layer_name == "api":
            self._initialize_api_layer()
        elif layer_name == "ui":
            self._initialize_ui_layer()
        elif layer_name == "data":
            self._initialize_data_layer()
        elif layer_name == "security":
            self._initialize_security_layer()

    def _initialize_capture_layer(self):
        """Initialize True Capture Layer with enhanced functionality."""
        try:
            from capture.python.integrated_capture_layer import create_integrated_capture_layer
            from capture.python.redis_queue import RedisQueueManager

            self.log.info("Initializing True Capture Layer components...")

            # Get capture configuration
            capture_config = self.config_loader.get_layer_config("capture")

            # Initialize True Integrated Capture Layer
            integrated_config = {
                "temp_storage_path": capture_config.get("temp_storage_path", "capture/temp_storage"),
                "use_mock_cpp": capture_config.get("use_mock_cpp", True),  # استخدام mock للتطوير
                "max_file_size_mb": capture_config.get("max_file_size_mb", 100),
                "enable_real_time": capture_config.get("enable_real_time", True),
                "monitor_browsers": capture_config.get("monitor_browsers", True),
                "monitor_social_media": capture_config.get("monitor_social_media", True),
                "monitor_cloud_storage": capture_config.get("monitor_cloud_storage", True),
                "monitor_email": capture_config.get("monitor_email", True),
                "monitor_usb": capture_config.get("monitor_usb", True)
            }

            # Create integrated capture layer
            integrated_capture_layer = create_integrated_capture_layer(integrated_config)

            # Set static analysis callback (placeholder for now)
            def static_analysis_callback(file_info):
                """Placeholder callback for static analysis integration"""
                self.log.info(f"Static analysis request for: {file_info.get('file_path', 'unknown')}")
                # TODO: Integrate with actual static analysis layer when implemented
                # For now, treat all files as safe
                return {"threat_level": "safe", "details": "Static analysis not yet implemented"}

            integrated_capture_layer.set_static_analysis_callback(static_analysis_callback)

            # Start integrated capture layer
            if integrated_capture_layer.start():
                self.log.info("True Capture Layer started successfully")
            else:
                self.log.error("Failed to start True Capture Layer")
                raise Exception("Failed to start True Capture Layer")

            # Initialize Redis queue if enabled (optional)
            redis_queue = None
            redis_config = self.config_loader.get_section("redis")
            if redis_config.get("enabled", False):
                try:
                    redis_queue_config = {
                        "redis_host": redis_config.get("host", "localhost"),
                        "redis_port": redis_config.get("port", 6379),
                        "redis_db": redis_config.get("db", 0),
                        "queue_prefix": "sbards_capture",
                        "max_retries": 3
                    }
                    redis_queue = RedisQueueManager(redis_queue_config)

                    if redis_queue.is_available():
                        # Register file event consumer
                        def process_file_event(data):
                            try:
                                self.log.info(f"Processing file event via Redis: {data.get('file_path', 'unknown')}")
                                # TODO: Forward to static analysis layer when implemented
                                return True
                            except Exception as e:
                                self.log.error(f"Error processing file event: {e}")
                                return False

                        redis_queue.register_consumer("file_event", process_file_event)
                        redis_queue.start(worker_count=2)
                        self.log.info("Redis queue manager started")
                    else:
                        self.log.warning("Redis not available - using direct processing")
                        redis_queue = None

                except Exception as e:
                    self.log.warning(f"Redis initialization failed: {e}")
                    redis_queue = None

            # Store layer components
            self.layer_managers["capture"] = {
                "status": "initialized",
                "integrated_capture_layer": integrated_capture_layer,
                "redis_queue": redis_queue,
                "running": True,
                "type": "true_capture_layer"
            }

            self.log.info("SUCCESS: True Capture Layer initialized successfully with enhanced functionality")

        except ImportError as e:
            self.log.warning(f"Capture layer components not available: {e}")
            self.layer_managers["capture"] = {"status": "unavailable", "error": str(e)}
        except Exception as e:
            self.log.error(f"Failed to initialize capture layer: {e}")
            self.layer_managers["capture"] = {"status": "failed", "error": str(e)}

    def _initialize_static_analysis_layer(self):
        """Initialize static analysis layer."""
        try:
            # Import static analysis components
            # Note: These will be implemented in the next steps
            self.log.info("Static analysis layer initialized (placeholder)")
            self.layer_managers["static_analysis"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"Static analysis layer not available: {e}")

    def _initialize_dynamic_analysis_layer(self):
        """Initialize dynamic analysis layer."""
        try:
            # Import dynamic analysis components
            # Note: These will be implemented in the next steps
            self.log.info("Dynamic analysis layer initialized (placeholder)")
            self.layer_managers["dynamic_analysis"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"Dynamic analysis layer not available: {e}")

    def _initialize_response_layer(self):
        """Initialize response layer."""
        try:
            # Import response layer components
            # Note: These will be implemented in the next steps
            self.log.info("Response layer initialized (placeholder)")
            self.layer_managers["response"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"Response layer not available: {e}")

    def _initialize_external_integration_layer(self):
        """Initialize external integration layer."""
        try:
            # Import external integration components
            # Note: These will be implemented in the next steps
            self.log.info("External integration layer initialized (placeholder)")
            self.layer_managers["external_integration"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"External integration layer not available: {e}")

    def _initialize_memory_protection_layer(self):
        """Initialize memory protection layer."""
        try:
            # Import memory protection components
            # Note: These will be implemented in the next steps
            self.log.info("Memory protection layer initialized (placeholder)")
            self.layer_managers["memory_protection"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"Memory protection layer not available: {e}")

    def _initialize_monitoring_layer(self):
        """Initialize monitoring layer."""
        try:
            # Import monitoring components
            # Note: These will be implemented in the next steps
            self.log.info("Monitoring layer initialized (placeholder)")
            self.layer_managers["monitoring"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"Monitoring layer not available: {e}")

    def _initialize_api_layer(self):
        """Initialize API layer with FastAPI."""
        try:
            import uvicorn
            from api.main import app

            self.log.info("Initializing API layer with FastAPI...")

            # Get API configuration
            api_config = self.config_loader.get_section("api")

            # Initialize capture layer for API if not already done
            if "capture" not in self.layer_managers:
                self._initialize_capture_layer()

            # Store API configuration
            self.layer_managers["api"] = {
                "status": "initialized",
                "app": app,
                "host": api_config.get("host", "127.0.0.1"),
                "port": api_config.get("port", 8000),
                "running": False,
                "endpoints": {
                    "dashboard": "/dashboard",
                    "health": "/api/health/page",
                    "capture_status": "/api/capture/status/page",
                    "monitoring_info": "/api/capture/monitoring-info",
                    "docs": "/api/docs",
                    "upload": "/api/upload"
                }
            }

            self.log.info("API layer initialized successfully")

        except ImportError as e:
            self.log.warning(f"API layer components not available: {e}")
            self.layer_managers["api"] = {"status": "unavailable", "error": str(e)}
        except Exception as e:
            self.log.error(f"Failed to initialize API layer: {e}")
            self.layer_managers["api"] = {"status": "failed", "error": str(e)}

    def _initialize_ui_layer(self):
        """Initialize UI layer."""
        try:
            # Import UI components
            # Note: These will be implemented in the next steps
            self.log.info("UI layer initialized (placeholder)")
            self.layer_managers["ui"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"UI layer not available: {e}")

    def _initialize_data_layer(self):
        """Initialize data layer."""
        try:
            # Import data layer components
            # Note: These will be implemented in the next steps
            self.log.info("Data layer initialized (placeholder)")
            self.layer_managers["data"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"Data layer not available: {e}")

    def _initialize_security_layer(self):
        """Initialize security layer."""
        try:
            # Import security layer components
            # Note: These will be implemented in the next steps
            self.log.info("Security layer initialized (placeholder)")
            self.layer_managers["security"] = {"status": "initialized"}
        except ImportError as e:
            self.log.warning(f"Security layer not available: {e}")

    def run_single_layer(self, layer_name: str, **kwargs):
        """
        Run a single layer.

        Args:
            layer_name (str): Name of the layer to run
            **kwargs: Additional arguments for the layer
        """
        self.log.info(f"Running {layer_name} layer only")

        # Initialize only the specified layer
        self._initialize_layers([layer_name])

        # Run the layer
        if layer_name in self.layer_managers:
            manager = self.layer_managers[layer_name]

            if manager.get("status") == "initialized":
                self.log.info(f"{layer_name} layer is running...")
                self.running = True

                try:
                    if layer_name == "api":
                        self._run_api_layer(manager, **kwargs)
                    elif layer_name == "capture":
                        self._run_capture_layer(manager, **kwargs)
                    else:
                        # Default running logic for other layers
                        while self.running:
                            time.sleep(1)
                            self._monitor_layer(layer_name, manager)

                except KeyboardInterrupt:
                    self.log.info(f"Stopping {layer_name} layer")
                    self.running = False
                finally:
                    self._shutdown_single_layer(layer_name, manager)
            else:
                self.log.error(f"Layer {layer_name} not properly initialized: {manager.get('status', 'unknown')}")
        else:
            self.log.error(f"Failed to initialize {layer_name} layer")

    def _run_api_layer(self, manager, **kwargs):
        """Run the API layer with FastAPI."""
        try:
            import uvicorn

            host = kwargs.get("host", manager.get("host", "127.0.0.1"))
            port = kwargs.get("port", manager.get("port", 8000))

            self.log.info(f"Starting FastAPI server on {host}:{port}")
            self.log.info("=" * 60)
            self.log.info("🚀 SBARDS API Server v2.0.0 - Enhanced Edition")
            self.log.info("=" * 60)
            self.log.info("📊 Available Endpoints:")

            endpoints = manager.get("endpoints", {})
            for name, path in endpoints.items():
                self.log.info(f"  ✅ {name}: http://{host}:{port}{path}")

            self.log.info("=" * 60)
            self.log.info("🎯 Quick Access URLs:")
            self.log.info(f"  🏠 Main Dashboard: http://{host}:{port}/dashboard")
            self.log.info(f"  📖 Enhanced API Docs: http://{host}:{port}/api/docs/enhanced")
            self.log.info(f"  🔧 Swagger UI: http://{host}:{port}/api/docs")
            self.log.info(f"  🏥 Health Monitor: http://{host}:{port}/api/health/page")
            self.log.info(f"  🛡️ Capture Status: http://{host}:{port}/api/capture/status/page")
            self.log.info("=" * 60)
            self.log.info("🔥 New Features in v2.0.0:")
            self.log.info("  ✨ Enhanced Health Monitoring with Real-time Updates")
            self.log.info("  🎨 Improved Navigation System with Theme Toggle")
            self.log.info("  📊 Interactive Dashboard Components")
            self.log.info("  🚀 Performance Optimizations")
            self.log.info("  🎯 Better User Experience")
            self.log.info("  🌓 Dark/Light Mode Support")
            self.log.info("  ⚡ Faster Navigation Between Pages")
            self.log.info("  🔧 Enhanced API Documentation")

            # Update manager status
            manager["running"] = True
            manager["host"] = host
            manager["port"] = port

            # Run FastAPI server
            uvicorn.run(
                manager["app"],
                host=host,
                port=port,
                log_level="info",
                access_log=True
            )

        except Exception as e:
            self.log.error(f"Error running API layer: {e}")
            manager["running"] = False

    def _run_capture_layer(self, manager, **kwargs):
        """Run the True Capture Layer."""
        try:
            integrated_capture_layer = manager.get("integrated_capture_layer")

            if integrated_capture_layer and integrated_capture_layer.running:
                self.log.info("True Capture Layer is monitoring for file interceptions...")
                self.log.info("Kernel-level monitoring active for all file sources")
                self.log.info("C++ bridge and Python processing layers operational")

                # Monitor True Capture Layer
                while self.running:
                    time.sleep(5)  # Check every 5 seconds

                    # Get and log comprehensive statistics
                    stats = integrated_capture_layer.get_comprehensive_statistics()
                    if stats.get("cpp_intercepts", 0) > 0:
                        self.log.info(f"C++ Intercepts: {stats.get('cpp_intercepts', 0)}, "
                                    f"Python Processes: {stats.get('python_processes', 0)}, "
                                    f"Static Analysis: {stats.get('static_analysis_requests', 0)}, "
                                    f"Quarantined: {stats.get('files_quarantined', 0)}")

                    # Check system status
                    system_status = integrated_capture_layer.get_status()
                    if not system_status["running"]:
                        self.log.warning("True Capture Layer stopped unexpectedly")
                        break

                    # Check component health
                    components = system_status["components"]
                    if not all(components.values()):
                        self.log.warning(f"Some components not operational: {components}")

            else:
                self.log.error("True Capture Layer not available or not running")

        except Exception as e:
            self.log.error(f"Error running True Capture Layer: {e}")

    def _monitor_layer(self, layer_name: str, manager: dict):
        """Monitor a layer's health and performance."""
        try:
            if layer_name == "capture":
                integrated_capture_layer = manager.get("integrated_capture_layer")
                if integrated_capture_layer:
                    stats = integrated_capture_layer.get_comprehensive_statistics()
                    # Log stats periodically (every 60 seconds)
                    if int(time.time()) % 60 == 0:
                        self.log.debug(f"True Capture Layer stats: {stats}")

        except Exception as e:
            self.log.error(f"Error monitoring {layer_name} layer: {e}")

    def _shutdown_single_layer(self, layer_name: str, manager: dict):
        """Shutdown a single layer."""
        try:
            self.log.info(f"Shutting down {layer_name} layer...")

            if layer_name == "capture":
                integrated_capture_layer = manager.get("integrated_capture_layer")
                redis_queue = manager.get("redis_queue")

                if integrated_capture_layer:
                    integrated_capture_layer.cleanup()
                    self.log.info("True Capture Layer stopped and cleaned up")

                if redis_queue:
                    redis_queue.stop()
                    self.log.info("Redis queue stopped")

            elif layer_name == "api":
                # FastAPI shutdown is handled by uvicorn
                manager["running"] = False

            manager["status"] = "stopped"
            self.log.info(f"{layer_name} layer shutdown completed")

        except Exception as e:
            self.log.error(f"Error shutting down {layer_name} layer: {e}")

    def run_all_layers(self):
        """Run all enabled layers."""
        self.log.info("Starting SBARDS with all enabled layers")

        # Initialize all enabled layers
        self._initialize_layers()

        # Start all layers
        self.running = True

        try:
            while self.running:
                # Main loop - coordinate between layers
                time.sleep(1)

                # Health check for layers
                self._health_check_layers()

        except KeyboardInterrupt:
            self.log.info("Shutting down SBARDS")
            self.running = False
        finally:
            self._shutdown_layers()

    def _health_check_layers(self):
        """Perform health check on all layers."""
        for layer_name, manager in self.layer_managers.items():
            if manager.get("status") == "initialized":
                # Perform health check
                pass

    def _shutdown_layers(self):
        """Shutdown all layers gracefully."""
        self.log.info("Shutting down all layers...")

        for layer_name in self.layer_managers:
            try:
                self.log.info(f"Shutting down {layer_name} layer")
                # Layer-specific shutdown logic will be implemented
            except Exception as e:
                self.log.error(f"Error shutting down {layer_name} layer: {e}")

        self.log.info("All layers shut down")

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description=f"{PROJECT_NAME} v{PROJECT_VERSION} - Security Behavior Analysis and Ransomware Detection System"
    )

    # General arguments
    parser.add_argument("--config", type=str, default="config.json", help="Configuration file path")
    parser.add_argument("--log-level", type=str, default="info",
                       choices=["debug", "info", "warning", "error", "critical"],
                       help="Logging level")
    parser.add_argument("--version", action="version", version=f"{PROJECT_NAME} {PROJECT_VERSION}")

    # Mode arguments
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument("--capture", action="store_true", help="Run capture layer only")
    mode_group.add_argument("--static-analysis", action="store_true", help="Run static analysis layer only")
    mode_group.add_argument("--dynamic-analysis", action="store_true", help="Run dynamic analysis layer only")
    mode_group.add_argument("--response", action="store_true", help="Run response layer only")
    mode_group.add_argument("--monitoring", action="store_true", help="Run monitoring layer only")
    mode_group.add_argument("--api", action="store_true", help="Run API layer only")
    mode_group.add_argument("--ui", action="store_true", help="Run UI layer only")

    # Layer-specific arguments
    parser.add_argument("--target", type=str, help="Target directory for capture layer")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="API server host")
    parser.add_argument("--port", type=int, default=8000, help="API server port")

    # System arguments
    parser.add_argument("--check-system", action="store_true", help="Check system requirements and exit")
    parser.add_argument("--test-config", action="store_true", help="Test configuration and exit")
    parser.add_argument("--status", action="store_true", help="Show system status and exit")
    parser.add_argument("--demo", action="store_true", help="Run capture layer demo")

    return parser.parse_args()

def check_system_requirements():
    """Check system requirements."""
    print(f"{PROJECT_NAME} v{PROJECT_VERSION} - System Requirements Check")
    print("=" * 60)

    # Platform info
    platform_info = SystemUtils.get_platform_info()
    print(f"Platform: {platform_info['system']} {platform_info['release']}")
    print(f"Architecture: {platform_info['architecture']}")
    print(f"Python Version: {platform_info['python_version']}")

    # System resources
    resources = SystemUtils.get_system_resources()
    if "error" not in resources:
        print(f"CPU Cores: {resources['cpu']['count']}")
        print(f"Memory: {PerformanceUtils.format_bytes(resources['memory']['total'])}")
        print(f"Disk Free: {PerformanceUtils.format_bytes(resources['disk']['free'])}")

    # Admin privileges
    is_admin = SystemUtils.check_admin_privileges()
    print(f"Admin Privileges: {'Yes' if is_admin else 'No'}")

    print("=" * 60)
    print("System check completed.")

def test_configuration(config_path: str):
    """Test configuration file."""
    print(f"Testing configuration: {config_path}")

    try:
        config_loader = ConfigLoader(config_path)
        config = config_loader.get_config()
        print("✓ Configuration loaded successfully")

        # Test each layer configuration
        layers = ["capture", "static_analysis", "dynamic_analysis", "response",
                 "external_integration", "memory_protection", "monitoring",
                 "api", "ui", "data", "security"]

        for layer in layers:
            if layer in config:
                enabled = config[layer].get("enabled", False)
                status = "enabled" if enabled else "disabled"
                print(f"✓ {layer}: {status}")
            else:
                print(f"⚠ {layer}: not configured")

        print("Configuration test completed.")

    except Exception as e:
        print(f"✗ Configuration error: {e}")
        sys.exit(1)

def show_system_status():
    """Show current system status."""
    print(f"{PROJECT_NAME} v{PROJECT_VERSION} - System Status")
    print("=" * 60)

    try:
        # Load configuration
        config_loader = ConfigLoader()
        config = config_loader.get_config()

        print(f"Configuration: LOADED")
        print(f"Project: {config.get('core', {}).get('project_name', 'Unknown')}")
        print(f"Version: {config.get('core', {}).get('version', 'Unknown')}")
        print()

        # Check layer status
        print("Layer Status:")
        layers = ["capture", "static_analysis", "dynamic_analysis", "response",
                 "external_integration", "memory_protection", "monitoring",
                 "api", "ui", "data", "security"]

        for layer in layers:
            layer_config = config.get(layer, {})
            enabled = layer_config.get("enabled", False) if layer != "api" else True
            status = "ENABLED" if enabled else "DISABLED"
            print(f"  {layer}: {status}")

        print()

        # Check capture layer specifically
        capture_config = config.get("capture", {})
        print("Capture Layer Details:")
        print(f"  Target directories: {len(capture_config.get('target_directories', []))}")
        print(f"  Max file size: {capture_config.get('max_file_size_mb', 100)} MB")
        print(f"  Temp storage: {capture_config.get('temp_storage_path', 'capture/temp_storage')}")
        print(f"  Real-time monitoring: {'ENABLED' if capture_config.get('enable_real_time', True) else 'DISABLED'}")
        print()

        # Check comprehensive monitoring capabilities
        print("Comprehensive Monitoring:")
        monitoring_features = {
            "Browsers": capture_config.get('monitor_browsers', True),
            "Social Media": capture_config.get('monitor_social_media', True),
            "Cloud Storage": capture_config.get('monitor_cloud_storage', True),
            "Email Clients": capture_config.get('monitor_email', True),
            "USB Devices": capture_config.get('monitor_usb', True)
        }

        for feature, enabled in monitoring_features.items():
            status = "ENABLED" if enabled else "DISABLED"
            print(f"  {feature}: {status}")
        print()

        # Check API configuration
        api_config = config.get("api", {})
        print("API Configuration:")
        print(f"  Host: {api_config.get('host', '127.0.0.1')}")
        print(f"  Port: {api_config.get('port', 8000)}")
        print(f"  CORS: {'Enabled' if api_config.get('enable_cors', True) else 'Disabled'}")
        print(f"  Docs: {'Enabled' if api_config.get('enable_docs', True) else 'Disabled'}")
        print()

        # Check Redis configuration
        redis_config = config.get("redis", {})
        print("Redis Configuration:")
        if redis_config.get("enabled", False):
            print(f"  Status: ENABLED")
            print(f"  Host: {redis_config.get('host', 'localhost')}")
            print(f"  Port: {redis_config.get('port', 6379)}")
        else:
            print(f"  Status: DISABLED")

        print("=" * 60)
        print("SUCCESS: System status check completed")

    except Exception as e:
        print(f"❌ Error checking system status: {e}")

def run_capture_demo():
    """Run capture layer demonstration."""
    print(f"{PROJECT_NAME} v{PROJECT_VERSION} - Capture Layer Demo")
    print("=" * 60)

    try:
        # Create a temporary runner for demo
        runner = SBARDSRunner()

        print("🔧 Initializing capture layer for demo...")
        runner._initialize_capture_layer()

        capture_manager = runner.layer_managers.get("capture")
        if capture_manager and capture_manager.get("status") == "initialized":
            print("True Capture Layer initialized successfully")

            # Show True Capture Layer info
            integrated_capture_layer = capture_manager.get("integrated_capture_layer")
            if integrated_capture_layer:
                stats = integrated_capture_layer.get_comprehensive_statistics()
                print(f"📊 Initial stats: {stats}")

                system_status = integrated_capture_layer.get_status()
                print(f"🔧 System status: {system_status}")

            print()
            print("True Capture Layer Demo Instructions:")
            print("  1. The True Capture Layer is now monitoring ALL file sources")
            print("  2. C++ kernel-level interception is active")
            print("  3. Python processing layer is ready")
            print("  4. Upload files via API or simulate file operations")
            print("  5. Check logs for interception and processing information")
            print("  6. Press Ctrl+C to stop the demo")
            print()

            # Run demo for a short time
            runner.running = True
            try:
                runner._run_capture_layer(capture_manager)
            except KeyboardInterrupt:
                print("\nDemo stopped by user")
            finally:
                runner._shutdown_single_layer("capture", capture_manager)
                print("Demo cleanup completed")
        else:
            print("Failed to initialize capture layer for demo")
            if capture_manager:
                print(f"   Status: {capture_manager.get('status', 'unknown')}")
                print(f"   Error: {capture_manager.get('error', 'none')}")

    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main entry point."""
    args = parse_args()

    # Handle special modes
    if args.check_system:
        check_system_requirements()
        return

    if args.test_config:
        test_configuration(args.config)
        return

    if args.status:
        show_system_status()
        return

    if args.demo:
        run_capture_demo()
        return

    # Create SBARDS runner
    try:
        runner = SBARDSRunner(args.config)

        print(f"Starting {PROJECT_NAME} v{PROJECT_VERSION}")
        print("=" * 60)

        # Determine which layer(s) to run
        if args.capture:
            print("Running Capture Layer Only")
            runner.run_single_layer("capture", target=args.target)
        elif args.static_analysis:
            print("Running Static Analysis Layer Only")
            runner.run_single_layer("static_analysis")
        elif args.dynamic_analysis:
            print("Running Dynamic Analysis Layer Only")
            runner.run_single_layer("dynamic_analysis")
        elif args.response:
            print("Running Response Layer Only")
            runner.run_single_layer("response")
        elif args.monitoring:
            print("Running Monitoring Layer Only")
            runner.run_single_layer("monitoring")
        elif args.api:
            print("Running API Layer Only")
            runner.run_single_layer("api", host=args.host, port=args.port)
        elif args.ui:
            print("Running UI Layer Only")
            runner.run_single_layer("ui")
        else:
            # Run all enabled layers
            print("Running All Enabled Layers")
            runner.run_all_layers()

    except KeyboardInterrupt:
        print("\nSBARDS stopped by user")
    except Exception as e:
        print(f"ERROR: Error starting SBARDS: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def check_enhanced_dependencies():
    """Check enhanced dependencies for improved functionality"""
    print("🔍 Checking Enhanced Dependencies...")
    print("=" * 50)

    enhanced_packages = {
        'fastapi': {'required': True, 'description': 'FastAPI web framework'},
        'uvicorn': {'required': True, 'description': 'ASGI server'},
        'psutil': {'required': True, 'description': 'System monitoring'},
        'pydantic': {'required': True, 'description': 'Data validation'},
        'jinja2': {'required': True, 'description': 'Template engine'},
        'redis': {'required': False, 'description': 'Redis client (optional)'},
        'pymongo': {'required': False, 'description': 'MongoDB client (optional)'},
        'requests': {'required': False, 'description': 'HTTP requests (optional)'},
        'aiofiles': {'required': False, 'description': 'Async file operations (optional)'},
        'websockets': {'required': False, 'description': 'WebSocket support (optional)'}
    }

    missing_required = []
    missing_optional = []
    available_packages = []

    for package, info in enhanced_packages.items():
        try:
            __import__(package)
            available_packages.append(package)
            print(f"✅ {package} - {info['description']}")
        except ImportError:
            if info['required']:
                missing_required.append(package)
                print(f"❌ {package} - {info['description']} (REQUIRED)")
            else:
                missing_optional.append(package)
                print(f"⚠️  {package} - {info['description']} (OPTIONAL)")

    print("=" * 50)
    print(f"✅ Available: {len(available_packages)}")
    print(f"❌ Missing Required: {len(missing_required)}")
    print(f"⚠️  Missing Optional: {len(missing_optional)}")

    if missing_required:
        print(f"\n💡 Install required packages:")
        print(f"   pip install {' '.join(missing_required)}")
        return False

    if missing_optional:
        print(f"\n💡 Install optional packages for enhanced features:")
        print(f"   pip install {' '.join(missing_optional)}")

    print("\n🎉 All required dependencies are available!")
    return True

def show_enhanced_banner():
    """Show enhanced startup banner"""
    print("=" * 80)
    print("🚀 SBARDS v2.0.0 - Enhanced Security Behavior Analysis System")
    print("=" * 80)
    print("🛡️  Multi-Layer Security Architecture")
    print("⚡ High-Performance File Interception")
    print("🎨 Modern Web Interface with Dark/Light Themes")
    print("📊 Real-time Monitoring & Analytics")
    print("🔧 Enhanced API Documentation")
    print("🌐 Professional Dashboard Experience")
    print("=" * 80)
    print()

def show_quick_start_guide():
    """Show quick start guide"""
    print("🚀 Quick Start Guide:")
    print("=" * 40)
    print("1. Start API Server:")
    print("   python run.py --api")
    print()
    print("2. Start Capture Layer:")
    print("   python run.py --capture")
    print()
    print("3. Run Full System:")
    print("   python run.py")
    print()
    print("4. Check System Status:")
    print("   python run.py --status")
    print()
    print("5. Test Configuration:")
    print("   python run.py --test-config")
    print("=" * 40)

if __name__ == "__main__":
    # Show enhanced banner
    show_enhanced_banner()

    # Check enhanced dependencies
    if not check_enhanced_dependencies():
        print("\n❌ Cannot start SBARDS due to missing required dependencies.")
        print("Please install the required packages and try again.")
        sys.exit(1)

    print("\n" + "=" * 80)
    print("🎯 Starting SBARDS Enhanced System...")
    print("=" * 80)

    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 SBARDS shutdown requested by user")
        print("👋 Thank you for using SBARDS v2.0.0!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        print("📝 Check logs for detailed error information")
        sys.exit(1)
