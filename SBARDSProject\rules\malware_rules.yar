/*
 * SBARDS Project - Comprehensive Malware Detection Rules
 * These rules are designed to detect various types of malware
 * including ransomware, trojans, backdoors, and more.
 */

// =========== Ransomware Detection Rules ===========

rule Ransomware_Generic {
    meta:
        description = "Detects generic ransomware characteristics",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "ransomware",
        severity = "high"

    strings:
        $ransom_note1 = "your files have been encrypted" nocase;
        $ransom_note2 = "pay the ransom" nocase;
        $ransom_note3 = "decrypt your files" nocase;
        $ransom_note4 = "your important files encryption produced" nocase;
        $ransom_note5 = "your documents, photos, databases and other important files have been encrypted" nocase;

        $bitcoin1 = "bitcoin" nocase;
        $bitcoin2 = "btc" nocase;
        $bitcoin3 = "wallet" nocase;
        $bitcoin4 = "cryptocurrency" nocase;

        $extension1 = ".encrypted" nocase;
        $extension2 = ".locked" nocase;
        $extension3 = ".crypt" nocase;
        $extension4 = ".crypted" nocase;
        $extension5 = ".enc" nocase;

        $function1 = "encrypt" nocase;
        $function2 = "decrypt" nocase;
        $function3 = "aes" nocase;
        $function4 = "rsa" nocase;
        $function5 = "cryptography" nocase;

    condition:
        (any of ($ransom_note*)) or
        (any of ($bitcoin*) and any of ($function*)) or
        (any of ($extension*) and any of ($function*))
}

rule Ransomware_File_Operations {
    meta:
        description = "Detects file operations commonly used by ransomware",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "ransomware",
        severity = "medium"

    strings:
        $file_op1 = "FindFirstFile" ascii wide;
        $file_op2 = "FindNextFile" ascii wide;
        $file_op3 = "CreateFile" ascii wide;
        $file_op4 = "ReadFile" ascii wide;
        $file_op5 = "WriteFile" ascii wide;
        $file_op6 = "DeleteFile" ascii wide;
        $file_op7 = "MoveFile" ascii wide;
        $file_op8 = "CopyFile" ascii wide;

        $ext1 = ".doc" nocase;
        $ext2 = ".xls" nocase;
        $ext3 = ".ppt" nocase;
        $ext4 = ".pdf" nocase;
        $ext5 = ".jpg" nocase;
        $ext6 = ".png" nocase;
        $ext7 = ".txt" nocase;
        $ext8 = ".zip" nocase;
        $ext9 = ".rar" nocase;
        $ext10 = ".sql" nocase;
        $ext11 = ".mdb" nocase;
        $ext12 = ".mp3" nocase;
        $ext13 = ".mp4" nocase;

    condition:
        4 of ($file_op*) and 3 of ($ext*)
}

// =========== Trojan Detection Rules ===========

rule Trojan_Generic {
    meta:
        description = "Detects generic trojan characteristics",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "trojan",
        severity = "high"

    strings:
        $registry1 = "RegCreateKey" ascii wide;
        $registry2 = "RegSetValue" ascii wide;
        $registry3 = "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" ascii wide;
        $registry4 = "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" ascii wide;

        $process1 = "CreateProcess" ascii wide;
        $process2 = "ShellExecute" ascii wide;
        $process3 = "WinExec" ascii wide;

        $network1 = "InternetOpen" ascii wide;
        $network2 = "InternetConnect" ascii wide;
        $network3 = "HttpSendRequest" ascii wide;
        $network4 = "socket" ascii wide;
        $network5 = "connect" ascii wide;
        $network6 = "recv" ascii wide;
        $network7 = "send" ascii wide;

        $stealth1 = "IsDebuggerPresent" ascii wide;
        $stealth2 = "CheckRemoteDebuggerPresent" ascii wide;
        $stealth3 = "OutputDebugString" ascii wide;
        $stealth4 = "GetTickCount" ascii wide;
        $stealth5 = "QueryPerformanceCounter" ascii wide;

    condition:
        (2 of ($registry*) and 1 of ($process*)) or
        (2 of ($network*) and 1 of ($stealth*)) or
        (1 of ($registry*) and 2 of ($network*))
}

// =========== Backdoor Detection Rules ===========

rule Backdoor_Generic {
    meta:
        description = "Detects generic backdoor characteristics",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "backdoor",
        severity = "high"

    strings:
        $cmd1 = "cmd.exe" ascii wide;
        $cmd2 = "powershell.exe" ascii wide;
        $cmd3 = "command" ascii wide;
        $cmd4 = "shell" ascii wide;

        $remote1 = "remote" ascii wide;
        $remote2 = "connect back" ascii wide;
        $remote3 = "reverse shell" ascii wide;

        $port1 = "port=" ascii wide;
        $port2 = "port =" ascii wide;
        $port3 = "port:" ascii wide;

        $ip1 = /\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/ ascii wide;

        $persistence1 = "startup" ascii wide;
        $persistence2 = "registry" ascii wide;
        $persistence3 = "task scheduler" ascii wide;
        $persistence4 = "autorun" ascii wide;

    condition:
        (1 of ($cmd*) and 1 of ($remote*)) or
        (1 of ($cmd*) and 1 of ($port*) and $ip1) or
        (1 of ($remote*) and 1 of ($persistence*))
}

// =========== Keylogger Detection Rules ===========

rule Keylogger_Generic {
    meta:
        description = "Detects generic keylogger characteristics",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "keylogger",
        severity = "medium"

    strings:
        $api1 = "GetAsyncKeyState" ascii wide;
        $api2 = "GetKeyboardState" ascii wide;
        $api3 = "SetWindowsHookEx" ascii wide;
        $api4 = "WH_KEYBOARD" ascii wide;
        $api5 = "WH_KEYBOARD_LL" ascii wide;

        $log1 = "keylog" ascii wide;
        $log2 = "keystroke" ascii wide;
        $log3 = "keyboard" ascii wide;
        $log4 = "typed" ascii wide;

        $file1 = "log.txt" ascii wide;
        $file2 = "keylog.txt" ascii wide;
        $file3 = ".log" ascii wide;

    condition:
        (1 of ($api*) and 1 of ($log*)) or
        (1 of ($api*) and 1 of ($file*))
}

// =========== Spyware Detection Rules ===========

rule Spyware_Generic {
    meta:
        description = "Detects generic spyware characteristics",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "spyware",
        severity = "medium"

    strings:
        $screen1 = "screenshot" ascii wide;
        $screen2 = "screen capture" ascii wide;
        $screen3 = "PrintWindow" ascii wide;
        $screen4 = "BitBlt" ascii wide;

        $webcam1 = "webcam" ascii wide;
        $webcam2 = "camera" ascii wide;
        $webcam3 = "video capture" ascii wide;

        $audio1 = "microphone" ascii wide;
        $audio2 = "audio capture" ascii wide;
        $audio3 = "record audio" ascii wide;

        $data1 = "browser history" ascii wide;
        $data2 = "password" ascii wide;
        $data3 = "cookie" ascii wide;
        $data4 = "credentials" ascii wide;

    condition:
        (1 of ($screen*)) or
        (1 of ($webcam*)) or
        (1 of ($audio*)) or
        (2 of ($data*))
}

// =========== General Malicious Behavior Rules ===========

rule Suspicious_Behavior {
    meta:
        description = "Detects suspicious behaviors that may indicate malware",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "general",
        severity = "low"

    strings:
        $antivm1 = "vmware" nocase;
        $antivm2 = "virtualbox" nocase;
        $antivm3 = "vbox" nocase;
        $antivm4 = "qemu" nocase;
        $antivm5 = "virtual machine" nocase;

        $antidebug1 = "IsDebuggerPresent" ascii wide;
        $antidebug2 = "CheckRemoteDebuggerPresent" ascii wide;
        $antidebug3 = "NtQueryInformationProcess" ascii wide;
        $antidebug4 = "ProcessDebugPort" ascii wide;

        $injection1 = "VirtualAlloc" ascii wide;
        $injection2 = "WriteProcessMemory" ascii wide;
        $injection3 = "CreateRemoteThread" ascii wide;
        $injection4 = "NtCreateThreadEx" ascii wide;

        $evasion1 = "Sleep" ascii wide;
        $evasion2 = "GetTickCount" ascii wide;
        $evasion3 = "QueryPerformanceCounter" ascii wide;
        $evasion4 = "timeGetTime" ascii wide;

    condition:
        (2 of ($antivm*)) or
        (2 of ($antidebug*)) or
        (2 of ($injection*)) or
        (2 of ($evasion*))
}

// =========== File Type Specific Rules ===========

rule Suspicious_Document {
    meta:
        description = "Detects suspicious characteristics in document files",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "document",
        severity = "medium"

    strings:
        $ole1 = { D0 CF 11 E0 A1 B1 1A E1 }; // OLE header
        $macro1 = "AutoOpen" ascii wide;
        $macro2 = "AutoExec" ascii wide;
        $macro3 = "AutoExit" ascii wide;
        $macro4 = "Document_Open" ascii wide;
        $macro5 = "Document_Close" ascii wide;

        $suspicious1 = "powershell" nocase;
        $suspicious2 = "cmd.exe" nocase;
        $suspicious3 = "shell" nocase;
        $suspicious4 = "http://" nocase;
        $suspicious5 = "https://" nocase;
        $suspicious6 = "ActiveXObject" nocase;
        $suspicious7 = "CreateObject" nocase;
        $suspicious8 = "WScript.Shell" nocase;

    condition:
        $ole1 and
        ((1 of ($macro*)) and (1 of ($suspicious*)))
}

rule Suspicious_Executable {
    meta:
        description = "Detects suspicious characteristics in executable files",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "executable",
        severity = "medium"

    strings:
        $mz = { 4D 5A }; // MZ header
        $pe = { 50 45 00 00 }; // PE header

        $packer1 = "UPX" ascii wide;
        $packer2 = "ASPack" ascii wide;
        $packer3 = "PECompact" ascii wide;
        $packer4 = "FSG" ascii wide;
        $packer5 = "Themida" ascii wide;

        $section1 = ".text" ascii;
        $section2 = ".data" ascii;
        $section3 = ".rdata" ascii;
        $section4 = ".rsrc" ascii;
        $section5 = ".reloc" ascii;

        $suspicious1 = "kernel32.dll" ascii wide;
        $suspicious2 = "user32.dll" ascii wide;
        $suspicious3 = "advapi32.dll" ascii wide;
        $suspicious4 = "ws2_32.dll" ascii wide;
        $suspicious5 = "wininet.dll" ascii wide;

    condition:
        $mz at 0 and $pe and
        ((1 of ($packer*)) or
        (all of ($section*) and all of ($suspicious*)))
}

// =========== Test Rules ===========

rule Test_Rule {
    meta:
        description = "This is a test rule for development purposes",
        author = "SBARDS Project",
        date = "2025-05-13",
        category = "test",
        severity = "info"

    strings:
        $test_string = "test" nocase;
        $malicious = "malicious" nocase;
        $sample = "sample" nocase;

    condition:
        any of them
}
