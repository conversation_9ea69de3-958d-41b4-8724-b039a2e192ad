"""
Logging Module for SBARDS

This module provides logging functionality for the SBARDS project.
"""

import os
import logging
import logging.handlers
from typing import Dict, Any, Optional

class Logger:
    """
    Logger for the SBARDS Project.
    Provides logging functionality with file and console handlers.
    """
    
    def __init__(self, log_dir: str = "logs", log_level: str = "info"):
        """
        Initialize the logger.
        
        Args:
            log_dir (str): Directory for log files
            log_level (str): Logging level (debug, info, warning, error, critical)
        """
        self.log_dir = os.path.abspath(log_dir)
        self.log_level = self._get_log_level(log_level)
        self.loggers: Dict[str, logging.Logger] = {}
        
        # Create log directory if it doesn't exist
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Configure root logger
        self._configure_root_logger()
    
    def _get_log_level(self, level: str) -> int:
        """
        Get the logging level from a string.
        
        Args:
            level (str): Logging level as a string
            
        Returns:
            int: Logging level as an integer
        """
        level = level.lower()
        if level == "debug":
            return logging.DEBUG
        elif level == "info":
            return logging.INFO
        elif level == "warning":
            return logging.WARNING
        elif level == "error":
            return logging.ERROR
        elif level == "critical":
            return logging.CRITICAL
        else:
            return logging.INFO
    
    def _configure_root_logger(self) -> None:
        """
        Configure the root logger.
        """
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # Create file handler for root logger
        root_file_handler = logging.handlers.RotatingFileHandler(
            os.path.join(self.log_dir, "sbards.log"),
            maxBytes=10*1024*1024,  # 10 MB
            backupCount=5
        )
        root_file_handler.setLevel(self.log_level)
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        root_file_handler.setFormatter(file_formatter)
        root_logger.addHandler(root_file_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        Get a logger with the specified name.
        
        Args:
            name (str): Logger name
            
        Returns:
            logging.Logger: Logger instance
        """
        if name in self.loggers:
            return self.loggers[name]
        
        # Create logger
        logger = logging.getLogger(name)
        logger.setLevel(self.log_level)
        
        # Create file handler
        file_handler = logging.handlers.RotatingFileHandler(
            os.path.join(self.log_dir, f"{name.lower().replace('.', '_')}.log"),
            maxBytes=5*1024*1024,  # 5 MB
            backupCount=3
        )
        file_handler.setLevel(self.log_level)
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        
        # Add file handler to logger
        logger.addHandler(file_handler)
        
        # Store logger
        self.loggers[name] = logger
        
        return logger
