"""
Redis Queue Manager for SBARDS Capture Layer

This module provides high-performance Redis-based queuing for file processing
between C++ and Python components. Enables scalable, distributed processing.

Features:
- High-performance Redis queuing
- Priority-based message handling
- Distributed processing support
- Fault tolerance and recovery
- Message persistence and reliability
"""

import json
import time
import threading
import logging
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import asdict
from datetime import datetime, timedelta

try:
    import redis
    from redis.exceptions import ConnectionError, TimeoutError
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.logger import get_global_logger
from core.constants import REDIS_DEFAULT_HOST, REDIS_DEFAULT_PORT, REDIS_DEFAULT_DB
from capture.python.file_interceptor import FileInterceptionEvent

class RedisQueueManager:
    """
    High-performance Redis queue manager for SBARDS file processing.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Redis queue manager.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        if not REDIS_AVAILABLE:
            raise ImportError("Redis library not available. Install with: pip install redis")

        self.config = config
        self.logger = get_global_logger().get_layer_logger("capture")

        # Redis configuration
        self.host = config.get("redis_host", REDIS_DEFAULT_HOST)
        self.port = config.get("redis_port", REDIS_DEFAULT_PORT)
        self.db = config.get("redis_db", REDIS_DEFAULT_DB)
        self.password = config.get("redis_password", None)
        self.socket_timeout = config.get("socket_timeout", 5.0)
        self.connection_pool_size = config.get("connection_pool_size", 10)

        # Queue configuration
        self.queue_prefix = config.get("queue_prefix", "sbards")
        self.high_priority_queue = f"{self.queue_prefix}:high_priority"
        self.normal_priority_queue = f"{self.queue_prefix}:normal_priority"
        self.processing_queue = f"{self.queue_prefix}:processing"
        self.failed_queue = f"{self.queue_prefix}:failed"
        self.stats_key = f"{self.queue_prefix}:stats"

        # Processing configuration
        self.max_retries = config.get("max_retries", 3)
        self.retry_delay = config.get("retry_delay", 5.0)
        self.message_ttl = config.get("message_ttl", 3600)  # 1 hour
        self.batch_size = config.get("batch_size", 10)

        # State management
        self.running = False
        self.worker_threads: List[threading.Thread] = []
        self.consumer_callbacks: Dict[str, Callable] = {}

        # Redis connection
        self.redis_client: Optional[redis.Redis] = None
        self.connection_pool: Optional[redis.ConnectionPool] = None

        # Statistics
        self.stats = {
            "messages_sent": 0,
            "messages_received": 0,
            "messages_processed": 0,
            "messages_failed": 0,
            "connection_errors": 0,
            "start_time": None
        }

        self._initialize_redis()

    def _initialize_redis(self):
        """Initialize Redis connection."""
        try:
            # Create connection pool
            self.connection_pool = redis.ConnectionPool(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                socket_timeout=self.socket_timeout,
                max_connections=self.connection_pool_size,
                decode_responses=True
            )

            # Create Redis client
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)

            # Test connection
            self.redis_client.ping()

            self.logger.info(f"Redis connection established: {self.host}:{self.port}/{self.db}")

        except Exception as e:
            self.logger.error(f"Failed to initialize Redis: {e}")
            self.redis_client = None
            raise

    def is_available(self) -> bool:
        """Check if Redis is available."""
        if not self.redis_client:
            return False

        try:
            self.redis_client.ping()
            return True
        except Exception:
            return False

    def start(self, worker_count: int = 2) -> bool:
        """
        Start the queue manager.

        Args:
            worker_count (int): Number of worker threads

        Returns:
            bool: True if started successfully
        """
        if self.running:
            self.logger.warning("RedisQueueManager is already running")
            return False

        if not self.is_available():
            self.logger.error("Redis is not available")
            return False

        self.running = True
        self.stats["start_time"] = time.time()

        try:
            # Start worker threads
            for i in range(worker_count):
                thread = threading.Thread(
                    target=self._worker_thread,
                    args=(i,),
                    daemon=True,
                    name=f"RedisQueue-Worker-{i}"
                )
                thread.start()
                self.worker_threads.append(thread)

            self.logger.info(f"RedisQueueManager started with {worker_count} workers")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start RedisQueueManager: {e}")
            self.running = False
            return False

    def stop(self):
        """Stop the queue manager."""
        if not self.running:
            return

        self.logger.info("Stopping RedisQueueManager...")
        self.running = False

        # Wait for worker threads
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=5.0)

        self.worker_threads.clear()
        self._update_stats()
        self.logger.info("RedisQueueManager stopped")

    def send_file_event(self, event: FileInterceptionEvent, priority: str = "normal") -> bool:
        """
        Send a file interception event to the queue.

        Args:
            event (FileInterceptionEvent): File event to send
            priority (str): Priority level ("high" or "normal")

        Returns:
            bool: True if sent successfully
        """
        if not self.is_available():
            return False

        try:
            # Serialize event
            message = {
                "type": "file_event",
                "data": asdict(event),
                "timestamp": time.time(),
                "retry_count": 0,
                "priority": priority
            }

            message_json = json.dumps(message)

            # Choose queue based on priority
            queue_name = self.high_priority_queue if priority == "high" else self.normal_priority_queue

            # Send to Redis
            self.redis_client.lpush(queue_name, message_json)

            # Set TTL for message
            if self.message_ttl > 0:
                self.redis_client.expire(queue_name, self.message_ttl)

            self.stats["messages_sent"] += 1
            self.logger.debug(f"Sent file event: {event.file_path} (priority: {priority})")

            return True

        except Exception as e:
            self.logger.error(f"Failed to send file event: {e}")
            self.stats["connection_errors"] += 1
            return False

    def send_message(self, message_type: str, data: Dict[str, Any], priority: str = "normal") -> bool:
        """
        Send a generic message to the queue.

        Args:
            message_type (str): Type of message
            data (Dict[str, Any]): Message data
            priority (str): Priority level

        Returns:
            bool: True if sent successfully
        """
        if not self.is_available():
            return False

        try:
            message = {
                "type": message_type,
                "data": data,
                "timestamp": time.time(),
                "retry_count": 0,
                "priority": priority
            }

            message_json = json.dumps(message)
            queue_name = self.high_priority_queue if priority == "high" else self.normal_priority_queue

            self.redis_client.lpush(queue_name, message_json)
            self.stats["messages_sent"] += 1

            return True

        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            self.stats["connection_errors"] += 1
            return False

    def register_consumer(self, message_type: str, callback: Callable[[Dict[str, Any]], bool]):
        """
        Register a consumer callback for a message type.

        Args:
            message_type (str): Type of message to consume
            callback (Callable): Function to call when message is received
        """
        self.consumer_callbacks[message_type] = callback
        self.logger.info(f"Registered consumer for message type: {message_type}")

    def get_queue_sizes(self) -> Dict[str, int]:
        """
        Get current queue sizes.

        Returns:
            Dict[str, int]: Queue sizes
        """
        if not self.is_available():
            return {}

        try:
            return {
                "high_priority": self.redis_client.llen(self.high_priority_queue),
                "normal_priority": self.redis_client.llen(self.normal_priority_queue),
                "processing": self.redis_client.llen(self.processing_queue),
                "failed": self.redis_client.llen(self.failed_queue)
            }
        except Exception as e:
            self.logger.error(f"Failed to get queue sizes: {e}")
            return {}

    def clear_queues(self):
        """Clear all queues."""
        if not self.is_available():
            return

        try:
            queues = [
                self.high_priority_queue,
                self.normal_priority_queue,
                self.processing_queue,
                self.failed_queue
            ]

            for queue in queues:
                self.redis_client.delete(queue)

            self.logger.info("All queues cleared")

        except Exception as e:
            self.logger.error(f"Failed to clear queues: {e}")

    def _worker_thread(self, worker_id: int):
        """Worker thread for processing messages."""
        self.logger.debug(f"Redis worker {worker_id} started")

        while self.running:
            try:
                # Process high priority queue first
                message = self._get_next_message(self.high_priority_queue, timeout=1)
                if not message:
                    # Process normal priority queue
                    message = self._get_next_message(self.normal_priority_queue, timeout=1)

                if message:
                    self._process_message(message)

            except Exception as e:
                self.logger.error(f"Redis worker {worker_id} error: {e}")
                time.sleep(1)

        self.logger.debug(f"Redis worker {worker_id} stopped")

    def _get_next_message(self, queue_name: str, timeout: int = 1) -> Optional[Dict[str, Any]]:
        """Get next message from queue."""
        try:
            # Use blocking pop with timeout
            result = self.redis_client.brpop(queue_name, timeout=timeout)
            if result:
                _, message_json = result
                message = json.loads(message_json)
                self.stats["messages_received"] += 1
                return message
        except Exception as e:
            self.logger.debug(f"Error getting message from {queue_name}: {e}")

        return None

    def _process_message(self, message: Dict[str, Any]):
        """Process a received message."""
        try:
            message_type = message.get("type")
            data = message.get("data", {})

            # Find appropriate consumer
            if message_type in self.consumer_callbacks:
                callback = self.consumer_callbacks[message_type]

                # Process message
                success = callback(data)

                if success:
                    self.stats["messages_processed"] += 1
                    self.logger.debug(f"Processed message: {message_type}")
                else:
                    self._handle_failed_message(message)
            else:
                self.logger.warning(f"No consumer registered for message type: {message_type}")
                self._handle_failed_message(message)

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            self._handle_failed_message(message)

    def _handle_failed_message(self, message: Dict[str, Any]):
        """Handle failed message processing."""
        try:
            retry_count = message.get("retry_count", 0)

            if retry_count < self.max_retries:
                # Retry message
                message["retry_count"] = retry_count + 1
                message["retry_timestamp"] = time.time() + self.retry_delay

                # Put back in queue for retry
                queue_name = (self.high_priority_queue if message.get("priority") == "high"
                             else self.normal_priority_queue)

                self.redis_client.lpush(queue_name, json.dumps(message))
                self.logger.debug(f"Retrying message (attempt {retry_count + 1})")

            else:
                # Move to failed queue
                self.redis_client.lpush(self.failed_queue, json.dumps(message))
                self.stats["messages_failed"] += 1
                self.logger.warning(f"Message failed after {self.max_retries} retries")

        except Exception as e:
            self.logger.error(f"Error handling failed message: {e}")

    def _update_stats(self):
        """Update statistics in Redis."""
        if not self.is_available():
            return

        try:
            stats_data = self.stats.copy()
            if stats_data["start_time"]:
                stats_data["runtime"] = time.time() - stats_data["start_time"]

            self.redis_client.hset(self.stats_key, mapping=stats_data)

        except Exception as e:
            self.logger.error(f"Failed to update stats: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """Get current statistics."""
        stats = self.stats.copy()
        if stats["start_time"]:
            stats["runtime"] = time.time() - stats["start_time"]

        # Add queue sizes
        stats["queue_sizes"] = self.get_queue_sizes()

        return stats

class RedisTaskScheduler:
    """
    Advanced task scheduler using Redis for delayed and recurring tasks.
    """

    def __init__(self, queue_manager: RedisQueueManager):
        """
        Initialize task scheduler.

        Args:
            queue_manager (RedisQueueManager): Redis queue manager instance
        """
        self.queue_manager = queue_manager
        self.logger = get_global_logger().get_layer_logger("capture")
        self.scheduled_tasks_key = f"{queue_manager.queue_prefix}:scheduled_tasks"
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None

    def schedule_task(self, task_type: str, data: Dict[str, Any],
                     delay_seconds: int = 0, recurring: bool = False,
                     interval_seconds: int = 0) -> str:
        """
        Schedule a task for future execution.

        Args:
            task_type (str): Type of task
            data (Dict[str, Any]): Task data
            delay_seconds (int): Delay before execution
            recurring (bool): Whether task should repeat
            interval_seconds (int): Interval for recurring tasks

        Returns:
            str: Task ID
        """
        if not self.queue_manager.is_available():
            return ""

        try:
            task_id = f"task_{int(time.time() * 1000000)}"
            execute_time = time.time() + delay_seconds

            task = {
                "id": task_id,
                "type": task_type,
                "data": data,
                "execute_time": execute_time,
                "recurring": recurring,
                "interval_seconds": interval_seconds,
                "created_time": time.time()
            }

            # Store in Redis sorted set with execute_time as score
            self.queue_manager.redis_client.zadd(
                self.scheduled_tasks_key,
                {json.dumps(task): execute_time}
            )

            self.logger.debug(f"Scheduled task {task_id} for execution in {delay_seconds} seconds")
            return task_id

        except Exception as e:
            self.logger.error(f"Failed to schedule task: {e}")
            return ""

    def start_scheduler(self):
        """Start the task scheduler."""
        if self.running:
            return

        self.running = True
        self.scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            daemon=True,
            name="RedisTaskScheduler"
        )
        self.scheduler_thread.start()
        self.logger.info("Task scheduler started")

    def stop_scheduler(self):
        """Stop the task scheduler."""
        if not self.running:
            return

        self.running = False
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5.0)

        self.logger.info("Task scheduler stopped")

    def _scheduler_loop(self):
        """Main scheduler loop."""
        while self.running:
            try:
                current_time = time.time()

                # Get tasks ready for execution
                ready_tasks = self.queue_manager.redis_client.zrangebyscore(
                    self.scheduled_tasks_key, 0, current_time, withscores=True
                )

                for task_json, score in ready_tasks:
                    try:
                        task = json.loads(task_json)

                        # Execute task
                        self.queue_manager.send_message(
                            task["type"],
                            task["data"],
                            "normal"
                        )

                        # Remove from scheduled tasks
                        self.queue_manager.redis_client.zrem(
                            self.scheduled_tasks_key,
                            task_json
                        )

                        # Reschedule if recurring
                        if task["recurring"] and task["interval_seconds"] > 0:
                            task["execute_time"] = current_time + task["interval_seconds"]
                            self.queue_manager.redis_client.zadd(
                                self.scheduled_tasks_key,
                                {json.dumps(task): task["execute_time"]}
                            )

                        self.logger.debug(f"Executed scheduled task: {task['id']}")

                    except Exception as e:
                        self.logger.error(f"Error executing scheduled task: {e}")

                time.sleep(1)  # Check every second

            except Exception as e:
                self.logger.error(f"Scheduler loop error: {e}")
                time.sleep(5)

class RedisMetrics:
    """
    Redis-based metrics collection and monitoring.
    """

    def __init__(self, queue_manager: RedisQueueManager):
        """
        Initialize metrics collector.

        Args:
            queue_manager (RedisQueueManager): Redis queue manager instance
        """
        self.queue_manager = queue_manager
        self.logger = get_global_logger().get_layer_logger("capture")
        self.metrics_key = f"{queue_manager.queue_prefix}:metrics"
        self.running = False
        self.metrics_thread: Optional[threading.Thread] = None

    def record_metric(self, metric_name: str, value: Union[int, float],
                     timestamp: Optional[float] = None):
        """
        Record a metric value.

        Args:
            metric_name (str): Name of the metric
            value (Union[int, float]): Metric value
            timestamp (Optional[float]): Timestamp (defaults to current time)
        """
        if not self.queue_manager.is_available():
            return

        try:
            if timestamp is None:
                timestamp = time.time()

            metric_key = f"{self.metrics_key}:{metric_name}"

            # Store as time series data
            self.queue_manager.redis_client.zadd(
                metric_key,
                {str(value): timestamp}
            )

            # Keep only last 1000 entries
            self.queue_manager.redis_client.zremrangebyrank(metric_key, 0, -1001)

        except Exception as e:
            self.logger.error(f"Failed to record metric {metric_name}: {e}")

    def get_metric_history(self, metric_name: str,
                          start_time: Optional[float] = None,
                          end_time: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Get metric history.

        Args:
            metric_name (str): Name of the metric
            start_time (Optional[float]): Start timestamp
            end_time (Optional[float]): End timestamp

        Returns:
            List[Dict[str, Any]]: Metric history
        """
        if not self.queue_manager.is_available():
            return []

        try:
            metric_key = f"{self.metrics_key}:{metric_name}"

            if start_time is None:
                start_time = 0
            if end_time is None:
                end_time = time.time()

            results = self.queue_manager.redis_client.zrangebyscore(
                metric_key, start_time, end_time, withscores=True
            )

            return [
                {"value": float(value), "timestamp": timestamp}
                for value, timestamp in results
            ]

        except Exception as e:
            self.logger.error(f"Failed to get metric history for {metric_name}: {e}")
            return []

    def start_monitoring(self, interval_seconds: int = 60):
        """
        Start automatic metrics monitoring.

        Args:
            interval_seconds (int): Monitoring interval
        """
        if self.running:
            return

        self.running = True
        self.interval = interval_seconds
        self.metrics_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="RedisMetrics"
        )
        self.metrics_thread.start()
        self.logger.info(f"Metrics monitoring started (interval: {interval_seconds}s)")

    def stop_monitoring(self):
        """Stop metrics monitoring."""
        if not self.running:
            return

        self.running = False
        if self.metrics_thread and self.metrics_thread.is_alive():
            self.metrics_thread.join(timeout=5.0)

        self.logger.info("Metrics monitoring stopped")

    def _monitoring_loop(self):
        """Metrics monitoring loop."""
        while self.running:
            try:
                # Record queue sizes
                queue_sizes = self.queue_manager.get_queue_sizes()
                for queue_name, size in queue_sizes.items():
                    self.record_metric(f"queue_size_{queue_name}", size)

                # Record processing statistics
                stats = self.queue_manager.get_statistics()
                for stat_name, value in stats.items():
                    if isinstance(value, (int, float)) and stat_name != "start_time":
                        self.record_metric(f"stat_{stat_name}", value)

                time.sleep(self.interval)

            except Exception as e:
                self.logger.error(f"Metrics monitoring error: {e}")
                time.sleep(self.interval)

# Example usage and testing
if __name__ == "__main__":
    # Test configuration
    config = {
        "redis_host": "localhost",
        "redis_port": 6379,
        "redis_db": 0,
        "queue_prefix": "sbards_test",
        "max_retries": 3,
        "batch_size": 5
    }

    # Create queue manager
    queue_manager = RedisQueueManager(config)

    if queue_manager.is_available():
        print("Redis is available")

        # Register a test consumer
        def test_consumer(data: Dict[str, Any]) -> bool:
            print(f"Received message: {data}")
            return True

        queue_manager.register_consumer("test_message", test_consumer)

        # Start queue manager
        if queue_manager.start(worker_count=2):
            print("Queue manager started")

            # Send test messages
            for i in range(5):
                queue_manager.send_message("test_message", {"test_id": i}, "normal")

            # Wait a bit
            time.sleep(2)

            # Print statistics
            stats = queue_manager.get_statistics()
            print(f"Statistics: {stats}")

            # Stop queue manager
            queue_manager.stop()
        else:
            print("Failed to start queue manager")
    else:
        print("Redis is not available")
