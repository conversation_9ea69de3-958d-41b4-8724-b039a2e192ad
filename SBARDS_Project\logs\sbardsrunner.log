2025-05-24 09:56:37,892 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - [run.py:281] - run_all_layers() - Starting SBARDS with all enabled layers
2025-05-24 09:56:37,893 - <PERSON><PERSON><PERSON><PERSON>unner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['capture', 'static_analysis', 'response', 'monitoring', 'api', 'ui', 'data', 'security']
2025-05-24 09:56:37,893 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - [run.py:116] - _initialize_layer() - Initializing capture layer...
2025-05-24 09:56:37,894 - <PERSON><PERSON><PERSON><PERSON>unner - INFO - [run.py:146] - _initialize_capture_layer() - Capture layer initialized (placeholder)
2025-05-24 09:56:37,894 - <PERSON><PERSON><PERSON><PERSON>unner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized capture layer
2025-05-24 09:56:37,895 - <PERSON><PERSON><PERSON><PERSON>un<PERSON> - INFO - [run.py:116] - _initialize_layer() - Initializing static_analysis layer...
2025-05-24 09:56:37,895 - <PERSON><PERSON><PERSON><PERSON>unner - INFO - [run.py:156] - _initialize_static_analysis_layer() - Static analysis layer initialized (placeholder)
2025-05-24 09:56:37,896 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized static_analysis layer
2025-05-24 09:56:37,896 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing response layer...
2025-05-24 09:56:37,897 - SBARDSRunner - INFO - [run.py:176] - _initialize_response_layer() - Response layer initialized (placeholder)
2025-05-24 09:56:37,897 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized response layer
2025-05-24 09:56:37,898 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing monitoring layer...
2025-05-24 09:56:37,898 - SBARDSRunner - INFO - [run.py:206] - _initialize_monitoring_layer() - Monitoring layer initialized (placeholder)
2025-05-24 09:56:37,899 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized monitoring layer
2025-05-24 09:56:37,899 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing api layer...
2025-05-24 09:56:37,900 - SBARDSRunner - INFO - [run.py:216] - _initialize_api_layer() - API layer initialized (placeholder)
2025-05-24 09:56:37,900 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized api layer
2025-05-24 09:56:37,901 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing ui layer...
2025-05-24 09:56:37,901 - SBARDSRunner - INFO - [run.py:226] - _initialize_ui_layer() - UI layer initialized (placeholder)
2025-05-24 09:56:37,902 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized ui layer
2025-05-24 09:56:37,902 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing data layer...
2025-05-24 09:56:37,903 - SBARDSRunner - INFO - [run.py:236] - _initialize_data_layer() - Data layer initialized (placeholder)
2025-05-24 09:56:37,903 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized data layer
2025-05-24 09:56:37,904 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing security layer...
2025-05-24 09:56:37,904 - SBARDSRunner - INFO - [run.py:246] - _initialize_security_layer() - Security layer initialized (placeholder)
2025-05-24 09:56:37,905 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized security layer
2025-05-24 09:56:56,161 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-24 09:56:56,913 - SBARDSRunner - INFO - [run.py:312] - _shutdown_layers() - Shutting down all layers...
2025-05-24 09:56:56,914 - SBARDSRunner - INFO - [run.py:316] - _shutdown_layers() - Shutting down capture layer
2025-05-24 09:56:56,914 - SBARDSRunner - INFO - [run.py:316] - _shutdown_layers() - Shutting down static_analysis layer
2025-05-24 09:56:56,915 - SBARDSRunner - INFO - [run.py:316] - _shutdown_layers() - Shutting down response layer
2025-05-24 09:56:56,915 - SBARDSRunner - INFO - [run.py:316] - _shutdown_layers() - Shutting down monitoring layer
2025-05-24 09:56:56,915 - SBARDSRunner - INFO - [run.py:316] - _shutdown_layers() - Shutting down api layer
2025-05-24 09:56:56,916 - SBARDSRunner - INFO - [run.py:316] - _shutdown_layers() - Shutting down ui layer
2025-05-24 09:56:56,916 - SBARDSRunner - INFO - [run.py:316] - _shutdown_layers() - Shutting down data layer
2025-05-24 09:56:56,917 - SBARDSRunner - INFO - [run.py:316] - _shutdown_layers() - Shutting down security layer
2025-05-24 09:56:56,917 - SBARDSRunner - INFO - [run.py:321] - _shutdown_layers() - All layers shut down
2025-05-24 10:11:15,883 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running capture layer only
2025-05-24 10:11:15,884 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['capture']
2025-05-24 10:11:15,885 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing capture layer...
2025-05-24 10:11:15,885 - SBARDSRunner - INFO - [run.py:146] - _initialize_capture_layer() - Capture layer initialized (placeholder)
2025-05-24 10:11:15,885 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized capture layer
2025-05-24 10:11:15,886 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - capture layer is running...
2025-05-24 10:11:44,975 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-24 10:12:16,993 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running static_analysis layer only
2025-05-24 10:12:16,994 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['static_analysis']
2025-05-24 10:12:16,994 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing static_analysis layer...
2025-05-24 10:12:16,995 - SBARDSRunner - INFO - [run.py:156] - _initialize_static_analysis_layer() - Static analysis layer initialized (placeholder)
2025-05-24 10:12:16,995 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized static_analysis layer
2025-05-24 10:12:16,995 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - static_analysis layer is running...
2025-05-24 10:13:26,435 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running monitoring layer only
2025-05-24 10:13:26,436 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['monitoring']
2025-05-24 10:13:26,436 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing monitoring layer...
2025-05-24 10:13:26,437 - SBARDSRunner - INFO - [run.py:206] - _initialize_monitoring_layer() - Monitoring layer initialized (placeholder)
2025-05-24 10:13:26,437 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized monitoring layer
2025-05-24 10:13:26,438 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - monitoring layer is running...
2025-05-24 10:13:55,846 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-24 10:14:17,250 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running api layer only
2025-05-24 10:14:17,251 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['api']
2025-05-24 10:14:17,252 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing api layer...
2025-05-24 10:14:17,252 - SBARDSRunner - INFO - [run.py:216] - _initialize_api_layer() - API layer initialized (placeholder)
2025-05-24 10:14:17,252 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized api layer
2025-05-24 10:14:17,253 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - api layer is running...
2025-05-24 10:14:46,646 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-24 10:18:19,977 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running dynamic_analysis layer only
2025-05-24 10:18:19,977 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['dynamic_analysis']
2025-05-24 10:18:19,978 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing dynamic_analysis layer...
2025-05-24 10:18:19,978 - SBARDSRunner - INFO - [run.py:166] - _initialize_dynamic_analysis_layer() - Dynamic analysis layer initialized (placeholder)
2025-05-24 10:18:19,978 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized dynamic_analysis layer
2025-05-24 10:18:19,979 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - dynamic_analysis layer is running...
2025-05-24 10:18:43,539 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-24 10:19:07,353 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running response layer only
2025-05-24 10:19:07,353 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['response']
2025-05-24 10:19:07,354 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing response layer...
2025-05-24 10:19:07,354 - SBARDSRunner - INFO - [run.py:176] - _initialize_response_layer() - Response layer initialized (placeholder)
2025-05-24 10:19:07,355 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized response layer
2025-05-24 10:19:07,356 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - response layer is running...
2025-05-24 10:19:38,111 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-24 10:21:51,518 - SBARDSRunner - INFO - [run.py:281] - run_all_layers() - Starting SBARDS with all enabled layers
2025-05-24 10:21:51,519 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['capture', 'static_analysis', 'response', 'monitoring', 'api', 'ui', 'data', 'security']
2025-05-24 10:21:51,520 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing capture layer...
2025-05-24 10:21:51,520 - SBARDSRunner - INFO - [run.py:146] - _initialize_capture_layer() - Capture layer initialized (placeholder)
2025-05-24 10:21:51,520 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized capture layer
2025-05-24 10:21:51,521 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing static_analysis layer...
2025-05-24 10:21:51,521 - SBARDSRunner - INFO - [run.py:156] - _initialize_static_analysis_layer() - Static analysis layer initialized (placeholder)
2025-05-24 10:21:51,521 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized static_analysis layer
2025-05-24 10:21:51,522 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing response layer...
2025-05-24 10:21:51,522 - SBARDSRunner - INFO - [run.py:176] - _initialize_response_layer() - Response layer initialized (placeholder)
2025-05-24 10:21:51,522 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized response layer
2025-05-24 10:21:51,523 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing monitoring layer...
2025-05-24 10:21:51,523 - SBARDSRunner - INFO - [run.py:206] - _initialize_monitoring_layer() - Monitoring layer initialized (placeholder)
2025-05-24 10:21:51,523 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized monitoring layer
2025-05-24 10:21:51,524 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing api layer...
2025-05-24 10:21:51,524 - SBARDSRunner - INFO - [run.py:216] - _initialize_api_layer() - API layer initialized (placeholder)
2025-05-24 10:21:51,525 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized api layer
2025-05-24 10:21:51,525 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing ui layer...
2025-05-24 10:21:51,526 - SBARDSRunner - INFO - [run.py:226] - _initialize_ui_layer() - UI layer initialized (placeholder)
2025-05-24 10:21:51,526 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized ui layer
2025-05-24 10:21:51,527 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing data layer...
2025-05-24 10:21:51,527 - SBARDSRunner - INFO - [run.py:236] - _initialize_data_layer() - Data layer initialized (placeholder)
2025-05-24 10:21:51,528 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized data layer
2025-05-24 10:21:51,528 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing security layer...
2025-05-24 10:21:51,529 - SBARDSRunner - INFO - [run.py:246] - _initialize_security_layer() - Security layer initialized (placeholder)
2025-05-24 10:21:51,529 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized security layer
2025-05-24 10:24:33,368 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running api layer only
2025-05-24 10:24:33,369 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['api']
2025-05-24 10:24:33,369 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing api layer...
2025-05-24 10:24:33,370 - SBARDSRunner - INFO - [run.py:216] - _initialize_api_layer() - API layer initialized (placeholder)
2025-05-24 10:24:33,370 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized api layer
2025-05-24 10:24:33,371 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - api layer is running...
2025-05-24 10:39:25,920 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running api layer only
2025-05-24 10:39:25,921 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['api']
2025-05-24 10:39:25,921 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing api layer...
2025-05-24 10:39:25,922 - SBARDSRunner - INFO - [run.py:216] - _initialize_api_layer() - API layer initialized (placeholder)
2025-05-24 10:39:25,922 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized api layer
2025-05-24 10:39:25,922 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - api layer is running...
2025-05-24 10:39:57,323 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-24 17:47:33,582 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running api layer only
2025-05-24 17:47:33,583 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['api']
2025-05-24 17:47:33,583 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing api layer...
2025-05-24 17:47:33,583 - SBARDSRunner - INFO - [run.py:216] - _initialize_api_layer() - API layer initialized (placeholder)
2025-05-24 17:47:33,584 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized api layer
2025-05-24 17:47:33,584 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - api layer is running...
2025-05-24 17:48:10,631 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-25 00:47:07,802 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running api layer only
2025-05-25 00:47:07,804 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 00:47:07,805 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing api layer...
2025-05-25 00:47:07,807 - SBARDSRunner - INFO - [run.py:216] - _initialize_api_layer() - API layer initialized (placeholder)
2025-05-25 00:47:07,808 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized api layer
2025-05-25 00:47:07,809 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - api layer is running...
2025-05-25 00:47:29,482 - SBARDSRunner - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-25 01:09:12,538 - SBARDSRunner - INFO - [run.py:259] - run_single_layer() - Running api layer only
2025-05-25 01:09:12,539 - SBARDSRunner - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 01:09:12,539 - SBARDSRunner - INFO - [run.py:116] - _initialize_layer() - Initializing api layer...
2025-05-25 01:09:12,539 - SBARDSRunner - INFO - [run.py:216] - _initialize_api_layer() - API layer initialized (placeholder)
2025-05-25 01:09:12,540 - SBARDSRunner - INFO - [run.py:81] - _initialize_layers() - Successfully initialized api layer
2025-05-25 01:09:12,540 - SBARDSRunner - INFO - [run.py:266] - run_single_layer() - api layer is running...
