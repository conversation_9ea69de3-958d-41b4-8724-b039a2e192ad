"""
File schemas for SBARDS API

This module contains Pydantic schemas for file operations.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class FileResultBase(BaseModel):
    """Base schema for file results."""
    
    file_path: str = Field(..., description="Path to the file")
    file_hash: str = Field(..., description="SHA-256 hash of the file")
    file_size: Optional[int] = Field(0, description="Size of the file in bytes")
    file_type: Optional[str] = Field("unknown", description="Type of the file")
    is_threat: bool = Field(..., description="Whether the file is identified as a threat")
    threat_type: Optional[str] = Field(None, description="Type of threat if identified")
    threat_level: Optional[str] = Field("safe", description="Level of threat (safe, low, medium, high, critical)")


class FileResultCreate(FileResultBase):
    """Schema for creating file results."""
    
    entropy_score: Optional[str] = Field(None, description="Entropy score of the file")
    signature_info: Optional[str] = Field(None, description="File signature information")


class FileResultResponse(FileResultBase):
    """Schema for file result responses."""
    
    id: int = Field(..., description="Database ID")
    virustotal_result: Optional[Dict[str, Any]] = Field(None, description="VirusTotal scan result")
    static_analysis_result: Optional[Dict[str, Any]] = Field(None, description="Static analysis result")
    entropy_score: Optional[str] = Field(None, description="Entropy score of the file")
    signature_info: Optional[str] = Field(None, description="File signature information")

    class Config:
        """Pydantic config."""
        orm_mode = True


class FileCheckRequest(BaseModel):
    """Schema for file check requests."""
    
    save_result: bool = Field(False, description="Whether to save the result to database")


class FileCheckResponse(BaseModel):
    """Schema for file check responses."""
    
    filename: str = Field(..., description="Name of the checked file")
    file_hash: str = Field(..., description="SHA-256 hash of the file")
    file_size: int = Field(..., description="Size of the file in bytes")
    virustotal_result: Dict[str, Any] = Field(..., description="VirusTotal scan result")
    summary: Optional[Dict[str, Any]] = Field(None, description="Summary of scan results")


class StaticAnalysisRequest(BaseModel):
    """Schema for static analysis requests."""
    
    file_path: str = Field(..., description="Path to the file to analyze")
    analysis_types: List[str] = Field(
        ["signature", "entropy", "hash", "yara"],
        description="Types of analysis to perform"
    )


class StaticAnalysisResponse(BaseModel):
    """Schema for static analysis responses."""
    
    file_path: str = Field(..., description="Path to the analyzed file")
    file_hash: str = Field(..., description="SHA-256 hash of the file")
    file_size: int = Field(..., description="Size of the file in bytes")
    file_type: str = Field(..., description="Detected file type")
    signature_info: Dict[str, Any] = Field(..., description="File signature information")
    entropy_analysis: Dict[str, Any] = Field(..., description="Entropy analysis results")
    hash_results: Dict[str, str] = Field(..., description="Hash calculation results")
    yara_matches: List[Dict[str, Any]] = Field(..., description="YARA rule matches")
    threat_assessment: Dict[str, Any] = Field(..., description="Overall threat assessment")
    processing_time: float = Field(..., description="Time taken for analysis in seconds")
