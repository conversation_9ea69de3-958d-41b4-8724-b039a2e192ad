#!/usr/bin/env python3
"""
SBARDS Backend Server Launcher

This script is a wrapper that launches the backend server from the correct location.
"""

import os
import sys
import subprocess

def main():
    """
    Main function to run the backend server.
    
    This wrapper script changes to the backend directory and runs the actual backend server.
    """
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to the actual backend script
    backend_script = os.path.join(script_dir, "backend", "run_backend.py")
    
    # Check if the backend script exists
    if not os.path.exists(backend_script):
        print(f"Error: Backend script not found at {backend_script}")
        return 1
    
    # Change to the backend directory
    backend_dir = os.path.join(script_dir, "backend")
    os.chdir(backend_dir)
    
    # Forward all command line arguments to the actual script
    cmd = [sys.executable, "run_backend.py"] + sys.argv[1:]
    
    print(f"Starting SBARDS Backend Server from {backend_dir}")
    
    try:
        # Run the actual backend script
        return subprocess.call(cmd)
    except Exception as e:
        print(f"Error starting backend server: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
