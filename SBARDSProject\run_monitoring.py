"""
Run Monitoring Phase for SBARDS

This script runs the Monitoring phase of the SBARDS project.
"""

import os
import sys
import json
import logging
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/monitoring.log', mode='a')
    ]
)

logger = logging.getLogger("SBARDS.Monitoring")

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Load configuration
try:
    with open('config.json', 'r') as f:
        config = json.load(f)
    logger.info("Configuration loaded successfully")
except Exception as e:
    logger.error(f"Error loading configuration: {e}")
    sys.exit(1)

# Import the monitor manager
try:
    from phases.monitoring.monitor_manager import MonitorManager
    logger.info("Monitor Manager imported successfully")
except Exception as e:
    logger.error(f"Error importing Monitor Manager: {e}")
    sys.exit(1)

def main():
    """Run the monitoring phase."""
    logger.info("Starting Monitoring Phase")
    
    # Create monitor manager
    monitor_manager = MonitorManager(config)
    
    # Start monitoring
    if monitor_manager.start_monitoring():
        logger.info("Monitoring started successfully")
    else:
        logger.error("Failed to start monitoring")
        return
    
    try:
        # Keep running until interrupted
        logger.info("Press Ctrl+C to stop monitoring")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping monitoring")
    finally:
        # Stop monitoring
        monitor_manager.stop_monitoring()
        logger.info("Monitoring stopped")

if __name__ == "__main__":
    main()
