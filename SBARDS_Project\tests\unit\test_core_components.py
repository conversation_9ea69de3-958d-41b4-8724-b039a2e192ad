#!/usr/bin/env python3
"""
Unit Tests for Core Components - SBARDS Project

This module provides comprehensive unit tests for all core components
to ensure 100% functionality preservation and performance improvements.

Test Categories:
- Core configuration loading
- Core utilities functionality
- Core logging system
- Error handling and edge cases
"""

import os
import sys
import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from core.config import ConfigManager
    from core.utils import FileUtils, SystemUtils, PerformanceUtils
    from core.logger import setup_logging, get_global_logger
    CORE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Core components not available: {e}")
    CORE_AVAILABLE = False

class TestCoreConfig(unittest.TestCase):
    """Test core configuration functionality."""

    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")

        # Create temporary config file
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.json"

        # Sample configuration
        self.test_config = {
            "core": {
                "project_name": "SBARDS_TEST",
                "version": "2.0.0",
                "debug_mode": True
            },
            "scanner": {
                "enabled": True,
                "parallel_processing": True,
                "max_threads": 4
            },
            "api": {
                "host": "127.0.0.1",
                "port": 8000
            }
        }

        with open(self.config_file, 'w') as f:
            json.dump(self.test_config, f)

    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_config_loading(self):
        """Test configuration file loading."""
        config_manager = ConfigManager(str(self.config_file))

        # Test basic loading
        self.assertTrue(config_manager.load_config())

        # Test configuration access
        self.assertEqual(config_manager.get("core.project_name"), "SBARDS_TEST")
        self.assertEqual(config_manager.get("core.version"), "2.0.0")
        self.assertTrue(config_manager.get("core.debug_mode"))

    def test_config_validation(self):
        """Test configuration validation."""
        config_manager = ConfigManager(str(self.config_file))
        config_manager.load_config()

        # Test required sections
        required_sections = ["core", "scanner", "api"]
        for section in required_sections:
            self.assertTrue(config_manager.has_section(section))

    def test_config_defaults(self):
        """Test default configuration values."""
        # Create empty config
        empty_config_file = Path(self.temp_dir) / "empty_config.json"
        with open(empty_config_file, 'w') as f:
            json.dump({}, f)

        config_manager = ConfigManager(str(empty_config_file))
        config_manager.load_config()

        # Test default values are applied
        self.assertIsNotNone(config_manager.get("core.project_name", "SBARDS"))

    def test_config_error_handling(self):
        """Test configuration error handling."""
        # Test non-existent file
        config_manager = ConfigManager("/non/existent/file.json")
        self.assertFalse(config_manager.load_config())

        # Test invalid JSON
        invalid_config_file = Path(self.temp_dir) / "invalid_config.json"
        with open(invalid_config_file, 'w') as f:
            f.write("invalid json content")

        config_manager = ConfigManager(str(invalid_config_file))
        self.assertFalse(config_manager.load_config())

class TestCoreUtils(unittest.TestCase):
    """Test core utilities functionality."""

    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")

        self.temp_dir = tempfile.mkdtemp()
        self.test_file = Path(self.temp_dir) / "test_file.txt"
        self.test_content = "This is test content for SBARDS testing.\n" * 100

        with open(self.test_file, 'w') as f:
            f.write(self.test_content)

    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_file_hash_calculation(self):
        """Test file hash calculation."""
        # Test SHA-256
        sha256_hash = FileUtils.get_file_hash(str(self.test_file), "sha256")
        self.assertIsNotNone(sha256_hash)
        self.assertEqual(len(sha256_hash), 64)  # SHA-256 is 64 hex characters

        # Test MD5
        md5_hash = FileUtils.get_file_hash(str(self.test_file), "md5")
        self.assertIsNotNone(md5_hash)
        self.assertEqual(len(md5_hash), 32)  # MD5 is 32 hex characters

        # Test consistency
        sha256_hash2 = FileUtils.get_file_hash(str(self.test_file), "sha256")
        self.assertEqual(sha256_hash, sha256_hash2)

    def test_file_operations(self):
        """Test file operation utilities."""
        # Test file existence check
        self.assertTrue(FileUtils.file_exists(str(self.test_file)))
        self.assertFalse(FileUtils.file_exists("/non/existent/file.txt"))

        # Test file size
        file_size = FileUtils.get_file_size(str(self.test_file))
        self.assertGreater(file_size, 0)

        # Check that file size is reasonable (account for line ending differences)
        expected_min_size = len(self.test_content)
        expected_max_size = len(self.test_content) + 200  # Allow for line ending differences
        self.assertGreaterEqual(file_size, expected_min_size)
        self.assertLessEqual(file_size, expected_max_size)

    def test_system_info(self):
        """Test system information utilities."""
        # Test platform info
        platform_info = SystemUtils.get_platform_info()
        self.assertIn("system", platform_info)
        self.assertIn("architecture", platform_info)
        self.assertIn("is_windows", platform_info)
        self.assertIn("is_linux", platform_info)

        # Test CPU info
        cpu_count = SystemUtils.get_cpu_count()
        self.assertIsInstance(cpu_count, int)
        self.assertGreater(cpu_count, 0)

    def test_performance_utils(self):
        """Test performance measurement utilities."""
        import time

        # Test timing decorator
        @PerformanceUtils.measure_time
        def test_function():
            time.sleep(0.1)
            return "test_result"

        result, duration = test_function()
        self.assertEqual(result, "test_result")
        self.assertGreaterEqual(duration, 0.1)
        self.assertLess(duration, 0.2)  # Should be close to 0.1 seconds

    def test_error_handling(self):
        """Test error handling in utilities."""
        # Test hash calculation with non-existent file
        hash_result = FileUtils.get_file_hash("/non/existent/file.txt", "sha256")
        self.assertIsNone(hash_result)

        # Test invalid hash algorithm - our implementation falls back to sha256
        hash_result = FileUtils.get_file_hash(str(self.test_file), "invalid_algorithm")
        # The implementation falls back to sha256, so it should return a valid hash
        self.assertIsNotNone(hash_result)
        self.assertEqual(len(hash_result), 64)  # SHA-256 fallback

class TestCoreLogging(unittest.TestCase):
    """Test core logging functionality."""

    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")

        self.temp_dir = tempfile.mkdtemp()
        self.log_file = Path(self.temp_dir) / "test.log"

    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_logger_setup(self):
        """Test logger setup and configuration."""
        # Test basic logger setup with correct parameters
        logger_manager = setup_logging(
            log_dir=str(self.temp_dir),
            log_level="INFO"
        )

        self.assertIsNotNone(logger_manager)

        # Test getting logger
        logger = get_global_logger().get_logger("test_component")
        self.assertIsNotNone(logger)

    def test_logging_levels(self):
        """Test different logging levels."""
        logger_manager = setup_logging(
            log_dir=str(self.temp_dir),
            log_level="DEBUG"
        )

        logger = get_global_logger().get_logger("test_levels")

        # Test different log levels
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")

        # Check if log files were created
        log_files = list(Path(self.temp_dir).glob("*.log"))
        self.assertGreater(len(log_files), 0)

    def test_layer_loggers(self):
        """Test layer-specific loggers."""
        logger_manager = setup_logging(
            log_dir=str(self.temp_dir),
            log_level="INFO"
        )

        # Test different layer loggers
        capture_logger = get_global_logger().get_layer_logger("capture")
        static_logger = get_global_logger().get_layer_logger("static_analysis")

        self.assertIsNotNone(capture_logger)
        self.assertIsNotNone(static_logger)

        # Test logging from different layers
        capture_logger.info("Capture layer message")
        static_logger.info("Static analysis layer message")

        # Verify log files were created
        log_files = list(Path(self.temp_dir).glob("*.log"))
        self.assertGreater(len(log_files), 0)

class TestPerformanceComparison(unittest.TestCase):
    """Test performance improvements over old system."""

    def setUp(self):
        """Set up performance test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")

        self.temp_dir = tempfile.mkdtemp()

        # Create test files of different sizes
        self.small_file = Path(self.temp_dir) / "small.txt"
        self.medium_file = Path(self.temp_dir) / "medium.txt"
        self.large_file = Path(self.temp_dir) / "large.txt"

        # Small file (1KB)
        with open(self.small_file, 'w') as f:
            f.write("test content\n" * 100)

        # Medium file (100KB)
        with open(self.medium_file, 'w') as f:
            f.write("test content for medium file\n" * 4000)

        # Large file (1MB)
        with open(self.large_file, 'w') as f:
            f.write("test content for large file testing\n" * 30000)

    def tearDown(self):
        """Clean up performance test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_hash_calculation_performance(self):
        """Test hash calculation performance."""
        import time

        test_files = [
            (self.small_file, "small"),
            (self.medium_file, "medium"),
            (self.large_file, "large")
        ]

        for file_path, size_name in test_files:
            # Measure hash calculation time
            start_time = time.time()
            hash_result = FileUtils.get_file_hash(str(file_path), "sha256")
            end_time = time.time()

            duration = end_time - start_time
            file_size = file_path.stat().st_size
            throughput = file_size / duration / 1024 / 1024  # MB/s

            # Verify hash was calculated
            self.assertIsNotNone(hash_result)
            self.assertEqual(len(hash_result), 64)

            # Performance expectations (relaxed for testing)
            if size_name == "small":
                self.assertLess(duration, 1.0)  # Less than 1s for small files
            elif size_name == "medium":
                self.assertLess(duration, 2.0)  # Less than 2s for medium files
            elif size_name == "large":
                self.assertLess(duration, 5.0)  # Less than 5s for large files

            print(f"{size_name.capitalize()} file ({file_size} bytes): {duration:.3f}s, {throughput:.1f} MB/s")

    def test_file_operations_performance(self):
        """Test file operations performance."""
        import time

        # Test multiple file existence checks
        start_time = time.time()
        for _ in range(1000):
            FileUtils.file_exists(str(self.small_file))
        end_time = time.time()

        duration = end_time - start_time
        operations_per_second = 1000 / duration

        # Should be reasonably fast (at least 100 operations per second)
        self.assertGreater(operations_per_second, 100)
        print(f"File existence checks: {operations_per_second:.0f} ops/sec")

def run_unit_tests():
    """Run all unit tests."""
    # Create test suite
    test_classes = [
        TestCoreConfig,
        TestCoreUtils,
        TestCoreLogging,
        TestPerformanceComparison
    ]

    suite = unittest.TestSuite()

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    return result.wasSuccessful()

if __name__ == "__main__":
    print("🧪 Running Core Components Unit Tests")
    print("=" * 50)

    success = run_unit_tests()

    if success:
        print("\n✅ All unit tests passed!")
        print("🚀 Core components are working correctly")
    else:
        print("\n❌ Some unit tests failed!")
        print("🔧 Please check the test output for details")

    exit(0 if success else 1)
