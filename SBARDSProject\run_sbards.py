#!/usr/bin/env python3
"""
SBARDS Project Runner

This script provides a unified interface to run the SBARDS project components.
"""

import os
import sys
import argparse
import subprocess
import time
import logging
import webbrowser
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sbards_runner.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SBARDS.Runner")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="SBARDS Project Runner",
        usage="%(prog)s command [options]"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Start command
    start_parser = subparsers.add_parser("start", help="Start SBARDS components")
    start_parser.add_argument("--backend", action="store_true", help="Start the backend API server")
    start_parser.add_argument("--scan", action="store_true", help="Run a scan")
    start_parser.add_argument("--path", default=".", help="Path to scan (default: current directory)")
    start_parser.add_argument("--dashboard", action="store_true", help="Open the dashboard in a browser")
    start_parser.add_argument("--all", action="store_true", help="Start all components")
    
    # Stop command
    stop_parser = subparsers.add_parser("stop", help="Stop SBARDS components")
    stop_parser.add_argument("--backend", action="store_true", help="Stop the backend API server")
    stop_parser.add_argument("--all", action="store_true", help="Stop all components")
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Check the status of SBARDS components")
    
    # Help command
    subparsers.add_parser("help", help="Show help message")
    
    return parser.parse_args()

def start_backend():
    """Start the backend API server."""
    logger.info("Starting SBARDS Backend API server...")
    
    backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backend")
    
    # Check if the backend directory exists
    if not os.path.exists(backend_dir):
        logger.error(f"Backend directory not found: {backend_dir}")
        return False
    
    # Start the backend server
    try:
        process = subprocess.Popen(
            [sys.executable, "run_backend.py"],
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for the server to start
        time.sleep(2)
        
        # Check if the process is still running
        if process.poll() is None:
            logger.info("Backend API server started successfully.")
            return True
        else:
            stdout, stderr = process.communicate()
            logger.error(f"Backend API server failed to start: {stderr}")
            return False
    except Exception as e:
        logger.error(f"Error starting backend API server: {e}")
        return False

def run_scan(path="."):
    """Run a scan on the specified path."""
    logger.info(f"Running scan on {path}...")
    
    try:
        # Run the scanner
        process = subprocess.run(
            [sys.executable, "run_scanner.py", "scan", path, "--send-to-backend", "--open-report"],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        logger.info("Scan completed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Scan failed: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Error running scan: {e}")
        return False

def open_dashboard():
    """Open the dashboard in a browser."""
    logger.info("Opening SBARDS Dashboard...")
    
    try:
        webbrowser.open("http://localhost:8000/dashboard")
        logger.info("Dashboard opened in browser.")
        return True
    except Exception as e:
        logger.error(f"Error opening dashboard: {e}")
        return False

def stop_backend():
    """Stop the backend API server."""
    logger.info("Stopping SBARDS Backend API server...")
    
    try:
        # Find and kill the backend process
        if sys.platform == "win32":
            subprocess.run(["taskkill", "/f", "/im", "python.exe", "/fi", "WindowTitle eq SBARDS Backend"])
        else:
            subprocess.run(["pkill", "-f", "run_backend.py"])
        
        logger.info("Backend API server stopped.")
        return True
    except Exception as e:
        logger.error(f"Error stopping backend API server: {e}")
        return False

def check_status():
    """Check the status of SBARDS components."""
    logger.info("Checking SBARDS components status...")
    
    # Check if the backend is running
    try:
        import requests
        response = requests.get("http://localhost:8000/api")
        if response.status_code == 200:
            print("Backend API server: RUNNING")
        else:
            print("Backend API server: ERROR")
    except:
        print("Backend API server: NOT RUNNING")
    
    return True

def command_help():
    """Show help message."""
    print(__doc__)
    return True

def main():
    """Main function."""
    args = parse_arguments()
    
    if args.command == "start":
        if args.all or args.backend:
            start_backend()
        
        if args.all or args.scan:
            run_scan(args.path)
        
        if args.all or args.dashboard:
            open_dashboard()
    
    elif args.command == "stop":
        if args.all or args.backend:
            stop_backend()
    
    elif args.command == "status":
        check_status()
    
    elif args.command == "help" or args.command is None:
        command_help()
    
    else:
        print(f"Unknown command: {args.command}")
        print("Use 'python run_sbards.py help' for usage information.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
