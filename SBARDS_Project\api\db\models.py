"""
Database models for SBARDS API

This module contains all database models for the SBARDS API.
"""

import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from .base import Base


class ScanReport(Base):
    """Scan report model."""
    
    __tablename__ = "scan_reports"

    id = Column(Integer, primary_key=True, index=True)
    scan_id = Column(String, unique=True, index=True)
    timestamp = Column(DateTime, default=datetime.datetime.utcnow)
    scan_path = Column(String)
    files_scanned = Column(Integer)
    threats_found = Column(Integer)
    report_path = Column(String)
    report_content = Column(Text)
    scan_type = Column(String, default="standard")  # standard, static_analysis, dynamic_analysis
    performance_metrics = Column(Text)  # JSON string with performance data

    # Relationships
    file_results = relationship("FileResult", back_populates="scan_report", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<ScanReport(id={self.id}, scan_id={self.scan_id}, threats_found={self.threats_found})>"


class FileResult(Base):
    """File result model."""
    
    __tablename__ = "file_results"

    id = Column(Integer, primary_key=True, index=True)
    scan_report_id = Column(Integer, ForeignKey("scan_reports.id"))
    file_path = Column(String)
    file_hash = Column(String)
    file_size = Column(Integer)
    file_type = Column(String)
    is_threat = Column(Boolean, default=False)
    threat_type = Column(String, nullable=True)
    threat_level = Column(String, default="safe")  # safe, low, medium, high, critical
    virustotal_result = Column(Text, nullable=True)
    static_analysis_result = Column(Text, nullable=True)  # JSON string
    entropy_score = Column(String, nullable=True)
    signature_info = Column(Text, nullable=True)

    # Relationships
    scan_report = relationship("ScanReport", back_populates="file_results")

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<FileResult(id={self.id}, file_path={self.file_path}, is_threat={self.is_threat})>"
