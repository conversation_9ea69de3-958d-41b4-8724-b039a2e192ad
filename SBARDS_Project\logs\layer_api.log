2025-05-25 00:09:23,754 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:09:23,772 - layer.api.services.static_analysis - INFO - [static_analysis.py:78] - analyze_file() - Static analysis completed for test_file.txt in 0.015s
2025-05-25 00:09:23,940 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:09:23,944 - layer.api.middleware.cors - INFO - [cors.py:69] - get_cors_middleware() - CORS middleware configured with origins: ['*']
2025-05-25 00:10:25,133 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:10:25,140 - layer.api.services.static_analysis - INFO - [static_analysis.py:78] - analyze_file() - Static analysis completed for test_file.txt in 0.003s
2025-05-25 00:10:25,283 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:14:46,066 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:16:29,549 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:20:21,865 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:21:22,423 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:24:14,506 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:24:14,545 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:24:14,567 - layer.api.services.static_analysis - INFO - [static_analysis.py:78] - analyze_file() - Static analysis completed for test_static.txt in 0.003s
2025-05-25 00:26:28,098 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:28:19,743 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:29:10,091 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:35:37,717 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:36:33,787 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:36:38,721 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:36:41,518 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:39:29,430 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in accept header from 127.0.0.1
2025-05-25 00:42:22,476 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:42:22,529 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in accept header from testclient
2025-05-25 00:42:33,646 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:42:33,701 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:42:33,740 - layer.api.services.static_analysis - INFO - [static_analysis.py:78] - analyze_file() - Static analysis completed for test_final.txt in 0.018s
2025-05-25 00:42:33,743 - layer.api.services.reports - INFO - [reports.py:39] - create_scan_report() - Creating scan report with ID: test_final_1748122953
2025-05-25 00:42:33,773 - layer.api.services.reports - INFO - [reports.py:73] - create_scan_report() - Successfully created scan report with ID: test_final_1748122953
2025-05-25 00:42:33,789 - layer.api.services.integration - INFO - [integration.py:47] - process_comprehensive_scan() - Starting comprehensive scan: test_integration
2025-05-25 00:42:33,790 - layer.api.services.integration - INFO - [integration.py:114] - _integrate_capture_layer() - Integrating with capture layer
2025-05-25 00:42:33,791 - layer.api.services.integration - INFO - [integration.py:144] - _integrate_static_analysis_layer() - Integrating with static analysis layer
2025-05-25 00:42:33,810 - layer.api.services.static_analysis - INFO - [static_analysis.py:78] - analyze_file() - Static analysis completed for test_final.txt in 0.017s
2025-05-25 00:42:33,811 - layer.api.services.virustotal - WARNING - [virustotal.py:42] - check_file_hash() - VirusTotal API key not configured
2025-05-25 00:42:33,812 - layer.api.services.integration - INFO - [integration.py:193] - _integrate_monitoring_layer() - Integrating with monitoring layer
2025-05-25 00:42:33,923 - layer.api.services.integration - INFO - [integration.py:93] - process_comprehensive_scan() - Comprehensive scan completed: test_integration
2025-05-25 00:42:56,811 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:42:56,952 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:42:59,298 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:42:59,333 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:42:59,788 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:42:59,912 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:43:00,507 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:43:00,537 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:44:34,047 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:44:58,164 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:47:38,356 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:47:43,875 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:47:44,125 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:48:20,779 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in accept header from 127.0.0.1
2025-05-25 00:49:19,578 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:50:29,293 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 00:50:33,775 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:50:34,056 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:51:05,025 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 00:51:05,210 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 01:19:42,000 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 01:20:21,299 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 01:21:45,091 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in accept header from 127.0.0.1
2025-05-25 01:23:00,603 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in accept header from 127.0.0.1
2025-05-25 01:23:59,713 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 22:07:23,413 - layer.api.db - ERROR - [session.py:68] - init_db() - Error initializing database: (sqlite3.DatabaseError) database disk image is malformed
[SQL: PRAGMA main.table_info("scan_reports")]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-05-25 22:40:21,428 - layer.api.db - ERROR - [session.py:68] - init_db() - Error initializing database: (sqlite3.DatabaseError) database disk image is malformed
[SQL: PRAGMA main.table_info("scan_reports")]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-05-25 22:41:55,151 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 22:46:18,467 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 22:49:41,383 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 22:50:18,646 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 22:50:18,682 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 22:50:18,682 - layer.api - INFO - [main.py:142] - startup_event() - Database initialized
2025-05-25 22:50:18,692 - layer.api - INFO - [main.py:146] - startup_event() - Capture layer initialized
2025-05-25 22:50:18,692 - layer.api - INFO - [main.py:148] - startup_event() - SBARDS API startup completed successfully
2025-05-25 22:51:00,874 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 22:51:17,199 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 22:51:33,048 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 22:51:51,284 - layer.api.middleware.security - WARNING - [security.py:115] - dispatch() - Malicious input detected: Malicious pattern in user-agent header from 127.0.0.1
2025-05-25 22:53:48,242 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 22:53:48,384 - layer.api.db - INFO - [session.py:64] - init_db() - Database initialized successfully
2025-05-25 22:53:48,400 - layer.api - INFO - [main.py:142] - startup_event() - Database initialized
2025-05-25 22:53:48,420 - layer.api - INFO - [main.py:146] - startup_event() - Capture layer initialized
2025-05-25 22:53:48,421 - layer.api - INFO - [main.py:148] - startup_event() - SBARDS API startup completed successfully
2025-05-25 22:54:31,308 - layer.api.middleware.logging - INFO - [logging.py:227] - _log_request() - Request: GET /docs - Client: 127.0.0.1
2025-05-25 22:54:31,310 - layer.api.middleware.logging - WARNING - [logging.py:264] - _log_response() - Response: GET /docs - Status: 404 - Time: 1.89ms
2025-05-25 22:54:47,483 - layer.api.middleware.logging - INFO - [logging.py:227] - _log_request() - Request: GET /api - Client: 127.0.0.1
2025-05-25 22:54:47,490 - layer.api.middleware.logging - INFO - [logging.py:264] - _log_response() - Response: GET /api - Status: 200 - Time: 6.99ms
2025-05-25 22:55:19,502 - layer.api.middleware.logging - INFO - [logging.py:227] - _log_request() - Request: GET /api/capture/status - Client: 127.0.0.1
2025-05-25 22:55:19,519 - layer.api.middleware.logging - INFO - [logging.py:264] - _log_response() - Response: GET /api/capture/status - Status: 200 - Time: 16.47ms
2025-05-25 22:55:57,410 - layer.api.middleware.logging - INFO - [logging.py:227] - _log_request() - Request: GET /dashboard - Client: 127.0.0.1
2025-05-25 22:55:57,413 - layer.api.middleware.logging - INFO - [logging.py:264] - _log_response() - Response: GET /dashboard - Status: 200 - Time: 3.30ms
2025-05-25 22:56:02,523 - layer.api.middleware.logging - INFO - [logging.py:227] - _log_request() - Request: GET /api/docs - Client: 127.0.0.1
2025-05-25 22:56:02,525 - layer.api.middleware.logging - INFO - [logging.py:264] - _log_response() - Response: GET /api/docs - Status: 200 - Time: 2.08ms
2025-05-25 22:57:13,201 - layer.api.middleware.logging - INFO - [logging.py:227] - _log_request() - Request: GET / - Client: 127.0.0.1
2025-05-25 22:57:13,203 - layer.api.middleware.logging - INFO - [logging.py:264] - _log_response() - Response: GET / - Status: 307 - Time: 2.92ms
2025-05-25 22:57:13,236 - layer.api.middleware.logging - INFO - [logging.py:227] - _log_request() - Request: GET /dashboard - Client: 127.0.0.1
2025-05-25 22:57:13,237 - layer.api.middleware.logging - INFO - [logging.py:264] - _log_response() - Response: GET /dashboard - Status: 200 - Time: 1.65ms
2025-05-25 22:57:24,154 - layer.api.middleware.logging - INFO - [logging.py:227] - _log_request() - Request: GET /docs - Client: 127.0.0.1
2025-05-25 22:57:24,155 - layer.api.middleware.logging - WARNING - [logging.py:264] - _log_response() - Response: GET /docs - Status: 404 - Time: 1.72ms
2025-05-25 22:58:37,646 - layer.api - INFO - [main.py:159] - shutdown_event() - Capture layer shutdown completed
2025-05-25 22:58:37,646 - layer.api - INFO - [main.py:161] - shutdown_event() - SBARDS API shutdown completed successfully
2025-05-25 23:05:25,837 - layer.api - INFO - [main.py:280] - upload_file() - File uploaded: test.txt (34 bytes)
2025-05-25 23:07:14,278 - layer.api - INFO - [main.py:280] - upload_file() - File uploaded: test.txt (34 bytes)
2025-05-25 23:15:21,706 - layer.api - INFO - [main.py:591] - upload_file() - File uploaded: test.txt (34 bytes)
2025-05-25 23:22:25,615 - layer.api - INFO - [main.py:591] - upload_file() - File uploaded: test_document.txt (64 bytes)
2025-05-25 23:22:25,625 - layer.api - INFO - [main.py:591] - upload_file() - File uploaded: suspicious_file.txt (59 bytes)
2025-05-25 23:22:25,633 - layer.api - INFO - [main.py:591] - upload_file() - File uploaded: malware.exe (16 bytes)
2025-05-25 23:22:25,656 - layer.api - INFO - [main.py:591] - upload_file() - File uploaded: photo.jpg (20 bytes)
2025-05-25 23:22:25,664 - layer.api - INFO - [main.py:591] - upload_file() - File uploaded: encrypted_files.zip (31 bytes)
2025-05-26 02:01:03,585 - layer.api - INFO - [main.py:87] - startup_event() - Starting SBARDS API server...
2025-05-26 02:01:03,757 - layer.api - INFO - [main.py:93] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 02:11:42,593 - layer.api - INFO - [main.py:87] - startup_event() - Starting SBARDS API server...
2025-05-26 02:11:42,873 - layer.api - INFO - [main.py:93] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 02:15:43,063 - layer.api - INFO - [main.py:87] - startup_event() - Starting SBARDS API server...
2025-05-26 02:15:43,242 - layer.api - INFO - [main.py:93] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 02:19:59,576 - layer.api - INFO - [main.py:102] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 02:20:00,438 - layer.api - INFO - [main.py:107] - shutdown_event() - Capture layer shutdown completed
2025-05-26 02:24:46,649 - layer.api - INFO - [main.py:87] - startup_event() - Starting SBARDS API server...
2025-05-26 02:24:46,814 - layer.api - INFO - [main.py:93] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 02:48:13,436 - layer.api - INFO - [main.py:87] - startup_event() - Starting SBARDS API server...
2025-05-26 02:48:13,603 - layer.api - INFO - [main.py:93] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 02:48:36,830 - layer.api - INFO - [main.py:102] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 02:48:36,899 - layer.api - INFO - [main.py:107] - shutdown_event() - Capture layer shutdown completed
2025-05-26 03:10:38,476 - layer.api - INFO - [main.py:87] - startup_event() - Starting SBARDS API server...
2025-05-26 03:10:38,766 - layer.api - INFO - [main.py:93] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 03:12:53,799 - layer.api - INFO - [main.py:87] - startup_event() - Starting SBARDS API server...
2025-05-26 03:12:53,992 - layer.api - INFO - [main.py:93] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 03:39:34,086 - layer.api - INFO - [main.py:87] - startup_event() - Starting SBARDS API server...
2025-05-26 03:39:34,239 - layer.api - INFO - [main.py:93] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 03:40:04,926 - layer.api - INFO - [main.py:102] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 03:40:05,598 - layer.api - INFO - [main.py:107] - shutdown_event() - Capture layer shutdown completed
2025-05-26 04:26:55,856 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 04:26:55,944 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 04:26:56,219 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 04:26:56,404 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 04:26:56,404 - layer.api - INFO - [main.py:487] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 04:29:37,923 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 04:29:37,971 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 04:29:38,234 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 04:29:38,395 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 04:29:38,395 - layer.api - INFO - [main.py:487] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 04:29:39,531 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 04:29:40,411 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-26 04:29:55,657 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 04:29:55,752 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-26 04:30:05,573 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 04:30:05,622 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 04:30:05,880 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 04:30:06,039 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 04:30:06,040 - layer.api - INFO - [main.py:487] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 04:36:56,440 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 04:36:56,944 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-26 05:00:28,998 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 05:00:29,107 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 05:00:29,396 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 05:00:29,590 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 05:00:29,590 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 05:03:52,320 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 05:03:53,264 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-26 05:07:01,888 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 05:07:01,941 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 05:07:02,234 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 05:07:02,404 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 05:07:02,404 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 05:07:03,539 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 05:07:04,431 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-26 05:07:24,733 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 05:07:24,804 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 05:07:25,115 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 05:07:25,286 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 05:07:25,286 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 05:13:36,640 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 05:13:37,049 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-26 05:36:13,652 - layer.api - WARNING - [main.py:45] - <module>() - Capture router not available: name 'HTMLResponse' is not defined
2025-05-26 05:36:15,869 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 05:36:15,937 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 05:36:16,271 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 05:36:16,272 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 05:37:35,807 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 05:58:15,522 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 05:58:15,584 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 05:58:15,864 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 05:58:16,036 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 05:58:16,036 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 05:58:17,178 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 05:58:18,054 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-26 06:05:30,985 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 06:05:31,051 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 06:05:31,332 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 06:05:31,499 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 06:05:31,500 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 06:28:41,975 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 06:28:42,035 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 06:28:42,308 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 06:28:42,472 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 06:28:42,473 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-26 06:30:11,495 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-26 06:30:12,112 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-26 06:33:01,860 - layer.api - INFO - [main.py:136] - <module>() - Analytics router included successfully
2025-05-26 06:33:01,907 - layer.api - INFO - [main.py:146] - <module>() - Notifications router included successfully
2025-05-26 06:33:02,157 - layer.api - INFO - [main.py:155] - startup_event() - Starting SBARDS API server...
2025-05-26 06:33:02,332 - layer.api - INFO - [main.py:161] - startup_event() - Capture layer initialized successfully on startup
2025-05-26 06:33:02,332 - layer.api - INFO - [main.py:729] - start_background_tasks() - Notification service initialized with WebSocket manager
2025-05-27 21:06:46,592 - layer.api - INFO - [main.py:170] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 21:06:47,322 - layer.api - INFO - [main.py:175] - shutdown_event() - Capture layer shutdown completed
2025-05-27 22:06:43,593 - layer.api - WARNING - [main.py:45] - <module>() - Capture router not available: cannot import name 'prescanning' from partially initialized module 'api.routers' (most likely due to a circular import) (G:\SBARDS\SBARDS_Project\api\routers\__init__.py)
2025-05-27 22:06:43,911 - layer.api - INFO - [main.py:137] - startup_event() - Starting SBARDS API server...
2025-05-27 22:11:24,215 - layer.api - WARNING - [main.py:45] - <module>() - Capture router not available: cannot import name 'prescanning' from partially initialized module 'api.routers' (most likely due to a circular import) (G:\SBARDS\SBARDS_Project\api\routers\__init__.py)
2025-05-27 22:13:04,202 - layer.api - INFO - [main.py:137] - startup_event() - Starting SBARDS API server...
2025-05-27 22:13:04,520 - layer.api - INFO - [main.py:143] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:15:29,914 - layer.api - INFO - [main.py:137] - startup_event() - Starting SBARDS API server...
2025-05-27 22:15:30,378 - layer.api - INFO - [main.py:143] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:16:00,134 - layer.api - INFO - [main.py:152] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 22:16:00,620 - layer.api - INFO - [main.py:157] - shutdown_event() - Capture layer shutdown completed
2025-05-27 22:16:35,311 - layer.api - INFO - [main.py:137] - startup_event() - Starting SBARDS API server...
2025-05-27 22:16:35,488 - layer.api - INFO - [main.py:143] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:21:09,469 - layer.api - INFO - [main.py:152] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 22:21:09,674 - layer.api - INFO - [main.py:157] - shutdown_event() - Capture layer shutdown completed
2025-05-27 22:27:49,389 - layer.api - INFO - [main.py:216] - startup_event() - Starting SBARDS API server...
2025-05-27 22:27:49,755 - layer.api - INFO - [main.py:222] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:29:12,804 - layer.api - INFO - [main.py:216] - startup_event() - Starting SBARDS API server...
2025-05-27 22:29:12,989 - layer.api - INFO - [main.py:222] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:34:49,862 - layer.api - INFO - [main.py:231] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 22:34:50,471 - layer.api - INFO - [main.py:236] - shutdown_event() - Capture layer shutdown completed
2025-05-27 22:36:33,707 - layer.api - INFO - [main.py:216] - startup_event() - Starting SBARDS API server...
2025-05-27 22:36:33,966 - layer.api - INFO - [main.py:222] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:36:53,342 - layer.api - INFO - [main.py:231] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 22:36:54,216 - layer.api - INFO - [main.py:236] - shutdown_event() - Capture layer shutdown completed
2025-05-27 22:38:40,735 - layer.api - INFO - [main.py:216] - startup_event() - Starting SBARDS API server...
2025-05-27 22:38:40,920 - layer.api - INFO - [main.py:222] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:39:06,404 - layer.api - INFO - [main.py:231] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 22:39:07,159 - layer.api - INFO - [main.py:236] - shutdown_event() - Capture layer shutdown completed
2025-05-27 22:43:54,884 - layer.api - INFO - [main.py:216] - startup_event() - Starting SBARDS API server...
2025-05-27 22:43:55,096 - layer.api - INFO - [main.py:222] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:47:22,689 - layer.api - INFO - [main.py:231] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 22:47:22,723 - layer.api - INFO - [main.py:236] - shutdown_event() - Capture layer shutdown completed
2025-05-27 22:51:23,685 - layer.api - INFO - [main.py:216] - startup_event() - Starting SBARDS API server...
2025-05-27 22:51:23,924 - layer.api - INFO - [main.py:222] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 22:51:52,380 - layer.api - INFO - [main.py:231] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 22:51:53,320 - layer.api - INFO - [main.py:236] - shutdown_event() - Capture layer shutdown completed
2025-05-27 22:54:45,235 - layer.api - INFO - [main.py:216] - startup_event() - Starting SBARDS API server...
2025-05-27 22:54:45,456 - layer.api - INFO - [main.py:222] - startup_event() - Capture layer initialized successfully on startup
2025-05-27 23:01:09,277 - layer.api - INFO - [main.py:231] - shutdown_event() - Shutting down SBARDS API server...
2025-05-27 23:01:10,187 - layer.api - INFO - [main.py:236] - shutdown_event() - Capture layer shutdown completed
2025-05-27 23:11:58,062 - layer.api - INFO - [main.py:222] - lifespan() - Starting SBARDS API server...
2025-05-27 23:11:58,274 - layer.api - INFO - [main.py:227] - lifespan() - Capture layer initialized successfully
2025-05-27 23:11:58,275 - layer.api - ERROR - [main.py:236] - lifespan() - Failed to start background tasks: name 'broadcast_data' is not defined
2025-05-27 23:12:42,918 - layer.api - INFO - [main.py:241] - lifespan() - Shutting down SBARDS API server...
2025-05-27 23:12:43,683 - layer.api - INFO - [main.py:246] - lifespan() - Capture layer shutdown completed
2025-05-27 23:12:56,204 - layer.api - INFO - [main.py:222] - lifespan() - Starting SBARDS API server...
2025-05-27 23:12:56,668 - layer.api - INFO - [main.py:227] - lifespan() - Capture layer initialized successfully
2025-05-27 23:12:56,669 - layer.api - ERROR - [main.py:236] - lifespan() - Failed to start background tasks: name 'broadcast_data' is not defined
