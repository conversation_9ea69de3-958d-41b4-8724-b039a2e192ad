"""
VirusTotal Integration for SBARDS Static Analysis Layer

This module provides integration with VirusTotal API for enhanced threat intelligence
and file reputation checking with rate limiting and caching.

Features:
- VirusTotal API v3 integration
- File hash checking and submission
- Rate limiting and quota management
- Response caching and optimization
- Comprehensive threat intelligence
- Batch processing support
"""

import os
import sys
import time
import hashlib
import json
import threading
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.logger import get_global_logger
from core.utils import FileUtils, DataUtils
from core.constants import VIRUSTOTAL_API_URL, VIRUSTOTAL_RATE_LIMIT

@dataclass
class VirusTotalResult:
    """Represents a VirusTotal scan result."""
    file_hash: str
    scan_date: str
    positives: int
    total: int
    detection_ratio: float
    permalink: str
    scan_results: Dict[str, Dict[str, Any]]
    file_info: Dict[str, Any]
    is_malicious: bool
    threat_level: str
    confidence_score: float
    
    # Additional metadata
    first_seen: Optional[str] = None
    last_seen: Optional[str] = None
    submission_names: List[str] = None
    file_types: List[str] = None

@dataclass
class VirusTotalStats:
    """VirusTotal API usage statistics."""
    requests_made: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    api_errors: int = 0
    rate_limit_hits: int = 0
    quota_remaining: int = 0
    last_request_time: float = 0.0

class VirusTotalClient:
    """
    Enhanced VirusTotal client with rate limiting, caching, and optimization.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize VirusTotal client.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = get_global_logger().get_layer_logger("static_analysis")
        
        # API configuration
        self.api_key = config.get("api_key", "")
        self.api_url = config.get("api_url", "https://www.virustotal.com/vtapi/v2/")
        self.rate_limit = config.get("rate_limit", VIRUSTOTAL_RATE_LIMIT)  # requests per minute
        self.timeout = config.get("timeout", 30)
        self.max_retries = config.get("max_retries", 3)
        
        # Caching configuration
        self.enable_caching = config.get("enable_caching", True)
        self.cache_duration_hours = config.get("cache_duration_hours", 24)
        self.cache_file = config.get("cache_file", "data/virus_total_cache.json")
        
        # Rate limiting
        self.request_times: List[float] = []
        self.rate_limit_lock = threading.Lock()
        
        # Cache storage
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_lock = threading.Lock()
        
        # Statistics
        self.stats = VirusTotalStats()
        
        # HTTP session with retry strategy
        self.session = self._create_session()
        
        # Initialize
        self._load_cache()
        self._validate_api_key()
        
        self.logger.info("VirusTotal client initialized")
    
    def _create_session(self) -> requests.Session:
        """Create HTTP session with retry strategy."""
        session = requests.Session()
        
        # Retry strategy
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Default headers
        session.headers.update({
            "User-Agent": "SBARDS/2.0",
            "Accept": "application/json"
        })
        
        return session
    
    def _validate_api_key(self):
        """Validate VirusTotal API key."""
        if not self.api_key:
            self.logger.warning("No VirusTotal API key provided")
            return False
        
        try:
            # Test API key with a simple request
            response = self._make_request("GET", "file/report", {"resource": "test"})
            if response and response.get("response_code") is not None:
                self.logger.info("VirusTotal API key validated successfully")
                return True
        except Exception as e:
            self.logger.error(f"VirusTotal API key validation failed: {e}")
        
        return False
    
    def _load_cache(self):
        """Load cache from file."""
        if not self.enable_caching:
            return
        
        try:
            cache_data = DataUtils.load_json(self.cache_file)
            if cache_data:
                # Filter expired entries
                current_time = time.time()
                valid_cache = {}
                
                for key, entry in cache_data.items():
                    if "timestamp" in entry:
                        age_hours = (current_time - entry["timestamp"]) / 3600
                        if age_hours < self.cache_duration_hours:
                            valid_cache[key] = entry
                
                with self.cache_lock:
                    self.cache = valid_cache
                
                self.logger.info(f"Loaded {len(self.cache)} cached VirusTotal results")
        except Exception as e:
            self.logger.warning(f"Failed to load VirusTotal cache: {e}")
    
    def _save_cache(self):
        """Save cache to file."""
        if not self.enable_caching:
            return
        
        try:
            with self.cache_lock:
                cache_data = self.cache.copy()
            
            DataUtils.save_json(cache_data, self.cache_file)
        except Exception as e:
            self.logger.warning(f"Failed to save VirusTotal cache: {e}")
    
    def _wait_for_rate_limit(self):
        """Wait if necessary to respect rate limits."""
        with self.rate_limit_lock:
            current_time = time.time()
            
            # Remove old request times (older than 1 minute)
            self.request_times = [t for t in self.request_times if current_time - t < 60]
            
            # Check if we need to wait
            if len(self.request_times) >= self.rate_limit:
                wait_time = 60 - (current_time - self.request_times[0])
                if wait_time > 0:
                    self.logger.info(f"Rate limit reached, waiting {wait_time:.1f} seconds")
                    self.stats.rate_limit_hits += 1
                    time.sleep(wait_time)
                    
                    # Clean up old times again
                    current_time = time.time()
                    self.request_times = [t for t in self.request_times if current_time - t < 60]
            
            # Record this request time
            self.request_times.append(current_time)
            self.stats.last_request_time = current_time
    
    def _make_request(self, method: str, endpoint: str, params: Dict[str, Any] = None, 
                     data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Make API request with rate limiting and error handling."""
        if not self.api_key:
            return None
        
        # Wait for rate limit
        self._wait_for_rate_limit()
        
        # Prepare request
        url = f"{self.api_url}{endpoint}"
        request_params = params or {}
        request_params["apikey"] = self.api_key
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=request_params, timeout=self.timeout)
            elif method.upper() == "POST":
                response = self.session.post(url, params=request_params, data=data, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            
            self.stats.requests_made += 1
            
            # Parse JSON response
            result = response.json()
            
            # Update quota information if available
            if "X-Apikey-Quota-Remaining" in response.headers:
                self.stats.quota_remaining = int(response.headers["X-Apikey-Quota-Remaining"])
            
            return result
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"VirusTotal API request failed: {e}")
            self.stats.api_errors += 1
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse VirusTotal response: {e}")
            self.stats.api_errors += 1
            return None
    
    def check_file_hash(self, file_hash: str) -> Optional[VirusTotalResult]:
        """
        Check file hash against VirusTotal database.
        
        Args:
            file_hash (str): File hash (MD5, SHA-1, or SHA-256)
            
        Returns:
            Optional[VirusTotalResult]: Scan result or None
        """
        # Check cache first
        if self.enable_caching:
            cached_result = self._get_cached_result(file_hash)
            if cached_result:
                self.stats.cache_hits += 1
                return cached_result
            self.stats.cache_misses += 1
        
        # Make API request
        response = self._make_request("GET", "file/report", {"resource": file_hash})
        
        if not response:
            return None
        
        # Check if file was found
        if response.get("response_code") != 1:
            self.logger.debug(f"File hash not found in VirusTotal: {file_hash}")
            return None
        
        # Parse response
        result = self._parse_scan_result(response, file_hash)
        
        # Cache result
        if self.enable_caching and result:
            self._cache_result(file_hash, result)
        
        return result
    
    def check_file(self, file_path: str) -> Optional[VirusTotalResult]:
        """
        Check file against VirusTotal database.
        
        Args:
            file_path (str): Path to file
            
        Returns:
            Optional[VirusTotalResult]: Scan result or None
        """
        try:
            # Calculate file hash
            file_hash = FileUtils.get_file_hash(file_path, "sha256")
            if not file_hash:
                self.logger.error(f"Failed to calculate hash for file: {file_path}")
                return None
            
            return self.check_file_hash(file_hash)
            
        except Exception as e:
            self.logger.error(f"Error checking file {file_path}: {e}")
            return None
    
    def submit_file(self, file_path: str) -> Optional[str]:
        """
        Submit file to VirusTotal for scanning.
        
        Args:
            file_path (str): Path to file
            
        Returns:
            Optional[str]: Scan ID or None
        """
        if not os.path.exists(file_path):
            self.logger.error(f"File not found: {file_path}")
            return None
        
        file_size = os.path.getsize(file_path)
        if file_size > 32 * 1024 * 1024:  # 32MB limit for free API
            self.logger.error(f"File too large for submission: {file_size} bytes")
            return None
        
        try:
            with open(file_path, "rb") as f:
                files = {"file": (os.path.basename(file_path), f)}
                response = self._make_request("POST", "file/scan", data=files)
            
            if response and response.get("response_code") == 1:
                scan_id = response.get("scan_id")
                self.logger.info(f"File submitted successfully: {scan_id}")
                return scan_id
            else:
                self.logger.error(f"File submission failed: {response}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error submitting file {file_path}: {e}")
            return None
    
    def get_scan_result(self, scan_id: str) -> Optional[VirusTotalResult]:
        """
        Get scan result by scan ID.
        
        Args:
            scan_id (str): Scan ID from submission
            
        Returns:
            Optional[VirusTotalResult]: Scan result or None
        """
        response = self._make_request("GET", "file/report", {"resource": scan_id})
        
        if not response:
            return None
        
        if response.get("response_code") == -2:
            self.logger.info(f"Scan still in progress: {scan_id}")
            return None
        elif response.get("response_code") != 1:
            self.logger.warning(f"Scan result not available: {scan_id}")
            return None
        
        return self._parse_scan_result(response, scan_id)
    
    def _parse_scan_result(self, response: Dict[str, Any], identifier: str) -> VirusTotalResult:
        """Parse VirusTotal API response into our format."""
        # Extract basic information
        positives = response.get("positives", 0)
        total = response.get("total", 0)
        detection_ratio = positives / total if total > 0 else 0.0
        
        # Determine threat level
        if detection_ratio >= 0.5:
            threat_level = "high"
            is_malicious = True
        elif detection_ratio >= 0.2:
            threat_level = "medium"
            is_malicious = True
        elif detection_ratio > 0:
            threat_level = "low"
            is_malicious = True
        else:
            threat_level = "clean"
            is_malicious = False
        
        # Calculate confidence score
        confidence_score = min(1.0, total / 50.0) if total > 0 else 0.0
        
        # Extract scan results
        scan_results = response.get("scans", {})
        
        # Extract file information
        file_info = {
            "md5": response.get("md5", ""),
            "sha1": response.get("sha1", ""),
            "sha256": response.get("sha256", ""),
            "size": response.get("size", 0),
            "file_type": response.get("file_type", ""),
            "magic": response.get("magic", "")
        }
        
        return VirusTotalResult(
            file_hash=identifier,
            scan_date=response.get("scan_date", ""),
            positives=positives,
            total=total,
            detection_ratio=detection_ratio,
            permalink=response.get("permalink", ""),
            scan_results=scan_results,
            file_info=file_info,
            is_malicious=is_malicious,
            threat_level=threat_level,
            confidence_score=confidence_score,
            first_seen=response.get("first_seen"),
            last_seen=response.get("last_seen"),
            submission_names=response.get("submission_names", []),
            file_types=response.get("additional_info", {}).get("file_types", [])
        )
    
    def _get_cached_result(self, file_hash: str) -> Optional[VirusTotalResult]:
        """Get cached result for file hash."""
        with self.cache_lock:
            if file_hash in self.cache:
                cached_data = self.cache[file_hash]
                
                # Check if cache entry is still valid
                current_time = time.time()
                age_hours = (current_time - cached_data.get("timestamp", 0)) / 3600
                
                if age_hours < self.cache_duration_hours:
                    # Convert cached data back to VirusTotalResult
                    result_data = cached_data.get("result", {})
                    return VirusTotalResult(**result_data)
                else:
                    # Remove expired entry
                    del self.cache[file_hash]
        
        return None
    
    def _cache_result(self, file_hash: str, result: VirusTotalResult):
        """Cache scan result."""
        with self.cache_lock:
            self.cache[file_hash] = {
                "timestamp": time.time(),
                "result": asdict(result)
            }
            
            # Limit cache size
            if len(self.cache) > 10000:
                # Remove oldest entries
                sorted_entries = sorted(
                    self.cache.items(),
                    key=lambda x: x[1].get("timestamp", 0)
                )
                
                # Keep only the newest 8000 entries
                self.cache = dict(sorted_entries[-8000:])
        
        # Save cache periodically
        if len(self.cache) % 100 == 0:
            self._save_cache()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get client statistics."""
        stats_dict = asdict(self.stats)
        stats_dict["cache_size"] = len(self.cache)
        stats_dict["cache_hit_ratio"] = (
            self.stats.cache_hits / (self.stats.cache_hits + self.stats.cache_misses)
            if (self.stats.cache_hits + self.stats.cache_misses) > 0 else 0.0
        )
        return stats_dict
    
    def clear_cache(self):
        """Clear the cache."""
        with self.cache_lock:
            self.cache.clear()
        self.logger.info("VirusTotal cache cleared")
    
    def save_cache(self):
        """Manually save cache to file."""
        self._save_cache()
        self.logger.info("VirusTotal cache saved")

# Example usage
if __name__ == "__main__":
    # Test configuration
    config = {
        "api_key": "your_virustotal_api_key_here",
        "rate_limit": 4,
        "enable_caching": True,
        "cache_duration_hours": 24
    }
    
    client = VirusTotalClient(config)
    
    # Test hash checking
    test_hash = "d41d8cd98f00b204e9800998ecf8427e"  # Empty file MD5
    result = client.check_file_hash(test_hash)
    
    if result:
        print(f"Hash: {result.file_hash}")
        print(f"Detection ratio: {result.detection_ratio:.2%}")
        print(f"Threat level: {result.threat_level}")
        print(f"Is malicious: {result.is_malicious}")
    else:
        print("Hash not found in VirusTotal database")
    
    # Print statistics
    stats = client.get_statistics()
    print(f"Statistics: {stats}")
