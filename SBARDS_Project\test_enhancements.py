#!/usr/bin/env python3
"""
SBARDS v2.0.0 Enhancement Testing Script
Comprehensive testing for all enhanced features
"""

import os
import sys
import time
import requests
import subprocess
from pathlib import Path
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class EnhancementTester:
    """Test all enhanced features of SBARDS v2.0.0"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.test_results = []
        self.start_time = time.time()
    
    def log_test(self, test_name, status, message="", duration=0):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status} ({duration:.2f}s)")
        if message:
            print(f"   {message}")
    
    def test_file_structure(self):
        """Test that all enhanced files exist"""
        print("\n🔍 Testing Enhanced File Structure...")
        
        required_files = [
            "api/static/css/api-docs-enhanced.css",
            "api/static/css/enhanced-components.css",
            "api/static/js/enhanced-api.js",
            "ENHANCED_FEATURES.md",
            "ENHANCEMENT_SUMMARY.md"
        ]
        
        for file_path in required_files:
            start_time = time.time()
            full_path = project_root / file_path
            
            if full_path.exists():
                self.log_test(f"File exists: {file_path}", "PASS", 
                            f"Size: {full_path.stat().st_size} bytes", 
                            time.time() - start_time)
            else:
                self.log_test(f"File exists: {file_path}", "FAIL", 
                            "File not found", time.time() - start_time)
    
    def test_css_enhancements(self):
        """Test CSS enhancement files"""
        print("\n🎨 Testing CSS Enhancements...")
        
        css_files = [
            "api/static/css/themes.css",
            "api/static/css/dashboard.css",
            "api/static/css/api-docs-enhanced.css",
            "api/static/css/enhanced-components.css"
        ]
        
        for css_file in css_files:
            start_time = time.time()
            css_path = project_root / css_file
            
            if css_path.exists():
                content = css_path.read_text(encoding='utf-8')
                
                # Check for enhanced features
                enhanced_features = [
                    "--theme-accent-primary-alpha",
                    "--theme-shadow-focus",
                    "@keyframes",
                    ".enhanced-",
                    "transition:"
                ]
                
                found_features = [f for f in enhanced_features if f in content]
                
                if len(found_features) >= 3:
                    self.log_test(f"CSS Enhanced: {css_file}", "PASS", 
                                f"Found {len(found_features)} enhanced features", 
                                time.time() - start_time)
                else:
                    self.log_test(f"CSS Enhanced: {css_file}", "WARN", 
                                f"Only {len(found_features)} enhanced features found", 
                                time.time() - start_time)
            else:
                self.log_test(f"CSS Enhanced: {css_file}", "FAIL", 
                            "File not found", time.time() - start_time)
    
    def test_javascript_enhancements(self):
        """Test JavaScript enhancement files"""
        print("\n⚡ Testing JavaScript Enhancements...")
        
        js_files = [
            "api/static/js/navigation.js",
            "api/static/js/dashboard.js",
            "api/static/js/interactive-components.js",
            "api/static/js/enhanced-api.js"
        ]
        
        for js_file in js_files:
            start_time = time.time()
            js_path = project_root / js_file
            
            if js_path.exists():
                content = js_path.read_text(encoding='utf-8')
                
                # Check for enhanced features
                enhanced_features = [
                    "class ",
                    "async ",
                    "await ",
                    "fetch(",
                    "addEventListener",
                    "setTimeout",
                    "console.log"
                ]
                
                found_features = [f for f in enhanced_features if f in content]
                
                if len(found_features) >= 4:
                    self.log_test(f"JS Enhanced: {js_file}", "PASS", 
                                f"Found {len(found_features)} enhanced features", 
                                time.time() - start_time)
                else:
                    self.log_test(f"JS Enhanced: {js_file}", "WARN", 
                                f"Only {len(found_features)} enhanced features found", 
                                time.time() - start_time)
            else:
                self.log_test(f"JS Enhanced: {js_file}", "FAIL", 
                            "File not found", time.time() - start_time)
    
    def test_api_server_start(self):
        """Test if API server can start"""
        print("\n🚀 Testing API Server Startup...")
        
        start_time = time.time()
        try:
            # Check if server is already running
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            if response.status_code == 200:
                self.log_test("API Server", "PASS", 
                            "Server already running", time.time() - start_time)
                return True
        except:
            pass
        
        # Try to start server (this would need to be done separately)
        self.log_test("API Server", "SKIP", 
                    "Manual server start required", time.time() - start_time)
        return False
    
    def test_api_endpoints(self):
        """Test enhanced API endpoints"""
        print("\n🔗 Testing Enhanced API Endpoints...")
        
        endpoints = [
            ("/api/health", "Health Check"),
            ("/api/dashboard/data", "Dashboard Data"),
            ("/api/system/metrics", "System Metrics"),
            ("/api/capture/status", "Capture Status"),
            ("/dashboard", "Dashboard Page"),
            ("/api/docs", "API Documentation")
        ]
        
        for endpoint, description in endpoints:
            start_time = time.time()
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                
                if response.status_code == 200:
                    self.log_test(f"Endpoint: {endpoint}", "PASS", 
                                f"{description} - Status: {response.status_code}", 
                                time.time() - start_time)
                else:
                    self.log_test(f"Endpoint: {endpoint}", "WARN", 
                                f"{description} - Status: {response.status_code}", 
                                time.time() - start_time)
            except requests.exceptions.ConnectionError:
                self.log_test(f"Endpoint: {endpoint}", "SKIP", 
                            f"{description} - Server not running", 
                            time.time() - start_time)
            except Exception as e:
                self.log_test(f"Endpoint: {endpoint}", "FAIL", 
                            f"{description} - Error: {str(e)}", 
                            time.time() - start_time)
    
    def test_dependencies(self):
        """Test enhanced dependencies"""
        print("\n📦 Testing Enhanced Dependencies...")
        
        dependencies = [
            "fastapi",
            "uvicorn", 
            "psutil",
            "pydantic",
            "jinja2"
        ]
        
        for dep in dependencies:
            start_time = time.time()
            try:
                __import__(dep)
                self.log_test(f"Dependency: {dep}", "PASS", 
                            "Available", time.time() - start_time)
            except ImportError:
                self.log_test(f"Dependency: {dep}", "FAIL", 
                            "Not available", time.time() - start_time)
    
    def test_run_script_enhancements(self):
        """Test run.py enhancements"""
        print("\n🏃 Testing Run Script Enhancements...")
        
        start_time = time.time()
        run_py = project_root / "run.py"
        
        if run_py.exists():
            content = run_py.read_text(encoding='utf-8')
            
            enhanced_features = [
                "show_enhanced_banner",
                "check_enhanced_dependencies", 
                "SBARDS v2.0.0",
                "Enhanced Security",
                "Professional"
            ]
            
            found_features = [f for f in enhanced_features if f in content]
            
            if len(found_features) >= 4:
                self.log_test("Run Script Enhanced", "PASS", 
                            f"Found {len(found_features)} enhancements", 
                            time.time() - start_time)
            else:
                self.log_test("Run Script Enhanced", "WARN", 
                            f"Only {len(found_features)} enhancements found", 
                            time.time() - start_time)
        else:
            self.log_test("Run Script Enhanced", "FAIL", 
                        "run.py not found", time.time() - start_time)
    
    def generate_report(self):
        """Generate test report"""
        print("\n" + "="*60)
        print("🎯 SBARDS v2.0.0 Enhancement Test Report")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.test_results if r["status"] == "FAIL"])
        warned_tests = len([r for r in self.test_results if r["status"] == "WARN"])
        skipped_tests = len([r for r in self.test_results if r["status"] == "SKIP"])
        
        print(f"📊 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   ⚠️  Warnings: {warned_tests}")
        print(f"   ⏭️  Skipped: {skipped_tests}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"   🎯 Success Rate: {success_rate:.1f}%")
        
        total_duration = time.time() - self.start_time
        print(f"   ⏱️  Total Duration: {total_duration:.2f}s")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    print(f"   - {result['test']}: {result['message']}")
        
        if warned_tests > 0:
            print(f"\n⚠️  Warnings:")
            for result in self.test_results:
                if result["status"] == "WARN":
                    print(f"   - {result['test']}: {result['message']}")
        
        print("\n" + "="*60)
        
        if success_rate >= 80:
            print("🎉 Enhancement testing completed successfully!")
            print("✨ SBARDS v2.0.0 enhancements are ready for use!")
        elif success_rate >= 60:
            print("⚠️  Enhancement testing completed with warnings.")
            print("🔧 Some features may need attention.")
        else:
            print("❌ Enhancement testing failed.")
            print("🛠️  Significant issues need to be resolved.")
        
        return success_rate >= 80
    
    def run_all_tests(self):
        """Run all enhancement tests"""
        print("🚀 Starting SBARDS v2.0.0 Enhancement Testing...")
        print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Run all test categories
        self.test_dependencies()
        self.test_file_structure()
        self.test_css_enhancements()
        self.test_javascript_enhancements()
        self.test_run_script_enhancements()
        self.test_api_server_start()
        self.test_api_endpoints()
        
        # Generate final report
        return self.generate_report()

def main():
    """Main testing function"""
    tester = EnhancementTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎊 All enhancements are working correctly!")
        print("🚀 You can now start SBARDS v2.0.0 with: python run.py --api")
        return 0
    else:
        print("\n🔧 Some enhancements need attention.")
        print("📝 Please review the test results above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
