"""
Configuration Loader for SBARDS

This module provides configuration loading and validation for the SBARDS project.
"""

import os
import json
import platform
import logging
from typing import Dict, Any, Optional

class ConfigLoader:
    """
    Configuration loader for the SBARDS Project.
    Loads and validates configuration from a JSON file.
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the configuration loader.
        
        Args:
            config_path (str): Path to the configuration file
        """
        self.config_path = os.path.abspath(config_path)
        self.config = self._load_config()
        self._validate_config()
        self._setup_platform_specifics()
        self._runtime_checks()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from the JSON file.
        
        Returns:
            dict: Configuration dictionary
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            logging.error(f"Configuration file not found: {self.config_path}")
            return self._create_default_config()
        except json.JSONDecodeError:
            logging.error(f"Invalid JSON in configuration file: {self.config_path}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """
        Create a default configuration.
        
        Returns:
            dict: Default configuration dictionary
        """
        default_config = {
            "scanner": {
                "target_directory": "samples",
                "recursive": True,
                "max_depth": 5,
                "exclude_dirs": [],
                "exclude_extensions": [],
                "max_file_size_mb": 100
            },
            "rules": {
                "rule_files": ["rules/custom_rules.yar"],
                "enable_categories": ["all"]
            },
            "output": {
                "log_directory": "logs",
                "output_directory": "output",
                "json_output": True,
                "csv_output": False,
                "html_report": False,
                "log_level": "info"
            },
            "performance": {
                "threads": 4,
                "batch_size": 20,
                "timeout_seconds": 30,
                "adaptive_threading": True,
                "memory_limit_mb": 1024
            },
            "api": {
                "host": "127.0.0.1",
                "port": 8000,
                "enable_cors": True,
                "allowed_origins": ["*"],
                "enable_docs": True,
                "enable_websockets": True
            },
            "monitoring": {
                "enabled": True,
                "process_monitoring": True,
                "filesystem_monitoring": True,
                "network_monitoring": True,
                "check_interval_seconds": 1.0,
                "alert_threshold": 0.7
            },
            "integration": {
                "enabled": True,
                "coordination_interval_seconds": 1.0,
                "fast_track_enabled": True,
                "fast_track_priority_threshold": 7
            }
        }
        
        # Save the default configuration
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=4)
        
        return default_config
    
    def _validate_config(self) -> None:
        """
        Validate the configuration.
        """
        # Ensure required sections exist
        required_sections = ["scanner", "rules", "output", "performance", "api", "monitoring", "integration"]
        for section in required_sections:
            if section not in self.config:
                logging.warning(f"Missing configuration section: {section}")
                self.config[section] = self._create_default_config()[section]
        
        # Ensure target directory exists
        target_dir = self.config["scanner"]["target_directory"]
        if not os.path.exists(target_dir):
            logging.warning(f"Target directory does not exist: {target_dir}")
            if target_dir != "samples":
                logging.info("Falling back to 'samples' directory")
                self.config["scanner"]["target_directory"] = "samples"
                os.makedirs("samples", exist_ok=True)
    
    def _setup_platform_specifics(self) -> None:
        """
        Set up platform-specific configuration.
        """
        system = platform.system()
        
        # Add platform-specific configuration
        if "platform_specific" not in self.config:
            self.config["platform_specific"] = {}
            
        if system == "Windows":
            if "windows" not in self.config["platform_specific"]:
                self.config["platform_specific"]["windows"] = {
                    "use_defender": True,
                    "enable_process_monitoring": True,
                    "enable_registry_monitoring": True
                }
        elif system == "Linux":
            if "linux" not in self.config["platform_specific"]:
                self.config["platform_specific"]["linux"] = {
                    "use_clamav": True,
                    "enable_auditd": True,
                    "enable_syslog_monitoring": True
                }
    
    def _runtime_checks(self) -> None:
        """
        Perform runtime checks for critical configuration values.
        """
        # Example check: Ensure log level is valid
        valid_log_levels = ["debug", "info", "warning", "error", "critical"]
        log_level = self.config["output"].get("log_level", "info")
        if log_level not in valid_log_levels:
            raise ValueError(f"Invalid log level: {log_level}. Must be one of {valid_log_levels}")
        
        # Add more runtime checks as needed
        required_keys = ['db_connection', 'scan_threshold', 'log_level']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Missing critical config key: {key}")
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get the configuration.
        
        Returns:
            dict: Configuration dictionary
        """
        return self.config
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get a specific section of the configuration.
        
        Args:
            section (str): Section name
            
        Returns:
            dict: Section configuration dictionary
        """
        return self.config.get(section, {})
    
    def save_config(self) -> None:
        """
        Save the current configuration to the file.
        """
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=4)
