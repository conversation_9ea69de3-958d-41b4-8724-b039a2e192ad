{"version": "2.0.0", "project_name": "SBARDS Enhanced", "description": "Security-Based Automated Ransomware Detection System - Enhanced Version", "scanner": {"target_directory": "D:\\IDA FILE", "recursive": true, "max_depth": 5, "exclude_dirs": ["node_modules", ".git", "__pycache__", "build", "dist"], "exclude_extensions": [".tmp", ".temp", ".log", ".cache"], "max_file_size_mb": 100, "scan_types": {"static_analysis": true, "dynamic_analysis": false, "signature_check": true, "entropy_analysis": true, "hash_generation": true, "yara_scanning": true, "virus_total_check": false}}, "rules": {"rule_directories": ["static_analysis/yara_rules/basic", "static_analysis/yara_rules/malware", "static_analysis/yara_rules/ransomware"], "rule_files": ["static_analysis/yara_rules/basic/basic_rules.yar", "static_analysis/yara_rules/malware/advanced_malware.yar", "static_analysis/yara_rules/ransomware/ransomware_families.yar"], "enable_categories": ["malware", "ransomware", "trojan", "spyware", "basic"], "rule_compilation": {"parallel_compilation": true, "cache_compiled_rules": true, "rule_timeout_seconds": 30}}, "static_analysis": {"enabled": true, "components": {"signature_checker": {"enabled": true, "use_cpp": true, "supported_formats": ["PE", "ELF", "Mach<PERSON><PERSON>", "PDF", "ZIP", "RAR", "JPEG", "PNG", "GIF", "MP3", "MP4"]}, "entropy_checker": {"enabled": true, "use_cpp": true, "block_size": 1024, "thresholds": {"low_entropy": 3.0, "medium_entropy": 6.0, "high_entropy": 7.0, "very_high_entropy": 7.5, "encrypted_threshold": 7.8}}, "hash_generator": {"enabled": true, "use_cpp": true, "algorithms": ["sha256", "sha512", "md5", "sha1"], "parallel_processing": true, "buffer_size": 65536}, "yara_scanner": {"enabled": true, "parallel_processing": true, "max_threads": 4, "enable_caching": true, "timeout_seconds": 30}, "virus_total": {"enabled": false, "api_key": "", "rate_limit": 4, "enable_caching": true, "cache_duration_hours": 24}}}, "dynamic_analysis": {"enabled": false, "sandbox": {"enabled": false, "timeout_seconds": 300, "vm_snapshot": "clean_snapshot", "network_isolation": true}, "api_monitoring": {"enabled": false, "hook_apis": true, "monitor_file_operations": true, "monitor_registry_operations": true, "monitor_network_operations": true}}, "output": {"log_directory": "logs", "output_directory": "output", "data_directory": "data", "formats": {"json_output": true, "csv_output": false, "html_report": true, "xml_output": false}, "log_level": "info", "log_rotation": {"enabled": true, "max_size_mb": 100, "backup_count": 5}}, "performance": {"threads": 4, "batch_size": 20, "timeout_seconds": 30, "adaptive_threading": true, "memory_limit_mb": 1024, "cpu_limit_percent": 80, "optimization": {"use_cpp_components": true, "enable_caching": true, "parallel_processing": true, "stream_processing": true}}, "api": {"host": "127.0.0.1", "port": 8000, "enable_cors": true, "allowed_origins": ["*"], "enable_docs": true, "enable_websockets": true, "rate_limiting": {"enabled": true, "requests_per_minute": 60, "burst_size": 10}, "authentication": {"enabled": false, "api_key_required": false, "jwt_secret": ""}}, "monitoring": {"enabled": true, "process_monitoring": true, "filesystem_monitoring": true, "network_monitoring": true, "check_interval_seconds": 1.0, "alert_threshold": 0.7, "process": {"suspicious_process_patterns": ["<PERSON><PERSON><PERSON><PERSON>", "psexec", "powershell -enc", "cmd /c", "regsvr32", "bitsadmin", "certutil", "rundll32"], "memory_usage_threshold_percent": 80, "cpu_usage_threshold_percent": 90}, "filesystem": {"watch_directories": ["samples", "data"], "detect_mass_operations": true, "mass_operation_threshold": 5, "mass_operation_time_window_seconds": 10, "suspicious_extensions": [".exe", ".dll", ".bat", ".ps1", ".vbs", ".js", ".hta", ".scr", ".com", ".pif", ".cmd"]}, "network": {"suspicious_ports": [4444, 8080, 1337, 31337, 6666, 9999], "suspicious_addresses": ["pastebin.com", "github.com", "raw.githubusercontent.com", "bit.ly", "tinyurl.com", "t.co"], "detect_connection_spikes": true, "connection_spike_threshold": 10}, "alert": {"log_alerts": true, "alert_level": "info", "max_alerts_per_minute": 20, "deduplicate_alerts": true, "deduplication_window_seconds": 300}, "response": {"enabled": true, "auto_quarantine": false, "auto_block_process": false, "auto_block_network": false, "quarantine_directory": "quarantine", "backup_before_quarantine": true}}, "integration": {"enabled": true, "coordination_interval_seconds": 1.0, "fast_track_enabled": true, "fast_track_priority_threshold": 7, "external_apis": {"virus_total": {"enabled": false, "api_key": "", "rate_limit": 4}, "hybrid_analysis": {"enabled": false, "api_key": ""}}}, "database": {"type": "sqlite", "connection_string": "sqlite:///./data/sbards.db", "backup": {"enabled": true, "interval_hours": 24, "keep_backups": 7}}, "security": {"encryption": {"enabled": false, "algorithm": "AES-256", "key_file": "security/encryption.key"}, "access_control": {"enabled": false, "admin_users": [], "read_only_users": []}}, "notifications": {"enabled": false, "email": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "recipients": []}, "webhook": {"enabled": false, "url": "", "secret": ""}}, "experimental": {"machine_learning": {"enabled": false, "model_path": "models/threat_detection.pkl", "confidence_threshold": 0.8}, "behavioral_analysis": {"enabled": false, "learning_mode": false, "baseline_period_days": 7}}, "legacy_compatibility": {"support_old_config": true, "migrate_old_data": true, "preserve_old_reports": true}, "scan_threshold": 5, "log_level": "INFO"}