MIT License

Copyright (c) 2025 SBARDS Project - Security Behavior Analysis and Ransomware Detection System

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

<PERSON><PERSON><PERSON><PERSON><PERSON> TERMS FOR SECURITY SOFTWARE:

1. RESPONSIBLE USE: This software is designed for legitimate security research,
   malware analysis, and system protection purposes only. Users must comply with
   all applicable laws and regulations.

2. NO MALICIOUS USE: This software must not be used to create, distribute, or
   facilitate malware, ransomware, or any other malicious software.

3. EDUCATIONAL PURPOSE: This software may be used for educational and research
   purposes in cybersecurity, provided that such use complies with institutional
   policies and applicable laws.

4. ENTERPRISE USE: Commercial use of this software is permitted under the MIT
   License terms, but users are encouraged to contribute improvements back to
   the community.

5. DISCLAIMER: The authors and contributors of this software are not responsible
   for any misuse, damage, or legal consequences resulting from the use of this
   software.

---

THIRD-PARTY COMPONENTS:

This software may include or depend on third-party components with their own
licenses. Please refer to the requirements.txt file and individual component
documentation for specific license information.

Key dependencies include:
- YARA (Apache License 2.0)
- Python libraries (various licenses)
- C++ standard libraries (various licenses)

---

SECURITY NOTICE:

This software is designed to detect and analyze potentially malicious files.
While every effort has been made to ensure the safety and security of this
software, users should:

1. Run this software in isolated environments when analyzing unknown files
2. Keep the software updated with the latest security patches
3. Follow security best practices when handling malware samples
4. Report security vulnerabilities to the project maintainers

---

CONTACT INFORMATION:

For questions about this license or the software:
- Project Repository: https://github.com/sbards/sbards
- Security Issues: <EMAIL>
- General Contact: <EMAIL>

---

Last Updated: 2025-05-25
