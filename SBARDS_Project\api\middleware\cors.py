"""
CORS middleware for SBARDS API

This module provides enhanced CORS (Cross-Origin Resource Sharing) middleware.
"""

from typing import List, Optional, Union
from fastapi.middleware.cors import CORSMiddleware as FastAPICORSMiddleware

from core.logger import get_global_logger
from core.config import get_config

# Configure logging
logger = get_global_logger().get_layer_logger("api.middleware.cors")


class CORSMiddleware:
    """Enhanced CORS middleware configuration."""
    
    def __init__(self):
        """Initialize CORS middleware configuration."""
        self.config = get_config()
        self.logger = logger
    
    def get_cors_middleware(self) -> FastAPICORSMiddleware:
        """
        Get configured CORS middleware.
        
        Returns:
            FastAPICORSMiddleware: Configured CORS middleware.
        """
        # Get CORS configuration from config
        cors_config = self.config.get("api", {}).get("cors", {})
        
        # Default CORS settings
        default_settings = {
            "allow_origins": ["*"],  # In production, specify exact origins
            "allow_credentials": True,
            "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
            "allow_headers": [
                "Accept",
                "Accept-Language",
                "Content-Language",
                "Content-Type",
                "Authorization",
                "X-Requested-With",
                "X-API-Key",
                "X-CSRFToken",
                "Cache-Control",
                "Pragma"
            ],
            "expose_headers": [
                "X-Process-Time",
                "X-RateLimit-Limit",
                "X-RateLimit-Remaining", 
                "X-RateLimit-Reset",
                "X-Total-Count",
                "X-Page-Count"
            ],
            "max_age": 86400  # 24 hours
        }
        
        # Override with config values
        settings = {**default_settings, **cors_config}
        
        # Validate and adjust settings for security
        settings = self._validate_cors_settings(settings)
        
        self.logger.info(f"CORS middleware configured with origins: {settings['allow_origins']}")
        
        return FastAPICORSMiddleware(
            allow_origins=settings["allow_origins"],
            allow_credentials=settings["allow_credentials"],
            allow_methods=settings["allow_methods"],
            allow_headers=settings["allow_headers"],
            expose_headers=settings["expose_headers"],
            max_age=settings["max_age"]
        )
    
    def _validate_cors_settings(self, settings: dict) -> dict:
        """
        Validate and adjust CORS settings for security.
        
        Args:
            settings (dict): CORS settings.
            
        Returns:
            dict: Validated CORS settings.
        """
        # Check if we're in production mode
        environment = self.config.get("environment", "development")
        
        if environment == "production":
            # In production, be more restrictive
            if "*" in settings["allow_origins"]:
                self.logger.warning(
                    "Using wildcard (*) for CORS origins in production is not recommended. "
                    "Please specify exact origins for better security."
                )
            
            # Ensure credentials are handled securely
            if settings["allow_credentials"] and "*" in settings["allow_origins"]:
                self.logger.error(
                    "Cannot use wildcard (*) for origins when allow_credentials is True. "
                    "Disabling credentials or specify exact origins."
                )
                settings["allow_credentials"] = False
        
        # Validate origins format
        validated_origins = []
        for origin in settings["allow_origins"]:
            if origin == "*":
                validated_origins.append(origin)
            elif self._is_valid_origin(origin):
                validated_origins.append(origin)
            else:
                self.logger.warning(f"Invalid CORS origin format: {origin}")
        
        settings["allow_origins"] = validated_origins
        
        # Validate methods
        valid_methods = {"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH", "TRACE"}
        settings["allow_methods"] = [
            method.upper() for method in settings["allow_methods"]
            if method.upper() in valid_methods
        ]
        
        # Ensure OPTIONS is always allowed for preflight requests
        if "OPTIONS" not in settings["allow_methods"]:
            settings["allow_methods"].append("OPTIONS")
        
        return settings
    
    def _is_valid_origin(self, origin: str) -> bool:
        """
        Validate origin format.
        
        Args:
            origin (str): Origin to validate.
            
        Returns:
            bool: True if origin is valid.
        """
        if not origin:
            return False
        
        # Basic validation - should start with http:// or https://
        if not (origin.startswith("http://") or origin.startswith("https://")):
            return False
        
        # Additional validation could be added here
        # For example, checking for valid domain format
        
        return True
    
    @staticmethod
    def get_development_cors_settings() -> dict:
        """
        Get CORS settings optimized for development.
        
        Returns:
            dict: Development CORS settings.
        """
        return {
            "allow_origins": [
                "http://localhost:3000",  # React dev server
                "http://localhost:8080",  # Vue dev server
                "http://localhost:4200",  # Angular dev server
                "http://localhost:5173",  # Vite dev server
                "http://127.0.0.1:3000",
                "http://127.0.0.1:8080",
                "http://127.0.0.1:4200",
                "http://127.0.0.1:5173"
            ],
            "allow_credentials": True,
            "allow_methods": ["*"],
            "allow_headers": ["*"],
            "max_age": 3600  # 1 hour for development
        }
    
    @staticmethod
    def get_production_cors_settings(allowed_domains: List[str]) -> dict:
        """
        Get CORS settings optimized for production.
        
        Args:
            allowed_domains (List[str]): List of allowed domains.
            
        Returns:
            dict: Production CORS settings.
        """
        # Generate origins from domains
        origins = []
        for domain in allowed_domains:
            origins.extend([
                f"https://{domain}",
                f"https://www.{domain}"
            ])
        
        return {
            "allow_origins": origins,
            "allow_credentials": True,
            "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
            "allow_headers": [
                "Accept",
                "Accept-Language",
                "Content-Language",
                "Content-Type",
                "Authorization",
                "X-Requested-With",
                "X-API-Key"
            ],
            "expose_headers": [
                "X-Process-Time",
                "X-RateLimit-Limit",
                "X-RateLimit-Remaining",
                "X-RateLimit-Reset"
            ],
            "max_age": 86400  # 24 hours
        }
