2025-05-23 01:15:22,511 - SBARDS.Scanner - INFO - Starting scan of .
2025-05-23 01:15:22,528 - SBARDS.Scanner - WARNING - Potential threat found in G:\SBARDS\SBARDSProject\backend\app\services\virustotal.py
2025-05-23 01:15:22,529 - SBARDS.Scanner - WARNING - Potential threat found in G:\SBARDS\SBARDSProject\backend\app\services\__pycache__\virustotal.cpython-313.pyc
2025-05-23 01:15:22,532 - SBARDS.Scanner - WARNING - Potential threat found in G:\SBARDS\SBARDSProject\rules\malware_rules.yar
2025-05-23 01:15:22,533 - SBARDS.Scanner - WARNING - Potential threat found in G:\SBARDS\SBARDSProject\rules\ransomware_advanced_rules.yar
2025-05-23 01:15:22,536 - SBARDS.Scanner - WARNING - Potential threat found in G:\SBARDS\SBARDSProject\tests\test_ransomware_behavior.py
2025-05-23 01:15:22,538 - SBARD<PERSON>.Scanner - INFO - Report generated: G:\SBARDS\SBARDSProject\output\reports\scan_report_20250523_011522.html
2025-05-25 01:12:16,107 - SBARDS.Scanner - INFO - Starting scan of samples
2025-05-25 01:12:16,108 - SBARDS.Scanner - DEBUG - Scanning file: G:\SBARDS\SBARDSProject\samples\test_sample.txt
2025-05-25 01:12:16,109 - SBARDS.Scanner - DEBUG - Scanning file: G:\SBARDS\SBARDSProject\samples\test_ransomware\bitcoin_payment.html
2025-05-25 01:12:16,109 - SBARDS.Scanner - DEBUG - Scanning file: G:\SBARDS\SBARDSProject\samples\test_ransomware\encrypted_file.docx.locked
2025-05-25 01:12:16,110 - SBARDS.Scanner - DEBUG - Scanning file: G:\SBARDS\SBARDSProject\samples\test_ransomware\permission_test.txt
2025-05-25 01:12:16,111 - SBARDS.Scanner - DEBUG - Scanning file: G:\SBARDS\SBARDSProject\samples\test_ransomware\ransom_note.txt
2025-05-25 01:12:16,111 - SBARDS.Scanner - DEBUG - Scanning file: G:\SBARDS\SBARDSProject\samples\test_ransomware\README.md
2025-05-25 01:12:16,112 - SBARDS.Scanner - DEBUG - Scanning file: G:\SBARDS\SBARDSProject\samples\test_ransomware\shadow_copy_test.txt
2025-05-25 01:12:16,113 - SBARDS.Scanner - INFO - Report generated: G:\SBARDS\SBARDSProject\output\reports\scan_report_20250525_011216.html
