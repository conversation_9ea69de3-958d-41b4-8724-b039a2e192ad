"""
Prescanning Router for SBARDS API

This module provides the prescanning router for the SBARDS API.
"""

import os
import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel

# Create router
router = APIRouter()

# Global orchestrator instance
_orchestrator = None

def set_orchestrator(orchestrator):
    """Set the global orchestrator instance."""
    global _orchestrator
    _orchestrator = orchestrator

def get_orchestrator():
    """Get the global orchestrator instance."""
    if _orchestrator is None:
        raise HTTPException(status_code=503, detail="Prescanning orchestrator not initialized")
    return _orchestrator

# Models
class ScanRequest(BaseModel):
    """Scan request model."""
    target_directory: str
    recursive: bool = True
    max_depth: int = 5
    exclude_dirs: List[str] = []
    exclude_extensions: List[str] = []
    max_file_size_mb: int = 100

class ScanResult(BaseModel):
    """Scan result model."""
    scan_id: str
    target_directory: str
    total_files: int
    scanned_files: int
    matched_files: int
    elapsed_time: float
    status: str

# Endpoints
@router.post("/scan", response_model=ScanResult)
async def start_scan(
    scan_request: ScanRequest,
    background_tasks: BackgroundTasks,
    orchestrator = Depends(get_orchestrator)
):
    """Start a prescanning scan."""
    try:
        # Validate target directory
        if not os.path.exists(scan_request.target_directory):
            raise HTTPException(status_code=400, detail=f"Target directory not found: {scan_request.target_directory}")
        
        # Start scan in background
        scan_id = orchestrator.prepare_scan(
            target_directory=scan_request.target_directory,
            recursive=scan_request.recursive,
            max_depth=scan_request.max_depth,
            exclude_dirs=scan_request.exclude_dirs,
            exclude_extensions=scan_request.exclude_extensions,
            max_file_size_mb=scan_request.max_file_size_mb
        )
        
        # Run scan in background
        background_tasks.add_task(
            orchestrator.run_scan_async,
            scan_id=scan_id
        )
        
        # Return initial scan result
        return ScanResult(
            scan_id=scan_id,
            target_directory=scan_request.target_directory,
            total_files=0,
            scanned_files=0,
            matched_files=0,
            elapsed_time=0.0,
            status="running"
        )
    except Exception as e:
        logging.error(f"Error starting scan: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting scan: {str(e)}")

@router.get("/scan/{scan_id}", response_model=ScanResult)
async def get_scan_result(
    scan_id: str,
    orchestrator = Depends(get_orchestrator)
):
    """Get scan result."""
    try:
        # Get scan result
        result = orchestrator.get_scan_result(scan_id)
        if result is None:
            raise HTTPException(status_code=404, detail=f"Scan result not found: {scan_id}")
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting scan result: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scan result: {str(e)}")

@router.get("/scans", response_model=List[ScanResult])
async def get_scans(
    orchestrator = Depends(get_orchestrator)
):
    """Get all scans."""
    try:
        # Get all scans
        scans = orchestrator.get_all_scans()
        return scans
    except Exception as e:
        logging.error(f"Error getting scans: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scans: {str(e)}")

@router.delete("/scan/{scan_id}")
async def delete_scan(
    scan_id: str,
    orchestrator = Depends(get_orchestrator)
):
    """Delete scan result."""
    try:
        # Delete scan
        success = orchestrator.delete_scan(scan_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Scan not found: {scan_id}")
        
        return {"status": "deleted", "scan_id": scan_id}
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error deleting scan: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting scan: {str(e)}")

@router.get("/status")
async def get_prescanning_status():
    """Get prescanning status."""
    try:
        return {
            "status": "active",
            "service": "prescanning",
            "version": "2.0.0",
            "capabilities": [
                "file_scanning",
                "yara_rules",
                "hash_checking",
                "background_processing"
            ]
        }
    except Exception as e:
        logging.error(f"Error getting prescanning status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting prescanning status: {str(e)}")
