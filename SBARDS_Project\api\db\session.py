"""
Database session management for SBARDS API

This module provides database session management and initialization.
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from core.config import get_config
from core.logger import get_global_logger
from .base import Base

# Configure logging
logger = get_global_logger().get_layer_logger("api.db")

# Load configuration
config = get_config()

# Database setup
DATABASE_URL = config.get("database_url", "sqlite:///./data/sbards.db")

# Create database engine
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if DATABASE_URL.startswith("sqlite") else {},
    pool_pre_ping=True,
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    """
    Get database session.
    
    Yields:
        Session: Database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db():
    """
    Initialize database.
    
    Creates all tables if they don't exist.
    """
    try:
        # Ensure data directory exists
        os.makedirs("data", exist_ok=True)
        
        # Import models to ensure they are registered with the Base
        from .models import ScanReport, FileResult  # noqa
        
        # Create tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")
        
        return True
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise


def get_db_info():
    """
    Get database information.
    
    Returns:
        dict: Database information.
    """
    return {
        "database_url": DATABASE_URL,
        "engine": str(engine),
        "tables": list(Base.metadata.tables.keys())
    }
