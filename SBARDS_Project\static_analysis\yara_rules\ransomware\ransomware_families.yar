/*
    Ransomware Family Detection Rules for SBARDS

    This file contains specialized YARA rules for detecting known ransomware families
    and their variants with high accuracy and minimal false positives.

    Families covered:
    - WannaCry and variants
    - Locky and variants
    - CryptoLocker family
    - Ryuk ransomware
    - Maze ransomware
    - REvil/Sodinokibi
    - DarkSide ransomware
    - Conti ransomware
*/

import "pe"
import "math"
import "hash"

rule WannaCry_Ransomware
{
    meta:
        description = "Detects WannaCry ransomware and its variants"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "critical"
        tags = "wannacry,worm,smb_exploit"
        reference = "https://www.microsoft.com/security/blog/2017/05/12/wannacrypt-ransomware-worm-targets-out-of-date-systems/"

    strings:
        // Unique WannaCry strings
        $wc1 = "WNcry@2ol7" ascii
        $wc2 = "WANACRY" ascii
        $wc3 = "wannacry" ascii nocase
        $wc4 = "Wana Decrypt0r" ascii
        $wc5 = "@WanaDecryptor@.exe" ascii

        // Ransom note content
        $note1 = "Ooops, your files have been encrypted!" ascii
        $note2 = "What happened to my computer?" ascii
        $note3 = "Your important files are encrypted" ascii
        $note4 = "bitcoin" ascii nocase

        // File extensions
        $ext1 = ".WNCRY" ascii
        $ext2 = ".WCRY" ascii
        $ext3 = ".wncry" ascii

        // SMB exploit indicators
        $smb1 = "\\\\%s\\IPC$" ascii
        $smb2 = "SMB" ascii
        $smb3 = "LANMAN" ascii

        // Encryption indicators
        $crypt1 = "CryptAcquireContext" ascii
        $crypt2 = "CryptGenKey" ascii
        $crypt3 = "CryptEncrypt" ascii

    condition:
        pe.is_pe and
        (
            (2 of ($wc*)) or
            (1 of ($wc*) and 1 of ($note*)) or
            (1 of ($ext*) and 1 of ($note*)) or
            (2 of ($note*) and 1 of ($smb*))
        ) and
        filesize < 10MB
}

rule Locky_Ransomware
{
    meta:
        description = "Detects Locky ransomware family"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "critical"
        tags = "locky,email_attachment,javascript"

    strings:
        // Locky specific strings
        $locky1 = "locky" ascii nocase
        $locky2 = ".locky" ascii
        $locky3 = "_Locky_recover_instructions.txt" ascii
        $locky4 = "LOCKY" ascii

        // Ransom note content
        $note1 = "All of your files are encrypted" ascii
        $note2 = "RSA-2048 and AES-128" ascii
        $note3 = "decrypt your files" ascii
        $note4 = "Locky Decryptor" ascii

        // File extensions used by variants
        $ext1 = ".zepto" ascii
        $ext2 = ".odin" ascii
        $ext3 = ".shit" ascii
        $ext4 = ".thor" ascii
        $ext5 = ".aesir" ascii
        $ext6 = ".zzzzz" ascii

        // JavaScript dropper indicators
        $js1 = "WScript.Shell" ascii
        $js2 = "ActiveXObject" ascii
        $js3 = "XMLHttpRequest" ascii

    condition:
        pe.is_pe and
        (
            (2 of ($locky*)) or
            (1 of ($locky*) and 1 of ($note*)) or
            (1 of ($ext*) and 1 of ($note*)) or
            (2 of ($note*) and 1 of ($js*))
        ) and
        filesize < 5MB
}

rule CryptoLocker_Family
{
    meta:
        description = "Detects CryptoLocker ransomware family"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "critical"
        tags = "cryptolocker,rsa_encryption,tor"

    strings:
        // CryptoLocker strings
        $cl1 = "CryptoLocker" ascii nocase
        $cl2 = "cryptolocker" ascii
        $cl3 = "DECRYPT_INSTRUCTION" ascii
        $cl4 = "Your files are encrypted" ascii

        // Encryption method indicators
        $enc1 = "RSA-2048" ascii
        $enc2 = "AES-256" ascii
        $enc3 = "public key" ascii
        $enc4 = "private key" ascii

        // Payment instructions
        $pay1 = "MoneyPak" ascii
        $pay2 = "Bitcoin" ascii nocase
        $pay3 = "payment" ascii
        $pay4 = "decrypt" ascii

        // Tor/onion indicators
        $tor1 = ".onion" ascii
        $tor2 = "tor browser" ascii nocase
        $tor3 = "hidden service" ascii

        // Registry modifications
        $reg1 = "HKEY_CURRENT_USER\\Software" ascii
        $reg2 = "HKEY_LOCAL_MACHINE\\Software" ascii

    condition:
        pe.is_pe and
        (
            (2 of ($cl*)) or
            (1 of ($cl*) and 1 of ($enc*)) or
            (2 of ($enc*) and 1 of ($pay*)) or
            (1 of ($pay*) and 1 of ($tor*))
        ) and
        filesize < 8MB
}

rule Ryuk_Ransomware
{
    meta:
        description = "Detects Ryuk ransomware"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "critical"
        tags = "ryuk,targeted_attack,enterprise"

    strings:
        // Ryuk specific strings
        $ryuk1 = "RYUK" ascii
        $ryuk2 = "ryuk" ascii nocase
        $ryuk3 = "RyukReadMe.txt" ascii
        $ryuk4 = "HERMES" ascii

        // Ransom note content
        $note1 = "Your network has been penetrated" ascii
        $note2 = "All files on each host in the network have been encrypted" ascii
        $note3 = "We exclusively have decryption software" ascii
        $note4 = "email us" ascii

        // Lateral movement indicators
        $lat1 = "psexec" ascii nocase
        $lat2 = "wmic" ascii
        $lat3 = "net use" ascii
        $lat4 = "admin$" ascii

        // Service manipulation
        $svc1 = "sc stop" ascii
        $svc2 = "sc delete" ascii
        $svc3 = "net stop" ascii

    condition:
        pe.is_pe and
        (
            (2 of ($ryuk*)) or
            (1 of ($ryuk*) and 1 of ($note*)) or
            (2 of ($note*) and 1 of ($lat*)) or
            (1 of ($note*) and 2 of ($svc*))
        ) and
        filesize < 15MB
}

rule Maze_Ransomware
{
    meta:
        description = "Detects Maze ransomware"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "critical"
        tags = "maze,data_theft,double_extortion"

    strings:
        // Maze specific strings
        $maze1 = "MAZE" ascii
        $maze2 = "maze" ascii nocase
        $maze3 = "DECRYPT-FILES.txt" ascii
        $maze4 = "ChaCha" ascii

        // Data theft indicators
        $theft1 = "Your data is downloaded" ascii
        $theft2 = "published on our news website" ascii
        $theft3 = "leak site" ascii nocase
        $theft4 = "stolen data" ascii nocase

        // Encryption indicators
        $enc1 = "ChaCha20" ascii
        $enc2 = "RSA-2048" ascii
        $enc3 = "encrypted" ascii

        // Network indicators
        $net1 = "mazeleaks" ascii nocase
        $net2 = "maze news" ascii nocase

    condition:
        pe.is_pe and
        (
            (2 of ($maze*)) or
            (1 of ($maze*) and 1 of ($theft*)) or
            (2 of ($theft*) and 1 of ($enc*)) or
            (1 of ($net*) and 1 of ($enc*))
        ) and
        filesize < 12MB
}

rule REvil_Sodinokibi
{
    meta:
        description = "Detects REvil/Sodinokibi ransomware"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "critical"
        tags = "revil,sodinokibi,ransomware_as_a_service"

    strings:
        // REvil/Sodinokibi strings
        $revil1 = "REvil" ascii
        $revil2 = "Sodinokibi" ascii
        $revil3 = "sodin" ascii nocase
        $revil4 = "{random}-readme.txt" ascii

        // Ransom note content
        $note1 = "What's Happen to My Computer?" ascii
        $note2 = "Your files are encrypted" ascii
        $note3 = "Salsa20" ascii
        $note4 = "Curve25519" ascii

        // Configuration indicators
        $cfg1 = "\"pk\":" ascii
        $cfg2 = "\"pid\":" ascii
        $cfg3 = "\"sub\":" ascii
        $cfg4 = "\"dbg\":" ascii

        // Evasion techniques
        $evas1 = "IsDebuggerPresent" ascii
        $evas2 = "GetTickCount" ascii
        $evas3 = "Sleep" ascii

    condition:
        pe.is_pe and
        (
            (2 of ($revil*)) or
            (1 of ($revil*) and 1 of ($note*)) or
            (2 of ($note*) and 1 of ($cfg*)) or
            (2 of ($cfg*) and 1 of ($evas*))
        ) and
        filesize < 10MB
}

rule DarkSide_Ransomware
{
    meta:
        description = "Detects DarkSide ransomware"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "critical"
        tags = "darkside,pipeline_attack,double_extortion"

    strings:
        // DarkSide strings
        $ds1 = "DarkSide" ascii
        $ds2 = "darkside" ascii nocase
        $ds3 = "README.TXT" ascii
        $ds4 = "RESTORING YOUR FILES" ascii

        // Ransom note content
        $note1 = "Your computers and servers are encrypted" ascii
        $note2 = "We also downloaded a lot of your private data" ascii
        $note3 = "If you refuse to pay" ascii
        $note4 = "leak website" ascii

        // Exclusion lists (countries/languages)
        $excl1 = "ru-RU" ascii
        $excl2 = "uk-UA" ascii
        $excl3 = "be-BY" ascii
        $excl4 = "Russian" ascii

        // Encryption indicators
        $enc1 = "Salsa20" ascii
        $enc2 = "RSA-1024" ascii

    condition:
        pe.is_pe and
        (
            (2 of ($ds*)) or
            (1 of ($ds*) and 1 of ($note*)) or
            (2 of ($note*) and 1 of ($excl*)) or
            (1 of ($note*) and 1 of ($enc*))
        ) and
        filesize < 8MB
}

rule Conti_Ransomware
{
    meta:
        description = "Detects Conti ransomware"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "critical"
        tags = "conti,enterprise_targeting,fast_encryption"

    strings:
        // Conti strings
        $conti1 = "CONTI" ascii
        $conti2 = "conti" ascii nocase
        $conti3 = "CONTI_README.txt" ascii
        $conti4 = "ContiRecover" ascii

        // Ransom note content
        $note1 = "All of your files are currently encrypted" ascii
        $note2 = "Do not try to decrypt your data using third party software" ascii
        $note3 = "contact us via" ascii
        $note4 = "Your personal ID" ascii

        // Fast encryption indicators
        $fast1 = "CreateIoCompletionPort" ascii
        $fast2 = "GetQueuedCompletionStatus" ascii
        $fast3 = "PostQueuedCompletionStatus" ascii

        // Network scanning
        $scan1 = "NetShareEnum" ascii
        $scan2 = "NetServerEnum" ascii
        $scan3 = "WNetEnumResource" ascii

    condition:
        pe.is_pe and
        (
            (2 of ($conti*)) or
            (1 of ($conti*) and 1 of ($note*)) or
            (2 of ($note*) and 1 of ($fast*)) or
            (1 of ($note*) and 2 of ($scan*))
        ) and
        filesize < 6MB
}

rule Generic_Ransomware_Behavior
{
    meta:
        description = "Detects generic ransomware behavior patterns"
        author = "SBARDS Team"
        date = "2024-05-24"
        category = "ransomware"
        severity = "high"
        tags = "generic_ransomware,encryption_behavior"

    strings:
        // Common ransom note keywords
        $ransom1 = "files are encrypted" ascii nocase
        $ransom2 = "decrypt" ascii nocase
        $ransom3 = "bitcoin" ascii nocase
        $ransom4 = "payment" ascii nocase
        $ransom5 = "recover your files" ascii nocase
        $ransom6 = "private key" ascii nocase

        // Encryption APIs
        $crypt1 = "CryptAcquireContext" ascii
        $crypt2 = "CryptGenKey" ascii
        $crypt3 = "CryptEncrypt" ascii
        $crypt4 = "CryptImportKey" ascii
        $crypt5 = "CryptCreateHash" ascii

        // File operations
        $file1 = "FindFirstFile" ascii
        $file2 = "FindNextFile" ascii
        $file3 = "CreateFile" ascii
        $file4 = "WriteFile" ascii
        $file5 = "DeleteFile" ascii
        $file6 = "MoveFile" ascii

        // Common encrypted file extensions
        $ext1 = ".encrypted" ascii
        $ext2 = ".locked" ascii
        $ext3 = ".crypto" ascii
        $ext4 = ".crypt" ascii

    condition:
        pe.is_pe and
        (
            (3 of ($ransom*) and 2 of ($crypt*)) or
            (2 of ($ransom*) and 3 of ($crypt*) and 2 of ($file*)) or
            (2 of ($ransom*) and 1 of ($ext*) and 2 of ($file*))
        ) and
        filesize > 5KB and
        math.entropy(0, filesize) > 6.0
}

// =========== Migrated Rules from Original SBARDSProject ===========

rule Ransomware_Permission_Manipulation_Migrated
{
    meta:
        description = "Detects ransomware that manipulates file permissions before encryption"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "critical"
        version = "2.0"
        migrated_from = "SBARDSProject/rules/ransomware_advanced_rules.yar"

    strings:
        // Windows permission APIs
        $win_perm1 = "SetFileSecurity" ascii wide
        $win_perm2 = "SetNamedSecurityInfo" ascii wide

        // Linux/Unix permission APIs
        $unix_perm1 = "chmod" ascii wide
        $unix_perm2 = "chown" ascii wide

        // Encryption indicators
        $encrypt1 = "CryptEncrypt" ascii wide
        $encrypt2 = "AES_encrypt" ascii wide

    condition:
        (1 of ($win_perm*) or 1 of ($unix_perm*)) and
        (1 of ($encrypt*))
}

rule Ransomware_Shadow_Copy_Deletion_Migrated
{
    meta:
        description = "Detects ransomware attempting to delete shadow copies"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "critical"
        version = "2.0"
        migrated_from = "SBARDSProject/rules/ransomware_advanced_rules.yar"

    strings:
        // Shadow copy deletion commands
        $shadow1 = "vssadmin delete shadows" nocase ascii wide
        $shadow2 = "wmic shadowcopy delete" nocase ascii wide

        // Command line tools
        $cmd1 = "cmd.exe" nocase ascii wide
        $cmd2 = "powershell" nocase ascii wide

    condition:
        (1 of ($shadow*)) and
        (1 of ($cmd*))
}

rule Ransomware_Indicators_Enhanced_Migrated
{
    meta:
        description = "Detects common ransomware indicators - enhanced version"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "high"
        version = "2.0"
        migrated_from = "SBARDSProject/rules/ransomware_advanced_rules.yar"

    strings:
        // Ransomware strings
        $ransom1 = "encrypted" nocase ascii wide
        $ransom2 = "bitcoin" nocase ascii wide
        $ransom3 = "payment" nocase ascii wide
        $ransom4 = "decrypt" nocase ascii wide
        $ransom5 = "ransom" nocase ascii wide

        // Additional indicators
        $ransom6 = "restore" nocase ascii wide
        $ransom7 = "recover" nocase ascii wide
        $ransom8 = "unlock" nocase ascii wide

    condition:
        2 of them
}
