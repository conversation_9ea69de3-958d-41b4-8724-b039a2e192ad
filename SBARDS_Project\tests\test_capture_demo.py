#!/usr/bin/env python3
"""
SBARDS Capture Layer Demo Test
تجربة عملية لطبقة الالتقاط في نظام SBARDS

هذا الملف يقوم بتشغيل عرض توضيحي شامل لطبقة الالتقاط
ويختبر جميع الوظائف المطلوبة وفقاً للتوثيق.
"""

import os
import sys
import time
import tempfile
import shutil
from pathlib import Path

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from capture.python.file_interceptor import FileInterceptor
from capture.python.redis_queue import RedisQueueManager
from core.logger import get_global_logger

def create_test_files(test_dir):
    """إنشاء ملفات اختبار متنوعة."""
    test_files = []
    
    # ملفات عادية
    normal_files = [
        ("document.txt", "This is a normal text document."),
        ("image.jpg", "Fake JPEG content"),
        ("data.csv", "name,age,city\nJohn,25,NYC"),
        ("config.json", '{"setting": "value"}')
    ]
    
    # ملفات مشبوهة
    suspicious_files = [
        ("malware.exe", "Fake executable content"),
        ("virus.bat", "@echo off\necho Suspicious batch file"),
        ("trojan.dll", "Fake DLL content"),
        ("ransomware.scr", "Fake screensaver")
    ]
    
    # إنشاء الملفات العادية
    print("📁 إنشاء ملفات اختبار عادية...")
    for filename, content in normal_files:
        file_path = os.path.join(test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        test_files.append((file_path, False))  # False = غير مشبوه
        print(f"   ✅ تم إنشاء: {filename}")
    
    # إنشاء الملفات المشبوهة
    print("\n🚨 إنشاء ملفات اختبار مشبوهة...")
    for filename, content in suspicious_files:
        file_path = os.path.join(test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        test_files.append((file_path, True))  # True = مشبوه
        print(f"   ⚠️ تم إنشاء: {filename}")
    
    return test_files

def test_file_interceptor():
    """اختبار مُعترِض الملفات."""
    print("\n" + "="*60)
    print("🔍 اختبار مُعترِض الملفات (File Interceptor)")
    print("="*60)
    
    # إنشاء مجلد اختبار مؤقت
    test_dir = tempfile.mkdtemp(prefix="sbards_test_")
    print(f"📂 مجلد الاختبار: {test_dir}")
    
    try:
        # تكوين مُعترِض الملفات
        config = {
            "target_directories": [test_dir],
            "recursive": True,
            "max_depth": 3,
            "exclude_dirs": [".git", "__pycache__"],
            "exclude_extensions": [".tmp", ".log"],
            "max_file_size_mb": 100,
            "threads": 2,
            "enable_real_time": True
        }
        
        # إنشاء مُعترِض الملفات
        interceptor = FileInterceptor(config)
        print("✅ تم إنشاء مُعترِض الملفات بنجاح")
        
        # إضافة callback للمعالجة
        processed_files = []
        def file_callback(event):
            processed_files.append({
                'path': event.file_path,
                'suspicious': event.is_suspicious,
                'priority': event.priority,
                'size': event.file_size
            })
            status = "🚨 مشبوه" if event.is_suspicious else "✅ آمن"
            print(f"   📄 معالجة: {os.path.basename(event.file_path)} - {status} (أولوية: {event.priority})")
        
        interceptor.add_callback(file_callback)
        
        # بدء تشغيل المُعترِض
        print("\n🚀 بدء تشغيل مُعترِض الملفات...")
        if interceptor.start():
            print("✅ تم بدء التشغيل بنجاح")
        else:
            print("❌ فشل في بدء التشغيل")
            return False
        
        # إنشاء ملفات الاختبار
        test_files = create_test_files(test_dir)
        
        # اعتراض الملفات
        print(f"\n🎯 اعتراض {len(test_files)} ملف...")
        intercepted_events = []
        
        for file_path, is_suspicious in test_files:
            event = interceptor.intercept_file(file_path, "created")
            if event:
                intercepted_events.append(event)
        
        # انتظار المعالجة
        print("⏳ انتظار معالجة الملفات...")
        time.sleep(2)
        
        # إيقاف المُعترِض
        print("\n🛑 إيقاف مُعترِض الملفات...")
        interceptor.stop()
        
        # تحليل النتائج
        print("\n📊 تحليل النتائج:")
        print(f"   📁 ملفات تم إنشاؤها: {len(test_files)}")
        print(f"   🎯 ملفات تم اعتراضها: {len(intercepted_events)}")
        print(f"   ⚙️ ملفات تم معالجتها: {len(processed_files)}")
        
        # إحصائيات التصنيف
        suspicious_count = sum(1 for event in intercepted_events if event.is_suspicious)
        safe_count = len(intercepted_events) - suspicious_count
        
        print(f"   🚨 ملفات مشبوهة: {suspicious_count}")
        print(f"   ✅ ملفات آمنة: {safe_count}")
        
        # إحصائيات الأولوية
        high_priority = sum(1 for event in intercepted_events if event.priority >= 3)
        normal_priority = len(intercepted_events) - high_priority
        
        print(f"   🔴 أولوية عالية: {high_priority}")
        print(f"   🟢 أولوية عادية: {normal_priority}")
        
        # إحصائيات الأداء
        stats = interceptor.get_statistics()
        print(f"\n⚡ إحصائيات الأداء:")
        print(f"   ⏱️ وقت التشغيل: {stats.get('runtime', 0):.2f} ثانية")
        print(f"   📈 معدل المعالجة: {stats.get('files_processed', 0) / max(stats.get('runtime', 1), 1):.2f} ملف/ثانية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مُعترِض الملفات: {e}")
        return False
    
    finally:
        # تنظيف مجلد الاختبار
        try:
            shutil.rmtree(test_dir, ignore_errors=True)
            print(f"🧹 تم تنظيف مجلد الاختبار: {test_dir}")
        except:
            pass

def test_redis_queue():
    """اختبار Redis Queue (اختياري)."""
    print("\n" + "="*60)
    print("🔄 اختبار Redis Queue")
    print("="*60)
    
    try:
        config = {
            "redis_host": "localhost",
            "redis_port": 6379,
            "redis_db": 15,
            "queue_prefix": "sbards_demo",
            "max_retries": 3
        }
        
        queue_manager = RedisQueueManager(config)
        
        if queue_manager.is_available():
            print("✅ Redis متاح ويعمل")
            
            # اختبار إرسال رسالة
            test_data = {"test": "demo", "timestamp": time.time()}
            if queue_manager.send_message("demo_message", test_data, "normal"):
                print("✅ تم إرسال رسالة اختبار بنجاح")
            
            # عرض أحجام الطوابير
            queue_sizes = queue_manager.get_queue_sizes()
            print(f"📊 أحجام الطوابير: {queue_sizes}")
            
            # تنظيف
            queue_manager.clear_queues()
            print("🧹 تم تنظيف الطوابير")
            
            return True
        else:
            print("⚠️ Redis غير متاح - سيتم تخطي هذا الاختبار")
            return True
            
    except Exception as e:
        print(f"⚠️ Redis غير متاح: {e}")
        return True  # لا نعتبر هذا فشل لأن Redis اختياري

def test_cpp_integration():
    """اختبار تكامل C++."""
    print("\n" + "="*60)
    print("🔧 اختبار تكامل C++")
    print("="*60)
    
    # التحقق من وجود ملفات C++
    cpp_files = [
        "capture/cpp/file_monitor.cpp",
        "capture/cpp/permission_manager.cpp"
    ]
    
    all_exist = True
    for cpp_file in cpp_files:
        file_path = Path(cpp_file)
        if file_path.exists():
            print(f"✅ موجود: {cpp_file}")
        else:
            print(f"❌ مفقود: {cpp_file}")
            all_exist = False
    
    if all_exist:
        print("✅ جميع ملفات C++ موجودة")
        print("ℹ️ ملاحظة: التكامل الفعلي مع C++ يتطلب تجميع المكتبات")
        return True
    else:
        print("⚠️ بعض ملفات C++ مفقودة")
        return False

def main():
    """الدالة الرئيسية للعرض التوضيحي."""
    print("🚀 SBARDS Capture Layer Demo")
    print("=" * 60)
    print("عرض توضيحي شامل لطبقة الالتقاط في نظام SBARDS")
    print("=" * 60)
    
    results = []
    
    # اختبار مُعترِض الملفات
    print("\n1️⃣ اختبار مُعترِض الملفات...")
    results.append(("File Interceptor", test_file_interceptor()))
    
    # اختبار Redis Queue
    print("\n2️⃣ اختبار Redis Queue...")
    results.append(("Redis Queue", test_redis_queue()))
    
    # اختبار تكامل C++
    print("\n3️⃣ اختبار تكامل C++...")
    results.append(("C++ Integration", test_cpp_integration()))
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("📋 ملخص نتائج العرض التوضيحي")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / total) * 100
    print(f"\n📊 معدل النجاح: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate >= 75:
        print("🎉 العرض التوضيحي نجح بامتياز!")
        print("✅ طبقة الالتقاط تعمل بكفاءة عالية")
    elif success_rate >= 50:
        print("⚠️ العرض التوضيحي نجح جزئياً")
        print("🔧 قد تحتاج بعض المكونات إلى تحسين")
    else:
        print("❌ العرض التوضيحي فشل")
        print("🛠️ يجب إصلاح المشاكل قبل الاستخدام")
    
    print("\n" + "="*60)
    print("🏁 انتهى العرض التوضيحي")
    print("="*60)

if __name__ == "__main__":
    main()
