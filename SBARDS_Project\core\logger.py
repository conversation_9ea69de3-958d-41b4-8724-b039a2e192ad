"""
Enhanced Logging Module for SBARDS New Architecture

This module provides advanced logging functionality for the multi-layer SBARDS project.
Supports layer-specific logging, security event logging, and performance monitoring.
"""

import os
import logging
import logging.handlers
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from .constants import LOG_LEVELS, PROJECT_NAME, MONGODB_CONFIG

# MongoDB support (optional)
try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False

class SecurityEventLogger:
    """
    Specialized logger for security events.
    """

    def __init__(self, log_dir: str):
        self.log_dir = log_dir
        self.security_log_path = os.path.join(log_dir, "security_events.log")
        self._setup_security_logger()

    def _setup_security_logger(self):
        """Setup security event logger."""
        self.security_logger = logging.getLogger("security_events")
        self.security_logger.setLevel(logging.INFO)

        # Security events should always be logged
        handler = logging.handlers.RotatingFileHandler(
            self.security_log_path,
            maxBytes=50*1024*1024,  # 50 MB
            backupCount=10
        )

        formatter = logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.security_logger.addHandler(handler)

    def log_threat_detected(self, file_path: str, threat_type: str, details: Dict[str, Any]):
        """Log threat detection event."""
        event = {
            "event_type": "threat_detected",
            "file_path": file_path,
            "threat_type": threat_type,
            "timestamp": datetime.now().isoformat(),
            "details": details
        }
        self.security_logger.warning(f"THREAT_DETECTED: {json.dumps(event)}")

    def log_file_quarantined(self, file_path: str, reason: str):
        """Log file quarantine event."""
        event = {
            "event_type": "file_quarantined",
            "file_path": file_path,
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        }
        self.security_logger.warning(f"FILE_QUARANTINED: {json.dumps(event)}")

    def log_system_access(self, user: str, action: str, resource: str):
        """Log system access event."""
        event = {
            "event_type": "system_access",
            "user": user,
            "action": action,
            "resource": resource,
            "timestamp": datetime.now().isoformat()
        }
        self.security_logger.info(f"SYSTEM_ACCESS: {json.dumps(event)}")

class PerformanceLogger:
    """
    Specialized logger for performance monitoring.
    """

    def __init__(self, log_dir: str):
        self.log_dir = log_dir
        self.performance_log_path = os.path.join(log_dir, "performance.log")
        self._setup_performance_logger()

    def _setup_performance_logger(self):
        """Setup performance logger."""
        self.performance_logger = logging.getLogger("performance")
        self.performance_logger.setLevel(logging.INFO)

        handler = logging.handlers.RotatingFileHandler(
            self.performance_log_path,
            maxBytes=20*1024*1024,  # 20 MB
            backupCount=5
        )

        formatter = logging.Formatter(
            '%(asctime)s - PERFORMANCE - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.performance_logger.addHandler(handler)

    def log_scan_performance(self, file_path: str, scan_time: float, file_size: int):
        """Log scan performance metrics."""
        metrics = {
            "file_path": file_path,
            "scan_time_seconds": scan_time,
            "file_size_bytes": file_size,
            "scan_rate_mb_per_second": (file_size / (1024*1024)) / scan_time if scan_time > 0 else 0,
            "timestamp": datetime.now().isoformat()
        }
        self.performance_logger.info(f"SCAN_PERFORMANCE: {json.dumps(metrics)}")

    def log_layer_performance(self, layer_name: str, operation: str, duration: float):
        """Log layer performance metrics."""
        metrics = {
            "layer": layer_name,
            "operation": operation,
            "duration_seconds": duration,
            "timestamp": datetime.now().isoformat()
        }
        self.performance_logger.info(f"LAYER_PERFORMANCE: {json.dumps(metrics)}")

class MongoDBLogger:
    """
    MongoDB logger for security events and threat intelligence.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize MongoDB logger.

        Args:
            config (Dict[str, Any]): MongoDB configuration
        """
        self.config = config
        self.client = None
        self.db = None
        self.enabled = config.get("enabled", False) and MONGODB_AVAILABLE

        if self.enabled:
            self._connect()

    def _connect(self):
        """Connect to MongoDB."""
        try:
            connection_string = f"mongodb://{self.config['host']}:{self.config['port']}/"
            self.client = MongoClient(connection_string, serverSelectionTimeoutMS=5000)

            # Test connection
            self.client.admin.command('ping')

            self.db = self.client[self.config['database']]
            self._create_indexes()

        except ConnectionFailure as e:
            logging.error(f"Failed to connect to MongoDB: {e}")
            self.enabled = False
        except Exception as e:
            logging.error(f"MongoDB connection error: {e}")
            self.enabled = False

    def _create_indexes(self):
        """Create indexes for better performance."""
        try:
            collections = self.config.get('collections', MONGODB_CONFIG['collections'])
            indexes = self.config.get('indexes', MONGODB_CONFIG['indexes'])

            for collection_name in collections.values():
                collection = self.db[collection_name]
                for index in indexes:
                    collection.create_index(index)

        except Exception as e:
            logging.error(f"Failed to create MongoDB indexes: {e}")

    def log_security_event(self, event_type: str, details: Dict[str, Any]):
        """
        Log security event to MongoDB.

        Args:
            event_type (str): Type of security event
            details (Dict[str, Any]): Event details
        """
        if not self.enabled:
            return

        try:
            document = {
                "timestamp": datetime.now(),
                "event_type": event_type,
                "details": details,
                "source": "sbards_security_system"
            }

            collection = self.db[self.config['collections']['security_events']]
            collection.insert_one(document)

        except Exception as e:
            logging.error(f"Failed to log security event to MongoDB: {e}")

    def log_threat_intelligence(self, file_hash: str, threat_data: Dict[str, Any]):
        """
        Log threat intelligence data to MongoDB.

        Args:
            file_hash (str): File hash
            threat_data (Dict[str, Any]): Threat intelligence data
        """
        if not self.enabled:
            return

        try:
            document = {
                "timestamp": datetime.now(),
                "file_hash": file_hash,
                "threat_data": threat_data,
                "source": "sbards_threat_intel"
            }

            collection = self.db[self.config['collections']['threat_intelligence']]
            collection.insert_one(document)

        except Exception as e:
            logging.error(f"Failed to log threat intelligence to MongoDB: {e}")

    def log_file_analysis(self, file_path: str, analysis_results: Dict[str, Any]):
        """
        Log file analysis results to MongoDB.

        Args:
            file_path (str): Path to analyzed file
            analysis_results (Dict[str, Any]): Analysis results
        """
        if not self.enabled:
            return

        try:
            document = {
                "timestamp": datetime.now(),
                "file_path": file_path,
                "analysis_results": analysis_results,
                "source": "sbards_file_analyzer"
            }

            collection = self.db[self.config['collections']['file_analysis']]
            collection.insert_one(document)

        except Exception as e:
            logging.error(f"Failed to log file analysis to MongoDB: {e}")

    def query_threat_intelligence(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """
        Query threat intelligence for a file hash.

        Args:
            file_hash (str): File hash to query

        Returns:
            Optional[Dict[str, Any]]: Threat intelligence data or None
        """
        if not self.enabled:
            return None

        try:
            collection = self.db[self.config['collections']['threat_intelligence']]
            result = collection.find_one({"file_hash": file_hash})
            return result

        except Exception as e:
            logging.error(f"Failed to query threat intelligence from MongoDB: {e}")
            return None

    def close(self):
        """Close MongoDB connection."""
        if self.client:
            self.client.close()

class EnhancedLogger:
    """
    Enhanced Logger for the new SBARDS architecture.
    Provides layer-specific logging, security event logging, and performance monitoring.
    """

    def __init__(self, log_dir: str = None, log_level: str = "info", mongodb_config: Optional[Dict[str, Any]] = None):
        """
        Initialize the enhanced logger.

        Args:
            log_dir (str): Directory for log files
            log_level (str): Logging level (debug, info, warning, error, critical)
            mongodb_config (Optional[Dict[str, Any]]): MongoDB configuration
        """
        # Use project root for log directory
        if log_dir is None:
            project_root = Path(__file__).parent.parent.absolute()
            self.log_dir = str(project_root / "logs")
        else:
            if not os.path.isabs(log_dir):
                project_root = Path(__file__).parent.parent.absolute()
                self.log_dir = str(project_root / log_dir)
            else:
                self.log_dir = log_dir

        self.log_level = self._get_log_level(log_level)
        self.loggers: Dict[str, logging.Logger] = {}

        # Create log directory if it doesn't exist
        os.makedirs(self.log_dir, exist_ok=True)

        # Initialize specialized loggers
        self.security_logger = SecurityEventLogger(self.log_dir)
        self.performance_logger = PerformanceLogger(self.log_dir)

        # Initialize MongoDB logger if configured
        self.mongodb_logger = None
        if mongodb_config:
            self.mongodb_logger = MongoDBLogger(mongodb_config)

        # Configure root logger
        self._configure_root_logger()

        # Setup layer-specific loggers
        self._setup_layer_loggers()

    def _get_log_level(self, level: str) -> int:
        """
        Get the logging level from a string.

        Args:
            level (str): Logging level as a string

        Returns:
            int: Logging level as an integer
        """
        level_map = {
            "debug": logging.DEBUG,
            "info": logging.INFO,
            "warning": logging.WARNING,
            "error": logging.ERROR,
            "critical": logging.CRITICAL
        }
        return level_map.get(level.lower(), logging.INFO)

    def _configure_root_logger(self) -> None:
        """
        Configure the root logger with enhanced formatting.
        """
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)

        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Create enhanced console handler with safe encoding for Windows PowerShell
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)

        # Use safe formatter without Unicode emojis for PowerShell compatibility
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        console_handler.setFormatter(console_formatter)

        # Set encoding to handle Unicode characters safely
        if hasattr(console_handler.stream, 'reconfigure'):
            try:
                # Try UTF-8 first, fallback to system default
                console_handler.stream.reconfigure(encoding='utf-8', errors='replace')
            except:
                try:
                    # Fallback for Windows PowerShell
                    console_handler.stream.reconfigure(encoding='cp1252', errors='replace')
                except:
                    pass

        root_logger.addHandler(console_handler)

        # Create enhanced file handler for root logger
        root_file_handler = logging.handlers.RotatingFileHandler(
            os.path.join(self.log_dir, f"{PROJECT_NAME.lower()}_main.log"),
            maxBytes=20*1024*1024,  # 20 MB
            backupCount=10
        )
        root_file_handler.setLevel(self.log_level)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        root_file_handler.setFormatter(file_formatter)
        root_logger.addHandler(root_file_handler)

    def _setup_layer_loggers(self) -> None:
        """
        Setup loggers for each layer of the architecture.
        """
        layers = [
            "capture", "static_analysis", "dynamic_analysis", "response",
            "external_integration", "memory_protection", "monitoring",
            "api", "ui", "data", "security"
        ]

        for layer in layers:
            self.get_layer_logger(layer)

    def get_logger(self, name: str) -> logging.Logger:
        """
        Get a logger with the specified name.

        Args:
            name (str): Logger name

        Returns:
            logging.Logger: Logger instance
        """
        if name in self.loggers:
            return self.loggers[name]

        # Create logger
        logger = logging.getLogger(name)
        logger.setLevel(self.log_level)

        # Create file handler
        safe_name = name.lower().replace('.', '_').replace('/', '_').replace('\\', '_')
        file_handler = logging.handlers.RotatingFileHandler(
            os.path.join(self.log_dir, f"{safe_name}.log"),
            maxBytes=10*1024*1024,  # 10 MB
            backupCount=5
        )
        file_handler.setLevel(self.log_level)

        # Enhanced formatter with more context
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s() - %(message)s'
        )
        file_handler.setFormatter(file_formatter)

        # Add file handler to logger
        logger.addHandler(file_handler)

        # Store logger
        self.loggers[name] = logger

        return logger

    def get_layer_logger(self, layer_name: str) -> logging.Logger:
        """
        Get a logger for a specific layer.

        Args:
            layer_name (str): Name of the layer

        Returns:
            logging.Logger: Layer-specific logger
        """
        logger_name = f"layer.{layer_name}"
        return self.get_logger(logger_name)

    def get_security_logger(self) -> SecurityEventLogger:
        """
        Get the security event logger.

        Returns:
            SecurityEventLogger: Security event logger instance
        """
        return self.security_logger

    def get_performance_logger(self) -> PerformanceLogger:
        """
        Get the performance logger.

        Returns:
            PerformanceLogger: Performance logger instance
        """
        return self.performance_logger

    def get_mongodb_logger(self) -> Optional[MongoDBLogger]:
        """
        Get the MongoDB logger.

        Returns:
            Optional[MongoDBLogger]: MongoDB logger instance or None
        """
        return self.mongodb_logger

    def log_layer_start(self, layer_name: str, operation: str):
        """
        Log the start of a layer operation.

        Args:
            layer_name (str): Name of the layer
            operation (str): Operation being performed
        """
        logger = self.get_layer_logger(layer_name)
        logger.info(f"Starting {operation} in {layer_name} layer")

    def log_layer_end(self, layer_name: str, operation: str, success: bool = True):
        """
        Log the end of a layer operation.

        Args:
            layer_name (str): Name of the layer
            operation (str): Operation that was performed
            success (bool): Whether the operation was successful
        """
        logger = self.get_layer_logger(layer_name)
        status = "completed successfully" if success else "failed"
        logger.info(f"Operation {operation} in {layer_name} layer {status}")

    def log_file_processing(self, file_path: str, layer_name: str, action: str, result: str):
        """
        Log file processing events.

        Args:
            file_path (str): Path to the file being processed
            layer_name (str): Layer processing the file
            action (str): Action being performed
            result (str): Result of the action
        """
        logger = self.get_layer_logger(layer_name)
        logger.info(f"File: {file_path} | Action: {action} | Result: {result}")

    def cleanup_old_logs(self, days_to_keep: int = 30):
        """
        Clean up old log files.

        Args:
            days_to_keep (int): Number of days to keep logs
        """
        cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)

        for log_file in Path(self.log_dir).glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    print(f"Deleted old log file: {log_file}")
                except Exception as e:
                    print(f"Failed to delete log file {log_file}: {e}")

# Global logger instance
_global_logger = None

def get_global_logger() -> EnhancedLogger:
    """
    Get the global logger instance.

    Returns:
        EnhancedLogger: Global logger instance
    """
    global _global_logger
    if _global_logger is None:
        _global_logger = EnhancedLogger()
    return _global_logger

def setup_logging(log_dir: str = "logs", log_level: str = "info") -> EnhancedLogger:
    """
    Setup global logging configuration.

    Args:
        log_dir (str): Directory for log files
        log_level (str): Logging level

    Returns:
        EnhancedLogger: Configured logger instance
    """
    global _global_logger
    _global_logger = EnhancedLogger(log_dir, log_level)
    return _global_logger
