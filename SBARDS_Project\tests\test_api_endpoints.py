#!/usr/bin/env python3
"""
API Endpoints Test Script
اختبار نقاط النهاية لـ API

هذا الملف يختبر جميع endpoints في API للتأكد من عملها
"""

import requests
import json
import time
from typing import Dict, Any

def test_endpoint(url: str, method: str = "GET", data: Dict[str, Any] = None, files: Dict[str, Any] = None) -> Dict[str, Any]:
    """اختبار endpoint واحد"""
    try:
        print(f"\n🔍 Testing {method} {url}")
        
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            if files:
                response = requests.post(url, data=data, files=files, timeout=10)
            else:
                response = requests.post(url, json=data, timeout=10)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        result = {
            "status_code": response.status_code,
            "success": response.status_code < 400,
            "response_time": response.elapsed.total_seconds(),
            "content_type": response.headers.get("content-type", ""),
        }
        
        # محاولة تحليل JSON
        try:
            result["json"] = response.json()
        except:
            result["text"] = response.text[:500]  # أول 500 حرف فقط
        
        if result["success"]:
            print(f"✅ Success: {response.status_code} ({result['response_time']:.3f}s)")
        else:
            print(f"❌ Failed: {response.status_code} - {response.text[:100]}")
        
        return result
        
    except Exception as e:
        print(f"❌ Exception: {e}")
        return {"error": str(e), "success": False}

def main():
    """اختبار جميع endpoints"""
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 SBARDS API Endpoints Test")
    print("=" * 50)
    
    # قائمة endpoints للاختبار
    endpoints = [
        # Basic endpoints
        {"url": f"{base_url}/", "method": "GET", "name": "Root"},
        {"url": f"{base_url}/dashboard", "method": "GET", "name": "Dashboard"},
        
        # API endpoints
        {"url": f"{base_url}/api/health", "method": "GET", "name": "Health Check"},
        {"url": f"{base_url}/api/docs", "method": "GET", "name": "API Documentation"},
        
        # Capture endpoints
        {"url": f"{base_url}/api/capture/status", "method": "GET", "name": "Capture Status"},
        {"url": f"{base_url}/api/capture/statistics", "method": "GET", "name": "Capture Statistics"},
        {"url": f"{base_url}/api/capture/monitoring-info", "method": "GET", "name": "Monitoring Info"},
        
        # Upload endpoint (with test file)
        {
            "url": f"{base_url}/api/capture/upload",
            "method": "POST",
            "name": "File Upload",
            "data": {"priority": "normal", "metadata": '{"test": true}'},
            "files": {"file": ("test.txt", b"This is a test file content", "text/plain")}
        }
    ]
    
    results = []
    
    for endpoint in endpoints:
        print(f"\n📍 Testing: {endpoint['name']}")
        print("-" * 30)
        
        result = test_endpoint(
            url=endpoint["url"],
            method=endpoint.get("method", "GET"),
            data=endpoint.get("data"),
            files=endpoint.get("files")
        )
        
        result["name"] = endpoint["name"]
        result["url"] = endpoint["url"]
        results.append(result)
        
        # انتظار قصير بين الطلبات
        time.sleep(0.5)
    
    # تلخيص النتائج
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    successful = 0
    failed = 0
    
    for result in results:
        status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
        response_time = result.get("response_time", 0)
        status_code = result.get("status_code", "N/A")
        
        print(f"{status} {result['name']:<20} ({status_code}) - {response_time:.3f}s")
        
        if result.get("success", False):
            successful += 1
        else:
            failed += 1
            # عرض تفاصيل الخطأ
            if "error" in result:
                print(f"     Error: {result['error']}")
            elif "json" in result and "detail" in result["json"]:
                print(f"     Detail: {result['json']['detail']}")
    
    print("-" * 50)
    print(f"📈 Total: {len(results)} | ✅ Passed: {successful} | ❌ Failed: {failed}")
    
    if failed == 0:
        print("🎉 All tests passed!")
    else:
        print(f"⚠️  {failed} test(s) failed")
    
    # عرض تفاصيل monitoring-info إذا نجح
    monitoring_result = next((r for r in results if r["name"] == "Monitoring Info"), None)
    if monitoring_result and monitoring_result.get("success") and "json" in monitoring_result:
        print("\n🔍 Monitoring Info Details:")
        monitoring_data = monitoring_result["json"]
        print(f"  Browsers: {len(monitoring_data.get('browsers_monitored', []))}")
        print(f"  Social Media: {len(monitoring_data.get('social_media_monitored', []))}")
        print(f"  Cloud Storage: {len(monitoring_data.get('cloud_storage_monitored', []))}")
        print(f"  Email Clients: {len(monitoring_data.get('email_clients_monitored', []))}")
        print(f"  USB Monitoring: {monitoring_data.get('usb_monitoring_enabled', False)}")
        print(f"  Total Paths: {monitoring_data.get('total_monitored_paths', 0)}")
    
    # عرض تفاصيل capture status إذا نجح
    status_result = next((r for r in results if r["name"] == "Capture Status"), None)
    if status_result and status_result.get("success") and "json" in status_result:
        print("\n📊 Capture Status Details:")
        status_data = status_result["json"]
        print(f"  Running: {status_data.get('running', False)}")
        interceptor_stats = status_data.get('interceptor_stats', {})
        print(f"  C++ Intercepts: {interceptor_stats.get('cpp_intercepts', 0)}")
        print(f"  Python Processes: {interceptor_stats.get('python_processes', 0)}")
        print(f"  Static Analysis: {interceptor_stats.get('static_analysis_requests', 0)}")
        print(f"  Files Quarantined: {interceptor_stats.get('files_quarantined', 0)}")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n\n💥 Test script error: {e}")
        exit(1)
