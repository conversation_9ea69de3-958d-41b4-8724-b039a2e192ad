"""
FastAPI Application for SBARDS

This module provides the FastAPI application for the SBARDS project.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
from fastapi import FastAP<PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import core components
from core.config import ConfigLoader
from core.logging import Logger

# Create FastAPI app
app = FastAPI(
    title="SBARDS API",
    description="API for Security Behavior Analysis and Ransomware Detection System",
    version="1.0.0"
)

# Load configuration
config_loader = ConfigLoader()
config = config_loader.get_config()
api_config = config.get("api", {})

# Set up CORS
if api_config.get("enable_cors", True):
    app.add_middleware(
        CORSMiddleware,
        allow_origins=api_config.get("allowed_origins", ["*"]),
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Set up logging
logger = Logger(
    log_dir=config.get("output", {}).get("log_directory", "logs"),
    log_level=config.get("output", {}).get("log_level", "info")
).get_logger("API")

# Import routers
from api.routers import prescanning, monitoring, system, scan

# Include routers
app.include_router(prescanning.router, prefix="/api/prescanning", tags=["prescanning"])
app.include_router(monitoring.router, prefix="/api/monitoring", tags=["monitoring"])
app.include_router(system.router, prefix="/api/system", tags=["system"])
app.include_router(scan.router, prefix="/api/scan", tags=["scan"])

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")

# Create connection manager
manager = ConnectionManager()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Process received data if needed
            await manager.send_personal_message(f"You sent: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Welcome to SBARDS API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

@app.on_event("startup")
async def startup_event():
    """Startup event handler."""
    logger.info("Starting SBARDS API")

@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler."""
    logger.info("Shutting down SBARDS API")
