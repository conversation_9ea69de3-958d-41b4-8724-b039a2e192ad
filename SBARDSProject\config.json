{"scanner": {"target_directory": "G:\\SBARDS\\SBARDSProject\\samples", "recursive": true, "max_depth": 5, "exclude_dirs": [], "exclude_extensions": [], "max_file_size_mb": 100}, "rules": {"rule_files": ["rules/custom_rules.yar"], "enable_categories": ["all"]}, "output": {"log_directory": "logs", "output_directory": "output", "json_output": true, "csv_output": false, "html_report": false, "log_level": "info"}, "performance": {"threads": 4, "batch_size": 20, "timeout_seconds": 30, "adaptive_threading": true, "memory_limit_mb": 1024}, "api": {"host": "127.0.0.1", "port": 8000, "enable_cors": true, "allowed_origins": ["*"], "enable_docs": true, "enable_websockets": true}, "monitoring": {"enabled": true, "process_monitoring": true, "filesystem_monitoring": true, "network_monitoring": true, "check_interval_seconds": 1.0, "alert_threshold": 0.7, "process": {"suspicious_process_patterns": ["<PERSON><PERSON><PERSON><PERSON>", "psexec", "powershell -enc", "cmd /c", "regsvr32", "bitsadmin"], "memory_usage_threshold_percent": 80, "cpu_usage_threshold_percent": 90}, "filesystem": {"watch_directories": ["samples"], "detect_mass_operations": true, "mass_operation_threshold": 5, "mass_operation_time_window_seconds": 10, "suspicious_extensions": [".exe", ".dll", ".bat", ".ps1", ".vbs", ".js", ".hta", ".scr"]}, "network": {"suspicious_ports": [4444, 8080, 1337, 31337, 6666], "suspicious_addresses": ["pastebin.com", "github.com", "raw.githubusercontent.com"], "detect_connection_spikes": true, "connection_spike_threshold": 10}, "alert": {"log_alerts": true, "alert_level": "info", "max_alerts_per_minute": 20, "deduplicate_alerts": true, "deduplication_window_seconds": 300}, "response": {"enabled": true, "auto_quarantine": false, "auto_block_process": false, "auto_block_network": false, "quarantine_directory": "quarantine"}}, "integration": {"enabled": true, "coordination_interval_seconds": 1.0, "fast_track_enabled": true, "fast_track_priority_threshold": 7}, "db_connection": "your_database_connection_string", "scan_threshold": 5, "log_level": "DEBUG"}