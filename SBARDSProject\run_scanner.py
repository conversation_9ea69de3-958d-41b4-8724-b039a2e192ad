#!/usr/bin/env python3
"""
SBARDS Scanner Command Line Interface

This script provides a command-line interface for the SBARDS scanner.
It allows users to scan files and directories for security threats.

Usage:
    python run_scanner.py scan [path] [options]
    python run_scanner.py report [options]
    python run_scanner.py backend [options]
    python run_scanner.py help

Commands:
    scan        Scan a file or directory for security threats
    report      View or manage scan reports
    backend     Interact with the backend API
    help        Show this help message

Options:
    --output=PATH       Specify output directory for scan results
    --format=FORMAT     Specify output format (html, json, text)
    --rules=PATH        Specify custom rules directory
    --verbose           Enable verbose output
    --quiet             Suppress all output except errors
    --no-report         Don't generate an HTML report
    --open-report       Open the HTML report after scanning
    --send-to-backend   Send scan results to the backend API
    --backend-url=URL   URL of the backend API (default: http://localhost:8000)
"""

import os
import sys
import argparse
import webbrowser
import logging
import json
import hashlib
import requests
from pathlib import Path
from datetime import datetime

# Try to import backend utilities
try:
    sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "backend"))
    from backend.app.utils import process_scan_report, calculate_file_hash
    BACKEND_AVAILABLE = True
except ImportError:
    BACKEND_AVAILABLE = False

# Configure logging
current_dir = os.path.dirname(os.path.abspath(__file__))
logs_dir = os.path.join(current_dir, "logs")
os.makedirs(logs_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(logs_dir, "scanner.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SBARDS.Scanner")

def ensure_dir_exists(path):
    """Ensure that a directory exists, creating it if necessary."""
    os.makedirs(path, exist_ok=True)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="SBARDS Scanner - Security-Based Automated Ransomware Detection System",
        usage="%(prog)s command [options]"
    )

    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Scan command
    scan_parser = subparsers.add_parser("scan", help="Scan a file or directory")
    scan_parser.add_argument("path", nargs="?", default=".", help="Path to scan (default: current directory)")
    scan_parser.add_argument("--output", help="Output directory for scan results")
    scan_parser.add_argument("--format", choices=["html", "json", "text"], default="html", help="Output format")
    scan_parser.add_argument("--rules", help="Custom rules directory")
    scan_parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    scan_parser.add_argument("--quiet", action="store_true", help="Suppress all output except errors")
    scan_parser.add_argument("--no-report", action="store_true", help="Don't generate an HTML report")
    scan_parser.add_argument("--open-report", action="store_true", help="Open the HTML report after scanning")
    scan_parser.add_argument("--send-to-backend", action="store_true", help="Send scan results to the backend API")
    scan_parser.add_argument("--backend-url", default="http://localhost:8000", help="URL of the backend API")

    # Report command
    report_parser = subparsers.add_parser("report", help="View or manage scan reports")
    report_parser.add_argument("--list", action="store_true", help="List all available reports")
    report_parser.add_argument("--view", help="View a specific report")
    report_parser.add_argument("--latest", action="store_true", help="View the latest report")
    report_parser.add_argument("--delete", help="Delete a specific report")
    report_parser.add_argument("--send-to-backend", action="store_true", help="Send report to the backend API")
    report_parser.add_argument("--backend-url", default="http://localhost:8000", help="URL of the backend API")

    # Backend command
    backend_parser = subparsers.add_parser("backend", help="Interact with the backend API")
    backend_parser.add_argument("--url", default="http://localhost:8000", help="URL of the backend API")
    backend_parser.add_argument("--stats", action="store_true", help="Get statistics from the backend")
    backend_parser.add_argument("--list-reports", action="store_true", help="List reports from the backend")
    backend_parser.add_argument("--check-file", help="Check a file against VirusTotal via the backend")

    # Help command
    subparsers.add_parser("help", help="Show help message")

    return parser.parse_args()

def command_scan(args):
    """Execute the scan command."""
    # Ensure output directory exists
    output_dir = args.output or os.path.join(current_dir, "output")
    ensure_dir_exists(output_dir)

    # Ensure logs directory exists
    logs_dir = os.path.join(current_dir, "logs")
    ensure_dir_exists(logs_dir)

    # Ensure reports directory exists
    reports_dir = os.path.join(output_dir, "reports")
    ensure_dir_exists(reports_dir)

    # Set up logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    elif args.quiet:
        logger.setLevel(logging.ERROR)

    # Log scan start
    logger.info(f"Starting scan of {args.path}")

    try:
        # Get absolute path to scan
        scan_path = os.path.abspath(args.path)

        # Check if path exists
        if not os.path.exists(scan_path):
            raise FileNotFoundError(f"Path not found: {scan_path}")

        # Determine if it's a file or directory
        is_directory = os.path.isdir(scan_path)

        # Print scan information
        if not args.quiet:
            print(f"Scanning {'directory' if is_directory else 'file'}: {scan_path}")
            print("This may take some time depending on the size...")
            print()

        # Simulate scanning process
        files_scanned = 0
        threats_found = 0

        # If it's a directory, walk through it
        if is_directory:
            for root, _, files in os.walk(scan_path):
                for file in files:
                    file_path = os.path.join(root, file)

                    # Simulate scanning a file
                    if not args.quiet and args.verbose:
                        print(f"Scanning: {file_path}")

                    # Log file scan
                    logger.debug(f"Scanning file: {file_path}")

                    # Increment counter
                    files_scanned += 1

                    # Simulate finding threats (for demonstration)
                    if "malware" in file.lower() or "virus" in file.lower() or "ransomware" in file.lower():
                        threats_found += 1
                        logger.warning(f"Potential threat found in {file_path}")
                        if not args.quiet:
                            print(f"[WARNING] Potential threat found in {file_path}")
        else:
            # Simulate scanning a single file
            files_scanned = 1

            # Simulate finding threats (for demonstration)
            file_name = os.path.basename(scan_path)
            if "malware" in file_name.lower() or "virus" in file_name.lower() or "ransomware" in file_name.lower():
                threats_found = 1
                logger.warning(f"Potential threat found in {scan_path}")
                if not args.quiet:
                    print(f"[WARNING] Potential threat found in {scan_path}")

        # Generate report
        if not args.no_report:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"scan_report_{timestamp}.html"
            report_path = os.path.join(reports_dir, report_filename)

            # Create a simple HTML report
            with open(report_path, "w") as f:
                f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>SBARDS Scan Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1 {{ color: #2c3e50; }}
        .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; }}
        .threat {{ color: #e74c3c; }}
        .safe {{ color: #27ae60; }}
    </style>
</head>
<body>
    <h1>SBARDS Scan Report</h1>
    <div class="summary">
        <h2>Scan Summary</h2>
        <p><strong>Path:</strong> {scan_path}</p>
        <p><strong>Type:</strong> {'Directory' if is_directory else 'File'}</p>
        <p><strong>Files Scanned:</strong> {files_scanned}</p>
        <p><strong>Threats Found:</strong> <span class="{'threat' if threats_found > 0 else 'safe'}">{threats_found}</span></p>
        <p><strong>Scan Date:</strong> {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>
</body>
</html>""")

            logger.info(f"Report generated: {report_path}")
        else:
            report_path = None

        # Print summary
        if not args.quiet:
            print(f"\nScan completed:")
            print(f"- Files scanned: {files_scanned}")
            print(f"- Threats found: {threats_found}")

            if report_path:
                print(f"- Report: {report_path}")

                # Open the report if requested
                if args.open_report:
                    webbrowser.open(f"file://{report_path}")

        # Send report to backend if requested
        if args.send_to_backend and report_path:
            if not BACKEND_AVAILABLE:
                logger.warning("Backend integration is not available. Make sure the backend module is installed.")
                if not args.quiet:
                    print("Warning: Backend integration is not available.")
            else:
                try:
                    if not args.quiet:
                        print(f"Sending report to backend at {args.backend_url}...")

                    # Process and send the report
                    log_file = os.path.join(logs_dir, "scanner.log")
                    result = process_scan_report(report_path, log_file, args.backend_url)

                    if "error" in result:
                        logger.error(f"Error sending report to backend: {result['error']}")
                        if not args.quiet:
                            print(f"Error sending report to backend: {result['error']}")
                    else:
                        logger.info(f"Report successfully sent to backend with ID: {result.get('scan_id', 'unknown')}")
                        if not args.quiet:
                            print(f"Report successfully sent to backend.")
                except Exception as e:
                    logger.error(f"Exception sending report to backend: {e}")
                    if not args.quiet:
                        print(f"Error sending report to backend: {e}")

        return 0

    except Exception as e:
        logger.error(f"Error during scan: {e}")
        if not args.quiet:
            print(f"Error: {e}")
        return 1

def command_report(args):
    """Execute the report command."""
    reports_dir = os.path.join(current_dir, "output", "reports")

    if not os.path.exists(reports_dir):
        print("No reports found.")
        return 0

    if args.list:
        # List all reports
        reports = [f for f in os.listdir(reports_dir) if f.endswith(".html")]
        if not reports:
            print("No reports found.")
            return 0

        print("Available reports:")
        for i, report in enumerate(sorted(reports, reverse=True)):
            report_path = os.path.join(reports_dir, report)
            report_time = os.path.getmtime(report_path)
            print(f"{i+1}. {report} (modified: {report_time})")

    elif args.view:
        # View a specific report
        report_path = os.path.join(reports_dir, args.view)
        if not os.path.exists(report_path):
            print(f"Report not found: {args.view}")
            return 1

        webbrowser.open(f"file://{report_path}")

    elif args.latest:
        # View the latest report
        reports = [os.path.join(reports_dir, f) for f in os.listdir(reports_dir) if f.endswith(".html")]
        if not reports:
            print("No reports found.")
            return 0

        # Sort by modification time (newest first)
        reports.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        webbrowser.open(f"file://{reports[0]}")

    elif args.delete:
        # Delete a specific report
        report_path = os.path.join(reports_dir, args.delete)
        if not os.path.exists(report_path):
            print(f"Report not found: {args.delete}")
            return 1

        os.remove(report_path)
        print(f"Report deleted: {args.delete}")

    else:
        # No action specified, show help
        print("Please specify an action: --list, --view, --latest, or --delete")

    return 0

def command_help():
    """Show help message."""
    print(__doc__)
    return 0

def command_backend(args):
    """Execute the backend command."""
    if not BACKEND_AVAILABLE:
        print("Backend integration is not available. Make sure the backend module is installed.")
        return 1

    try:
        # Get statistics from the backend
        if args.stats:
            print("Getting statistics from the backend...")
            response = requests.get(f"{args.url}/api/stats/")
            if response.status_code == 200:
                stats = response.json()
                print("\nBackend Statistics:")
                print(f"- Total Scans: {stats.get('total_scans', 0)}")
                print(f"- Total Files Scanned: {stats.get('total_files_scanned', 0)}")
                print(f"- Total Threats Found: {stats.get('total_threats', 0)}")
            else:
                print(f"Error getting statistics: {response.status_code} - {response.text}")
                return 1

        # List reports from the backend
        elif args.list_reports:
            print("Getting reports from the backend...")
            response = requests.get(f"{args.url}/api/reports/")
            if response.status_code == 200:
                reports = response.json()
                if not reports:
                    print("No reports found in the backend.")
                    return 0

                print("\nBackend Reports:")
                for i, report in enumerate(reports):
                    print(f"{i+1}. Scan ID: {report.get('scan_id')}")
                    print(f"   - Path: {report.get('scan_path')}")
                    print(f"   - Files Scanned: {report.get('files_scanned')}")
                    print(f"   - Threats Found: {report.get('threats_found')}")
                    print(f"   - Timestamp: {report.get('timestamp')}")
                    print()
            else:
                print(f"Error getting reports: {response.status_code} - {response.text}")
                return 1

        # Check a file against VirusTotal
        elif args.check_file:
            file_path = args.check_file
            if not os.path.exists(file_path):
                print(f"File not found: {file_path}")
                return 1

            print(f"Checking file {file_path} against VirusTotal via the backend...")

            # Calculate file hash locally
            file_hash = calculate_file_hash(file_path)
            print(f"File hash (SHA-256): {file_hash}")

            # Send file to backend for VirusTotal check
            with open(file_path, "rb") as f:
                files = {"file": (os.path.basename(file_path), f)}
                response = requests.post(f"{args.url}/api/check-file/", files=files)

            if response.status_code == 200:
                result = response.json()
                print("\nVirusTotal Results:")
                if "error" in result.get("virustotal_result", {}):
                    print(f"Error: {result['virustotal_result']['error']}")
                else:
                    vt_data = result.get("virustotal_result", {}).get("data", {})
                    vt_attributes = vt_data.get("attributes", {})

                    # Print detection stats
                    stats = vt_attributes.get("last_analysis_stats", {})
                    if stats:
                        print(f"- Malicious: {stats.get('malicious', 0)}")
                        print(f"- Suspicious: {stats.get('suspicious', 0)}")
                        print(f"- Undetected: {stats.get('undetected', 0)}")
                        print(f"- Harmless: {stats.get('harmless', 0)}")

                    # Print more details if available
                    print(f"- First Seen: {vt_attributes.get('first_submission_date', 'Unknown')}")
                    print(f"- Last Seen: {vt_attributes.get('last_analysis_date', 'Unknown')}")

                    # Print link to VirusTotal
                    print(f"\nView full report: https://www.virustotal.com/gui/file/{file_hash}")
            else:
                print(f"Error checking file: {response.status_code} - {response.text}")
                return 1

        else:
            print("Please specify an action: --stats, --list-reports, or --check-file")
            return 1

        return 0

    except Exception as e:
        print(f"Error interacting with backend: {e}")
        return 1

def main():
    """Main function."""
    args = parse_arguments()

    if args.command == "scan":
        return command_scan(args)
    elif args.command == "report":
        return command_report(args)
    elif args.command == "backend":
        return command_backend(args)
    elif args.command == "help" or args.command is None:
        return command_help()
    else:
        print(f"Unknown command: {args.command}")
        print("Use 'python run_scanner.py help' for usage information.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
