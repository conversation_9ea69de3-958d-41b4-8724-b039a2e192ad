#!/usr/bin/env python3
"""
SBARDS Capture Layer Demo Runner

This script demonstrates the capture layer functionality without requiring
full system setup. It shows the core features working together.
"""

import os
import sys
import time
import tempfile
import json
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_demo_files():
    """Create demo files for testing."""
    demo_dir = tempfile.mkdtemp(prefix="sbards_demo_")
    
    # Clean file
    clean_file = os.path.join(demo_dir, "clean_document.txt")
    with open(clean_file, "w") as f:
        f.write("This is a clean document for SBARDS testing.\nNo suspicious content here.")
    
    # Suspicious file
    suspicious_file = os.path.join(demo_dir, "suspicious_file.txt")
    with open(suspicious_file, "w") as f:
        f.write("encrypt decrypt ransom bitcoin payment wallet crypto")
    
    # Binary file
    binary_file = os.path.join(demo_dir, "test_binary.bin")
    with open(binary_file, "wb") as f:
        f.write(b"\x00\x01\x02\x03\x04\x05" * 50)
    
    return demo_dir, [clean_file, suspicious_file, binary_file]

def test_file_interceptor():
    """Test the file interceptor functionality."""
    print("🔍 Testing File Interceptor...")
    
    try:
        from capture.python.file_interceptor import FileInterceptor, FastAPIFileHandler
        
        # Create demo files
        demo_dir, demo_files = create_demo_files()
        
        # Configure interceptor
        config = {
            "target_directories": [demo_dir],
            "recursive": False,
            "max_depth": 1,
            "exclude_dirs": [],
            "exclude_extensions": [],
            "max_file_size_mb": 10,
            "threads": 2,
            "enable_real_time": True
        }
        
        # Create interceptor
        interceptor = FileInterceptor(config)
        
        # Test basic functionality
        print(f"  ✅ FileInterceptor created successfully")
        print(f"  📁 Demo directory: {demo_dir}")
        print(f"  📄 Demo files: {len(demo_files)} files created")
        
        # Test file handler
        temp_storage = os.path.join(demo_dir, "temp_storage")
        os.makedirs(temp_storage, exist_ok=True)
        
        handler = FastAPIFileHandler(interceptor, temp_storage)
        print(f"  ✅ FastAPIFileHandler created successfully")
        
        # Test file processing
        for file_path in demo_files:
            with open(file_path, "rb") as f:
                content = f.read()
                filename = os.path.basename(file_path)
                
                # Simulate file upload
                result = handler.handle_file_upload_sync(content, filename, "text/plain")
                
                print(f"  📄 Processed {filename}:")
                print(f"    - Status: {result['status']}")
                print(f"    - Suspicious: {result['is_suspicious']}")
                print(f"    - File ID: {result['file_id']}")
        
        # Get statistics
        stats = interceptor.get_statistics()
        print(f"  📊 Interceptor Statistics:")
        print(f"    - Files processed: {stats.get('files_processed', 0)}")
        print(f"    - Suspicious files: {stats.get('suspicious_files', 0)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing file interceptor: {e}")
        return False

def test_redis_queue():
    """Test Redis queue functionality."""
    print("🔄 Testing Redis Queue...")
    
    try:
        from capture.python.redis_queue import RedisQueueManager
        
        config = {
            "redis_host": "localhost",
            "redis_port": 6379,
            "redis_db": 0,
            "queue_prefix": "demo_sbards",
            "max_retries": 3
        }
        
        redis_queue = RedisQueueManager(config)
        
        if redis_queue.is_available():
            print(f"  ✅ Redis connection successful")
            
            # Test message sending
            test_data = {"test": "demo_message", "timestamp": time.time()}
            success = redis_queue.send_message("demo_queue", test_data)
            
            if success:
                print(f"  ✅ Message sent successfully")
            else:
                print(f"  ⚠️ Message sending failed")
            
            # Get statistics
            stats = redis_queue.get_statistics()
            print(f"  📊 Redis Statistics:")
            print(f"    - Messages sent: {stats.get('messages_sent', 0)}")
            print(f"    - Connection status: {stats.get('connection_status', 'unknown')}")
            
            redis_queue.stop()
            return True
        else:
            print(f"  ⚠️ Redis not available - skipping Redis tests")
            return True
            
    except Exception as e:
        print(f"  ⚠️ Redis test skipped: {e}")
        return True

def test_configuration():
    """Test configuration loading."""
    print("⚙️ Testing Configuration...")
    
    try:
        from core.config import ConfigLoader
        
        config_loader = ConfigLoader()
        config = config_loader.get_config()
        
        print(f"  ✅ Configuration loaded successfully")
        print(f"  📋 Project: {config.get('core', {}).get('project_name', 'Unknown')}")
        print(f"  📋 Version: {config.get('core', {}).get('version', 'Unknown')}")
        
        # Test capture layer config
        capture_config = config_loader.get_layer_config("capture")
        print(f"  📋 Capture enabled: {capture_config.get('enabled', False)}")
        print(f"  📋 Target directories: {len(capture_config.get('target_directories', []))}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing configuration: {e}")
        return False

def test_logging():
    """Test logging functionality."""
    print("📝 Testing Logging...")
    
    try:
        from core.logger import get_global_logger
        
        logger = get_global_logger().get_layer_logger("demo")
        
        logger.info("Demo logging test - INFO level")
        logger.warning("Demo logging test - WARNING level")
        logger.debug("Demo logging test - DEBUG level")
        
        print(f"  ✅ Logging system working")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing logging: {e}")
        return False

def main():
    """Main demo function."""
    print("🚀 SBARDS Capture Layer Demo")
    print("=" * 50)
    print()
    
    # Test results
    results = []
    
    # Test configuration
    results.append(("Configuration", test_configuration()))
    
    # Test logging
    results.append(("Logging", test_logging()))
    
    # Test file interceptor
    results.append(("File Interceptor", test_file_interceptor()))
    
    # Test Redis queue
    results.append(("Redis Queue", test_redis_queue()))
    
    # Summary
    print()
    print("📊 Demo Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print()
    print(f"📈 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Capture layer is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    print()
    print("🔗 Next Steps:")
    print("  1. Start the FastAPI server: python -m uvicorn api.main:app --reload")
    print("  2. Visit http://127.0.0.1:8000/docs for API documentation")
    print("  3. Test file upload: POST to /api/capture/upload")
    print("  4. Monitor status: GET /api/capture/status")

if __name__ == "__main__":
    main()
