"""
Main Entry Point for SBARDS

This module provides the main entry point for the SBARDS project.
"""

import os
import sys
import argparse
import logging
import uvicorn
from typing import Dict, List, Any, Optional

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import core components
from core.config import ConfigLoader
from core.logging import Logger
from core.utils import get_platform, is_windows, is_linux

# Import phases
from phases.prescanning.orchestrator import Orchestrator
from phases.monitoring.monitor_manager import MonitorManager
from phases.integration.phase_coordinator import PhaseCoordinator

# Import API
from api.routers.prescanning import set_orchestrator
from api.routers.monitoring import set_manager

def parse_args():
    """
    Parse command-line arguments.

    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description="SBARDS - Security Behavior Analysis and Ransomware Detection System")

    # General arguments
    parser.add_argument("--config", type=str, default="config.json", help="Configuration file")
    parser.add_argument("--log-level", type=str, default="info", help="Logging level")

    # Mode arguments
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument("--prescanning", action="store_true", help="Run Pre-Scanning phase only")
    mode_group.add_argument("--monitoring", action="store_true", help="Run Monitoring phase only")
    mode_group.add_argument("--api", action="store_true", help="Run API server only")

    # Pre-Scanning arguments
    parser.add_argument("--target", type=str, help="Target directory to scan")

    # API arguments
    parser.add_argument("--host", type=str, default="127.0.0.1", help="API server host")
    parser.add_argument("--port", type=int, default=8000, help="API server port")

    return parser.parse_args()

def main():
    """Main entry point."""
    # Parse arguments
    args = parse_args()

    # Load configuration
    config_loader = ConfigLoader(args.config)
    config = config_loader.get_config()

    # Set up logging
    logger = Logger(
        log_dir=config.get("output", {}).get("log_directory", "logs"),
        log_level=args.log_level
    )

    # Get logger
    log = logger.get_logger("Main")

    # Log platform information
    platform = get_platform()
    log.info(f"Platform: {platform}")

    # Run in the specified mode
    if args.prescanning:
        # Run Pre-Scanning phase only
        log.info("Running Pre-Scanning phase only")

        # Create orchestrator
        orchestrator = Orchestrator(args.config)

        # Run scan
        target_directory = args.target or config.get("scanner", {}).get("target_directory", "samples")
        orchestrator.run_scan(target_directory)

    elif args.monitoring:
        # Run Monitoring phase only
        log.info("Running Monitoring phase only")

        # Create monitor manager
        monitor_manager = MonitorManager(args.config)

        # Start monitoring
        monitor_manager.start_monitoring()

        try:
            # Keep running until interrupted
            log.info("Press Ctrl+C to stop monitoring")
            while True:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            log.info("Stopping monitoring")
            monitor_manager.stop_monitoring()

    elif args.api:
        # Run API server only
        log.info("Running API server only")

        # Create orchestrator and monitor manager
        orchestrator = Orchestrator(args.config)
        monitor_manager = MonitorManager(args.config)

        # Set orchestrator and monitor manager for API
        set_orchestrator(orchestrator)
        set_manager(monitor_manager)

        # Run API server
        uvicorn.run(
            "api.main:app",
            host=args.host,
            port=args.port,
            reload=True
        )

    else:
        # Run all phases
        log.info("Running all phases")

        # Create orchestrator and monitor manager
        orchestrator = Orchestrator(args.config)
        monitor_manager = MonitorManager(args.config)

        # Create phase coordinator
        phase_coordinator = PhaseCoordinator(args.config)
        phase_coordinator.set_prescanning_orchestrator(orchestrator)
        phase_coordinator.set_monitoring_manager(monitor_manager)

        # Set orchestrator and monitor manager for API
        set_orchestrator(orchestrator)
        set_manager(monitor_manager)

        # Start monitoring
        monitor_manager.start_monitoring()

        # Start coordination
        phase_coordinator.start_coordination()

        # Run API server
        uvicorn.run(
            "api.main:app",
            host=args.host,
            port=args.port
        )

        # Stop coordination and monitoring when API server stops
        phase_coordinator.stop_coordination()
        monitor_manager.stop_monitoring()

if __name__ == "__main__":
    main()
