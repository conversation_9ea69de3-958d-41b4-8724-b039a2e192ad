#!/usr/bin/env python3
"""
Build Script for SBARDS Static Analysis Layer

This script automates the compilation and setup of the static analysis layer,
including C++ components, Python dependencies, and YARA rules.

Usage:
    python build_static_analysis.py [options]

Options:
    --clean         Clean build directory before building
    --debug         Build in debug mode
    --test          Run tests after building
    --install       Install libraries after building
    --setup-yara    Setup YARA rules and dependencies
    --benchmark     Run performance benchmarks
"""

import os
import sys
import subprocess
import shutil
import argparse
import platform
from pathlib import Path
import json
import time

class StaticAnalysisBuilder:
    """Builder for SBARDS Static Analysis Layer."""
    
    def __init__(self):
        """Initialize the builder."""
        self.script_dir = Path(__file__).parent
        self.cpp_dir = self.script_dir / "cpp"
        self.python_dir = self.script_dir / "python"
        self.yara_rules_dir = self.script_dir / "yara_rules"
        self.build_dir = self.cpp_dir / "build"
        self.lib_dir = self.script_dir / "lib"
        
        # Platform detection
        self.is_windows = platform.system() == "Windows"
        self.is_linux = platform.system() == "Linux"
        self.is_macos = platform.system() == "Darwin"
        
        # Build configuration
        self.build_type = "Release"
        self.generator = self._detect_generator()
        
    def _detect_generator(self):
        """Detect appropriate CMake generator."""
        if self.is_windows:
            if shutil.which("cl"):
                return "Visual Studio 16 2019"
            elif shutil.which("gcc"):
                return "MinGW Makefiles"
            else:
                return "NMake Makefiles"
        else:
            if shutil.which("ninja"):
                return "Ninja"
            else:
                return "Unix Makefiles"
    
    def check_dependencies(self):
        """Check if required build tools and dependencies are available."""
        print("🔍 Checking static analysis dependencies...")
        
        missing_deps = []
        
        # Check CMake
        if not shutil.which("cmake"):
            missing_deps.append("CMake 3.16+")
        
        # Check compiler
        if self.is_windows:
            if not (shutil.which("cl") or shutil.which("gcc") or shutil.which("clang")):
                missing_deps.append("C++ compiler (Visual Studio/MinGW/Clang)")
        else:
            if not (shutil.which("gcc") or shutil.which("clang")):
                missing_deps.append("C++ compiler (GCC/Clang)")
        
        # Check Python dependencies
        try:
            import yara
        except ImportError:
            missing_deps.append("yara-python (pip install yara-python)")
        
        try:
            import requests
        except ImportError:
            missing_deps.append("requests (pip install requests)")
        
        # Check OpenSSL (for hash generation)
        if self.is_windows:
            if not Path("C:/Program Files/OpenSSL-Win64").exists():
                print("⚠️  OpenSSL not found - hash generation may not work")
        else:
            if not shutil.which("openssl"):
                print("⚠️  OpenSSL not found - hash generation may not work")
        
        if missing_deps:
            print("❌ Missing dependencies:")
            for dep in missing_deps:
                print(f"   - {dep}")
            return False
        
        print("✅ All dependencies found")
        return True
    
    def clean_build(self):
        """Clean the build directory."""
        print("🧹 Cleaning build directory...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"✅ Cleaned {self.build_dir}")
        
        if self.lib_dir.exists():
            shutil.rmtree(self.lib_dir)
            print(f"✅ Cleaned {self.lib_dir}")
    
    def build_cpp_components(self):
        """Build C++ components."""
        print("🔨 Building C++ static analysis components...")
        
        # Create build directory
        self.build_dir.mkdir(parents=True, exist_ok=True)
        
        # CMake configure
        configure_cmd = [
            "cmake",
            "-S", str(self.cpp_dir),
            "-B", str(self.build_dir),
            f"-DCMAKE_BUILD_TYPE={self.build_type}",
            f"-DCMAKE_INSTALL_PREFIX={self.lib_dir}"
        ]
        
        if self.generator:
            configure_cmd.extend(["-G", self.generator])
        
        print(f"Configuring with: {' '.join(configure_cmd)}")
        result = subprocess.run(configure_cmd, cwd=self.cpp_dir)
        
        if result.returncode != 0:
            print("❌ CMake configuration failed")
            return False
        
        # Build
        build_cmd = [
            "cmake",
            "--build", str(self.build_dir),
            "--config", self.build_type,
            "--parallel"
        ]
        
        print(f"Building with: {' '.join(build_cmd)}")
        result = subprocess.run(build_cmd, cwd=self.cpp_dir)
        
        if result.returncode != 0:
            print("❌ Build failed")
            return False
        
        print("✅ C++ components built successfully")
        return True
    
    def setup_yara_rules(self):
        """Setup YARA rules and validate them."""
        print("📋 Setting up YARA rules...")
        
        if not self.yara_rules_dir.exists():
            self.yara_rules_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created YARA rules directory: {self.yara_rules_dir}")
        
        # Validate existing rules
        rule_files = list(self.yara_rules_dir.rglob("*.yar")) + list(self.yara_rules_dir.rglob("*.yara"))
        
        if not rule_files:
            print("⚠️  No YARA rules found - creating basic rule set...")
            self._create_basic_rules()
        else:
            print(f"📋 Found {len(rule_files)} YARA rule files")
            
            # Validate rules
            valid_rules = 0
            invalid_rules = 0
            
            try:
                import yara
                
                for rule_file in rule_files:
                    try:
                        yara.compile(filepath=str(rule_file))
                        valid_rules += 1
                    except Exception as e:
                        print(f"❌ Invalid rule file {rule_file}: {e}")
                        invalid_rules += 1
                
                print(f"✅ Valid rules: {valid_rules}")
                if invalid_rules > 0:
                    print(f"❌ Invalid rules: {invalid_rules}")
                    
            except ImportError:
                print("⚠️  YARA not available - skipping rule validation")
        
        return True
    
    def _create_basic_rules(self):
        """Create basic YARA rules for testing."""
        basic_rules = {
            "basic/test_rule.yar": '''
rule SBARDS_Test_Rule
{
    meta:
        description = "Basic test rule for SBARDS"
        author = "SBARDS Team"
        category = "test"
        severity = "low"
    
    strings:
        $test = "SBARDS_TEST_PATTERN"
    
    condition:
        $test
}
''',
            "malware/generic_malware.yar": '''
rule Generic_Malware_Indicator
{
    meta:
        description = "Generic malware indicators"
        author = "SBARDS Team"
        category = "malware"
        severity = "high"
    
    strings:
        $malware1 = "malware" nocase
        $malware2 = "trojan" nocase
        $malware3 = "backdoor" nocase
    
    condition:
        any of them
}
''',
            "permissions/dangerous_permissions.yar": '''
rule Dangerous_File_Permissions
{
    meta:
        description = "Detects files with dangerous permissions"
        author = "SBARDS Team"
        category = "permissions"
        severity = "medium"
    
    condition:
        // This rule is processed by the permission analyzer
        true
}
'''
        }
        
        for rule_path, rule_content in basic_rules.items():
            full_path = self.yara_rules_dir / rule_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(full_path, 'w') as f:
                f.write(rule_content)
            
            print(f"✅ Created rule: {rule_path}")
    
    def install_python_dependencies(self):
        """Install Python dependencies."""
        print("🐍 Installing Python dependencies...")
        
        dependencies = [
            "yara-python>=4.3.0",
            "requests>=2.31.0",
            "cryptography>=41.0.0"
        ]
        
        for dep in dependencies:
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", dep
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"✅ Installed: {dep}")
                else:
                    print(f"❌ Failed to install {dep}: {result.stderr}")
                    
            except Exception as e:
                print(f"❌ Error installing {dep}: {e}")
        
        return True
    
    def run_tests(self):
        """Run static analysis tests."""
        print("🧪 Running static analysis tests...")
        
        test_file = self.script_dir / "test_static_analysis.py"
        if not test_file.exists():
            print("❌ Test file not found")
            return False
        
        result = subprocess.run([
            sys.executable, str(test_file)
        ], cwd=self.script_dir)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Some tests failed")
            return False
    
    def run_benchmarks(self):
        """Run performance benchmarks."""
        print("📊 Running performance benchmarks...")
        
        # Create test files for benchmarking
        benchmark_dir = self.script_dir / "benchmark_files"
        benchmark_dir.mkdir(exist_ok=True)
        
        # Create various file types for testing
        test_files = []
        
        # Text files
        for i in range(10):
            test_file = benchmark_dir / f"text_{i}.txt"
            with open(test_file, 'w') as f:
                f.write(f"Test content {i} " * 100)
            test_files.append(test_file)
        
        # Binary files
        import random
        for i in range(5):
            test_file = benchmark_dir / f"binary_{i}.bin"
            with open(test_file, 'wb') as f:
                data = bytes([random.randint(0, 255) for _ in range(1000)])
                f.write(data)
            test_files.append(test_file)
        
        # Benchmark signature checking
        start_time = time.time()
        for test_file in test_files:
            # Mock signature checking
            with open(test_file, 'rb') as f:
                header = f.read(16)
                # Simple signature check
                if header.startswith(b'MZ'):
                    file_type = "PE"
                elif header.startswith(b'\x7fELF'):
                    file_type = "ELF"
                else:
                    file_type = "Unknown"
        
        signature_time = time.time() - start_time
        
        # Benchmark entropy calculation
        start_time = time.time()
        for test_file in test_files:
            with open(test_file, 'rb') as f:
                data = f.read()
                # Simple entropy calculation
                if data:
                    byte_counts = [0] * 256
                    for byte in data:
                        byte_counts[byte] += 1
                    
                    entropy = 0.0
                    data_len = len(data)
                    for count in byte_counts:
                        if count > 0:
                            p = count / data_len
                            entropy -= p * (p.bit_length() - 1)
        
        entropy_time = time.time() - start_time
        
        # Print benchmark results
        print(f"\n📊 Benchmark Results:")
        print(f"  Files processed: {len(test_files)}")
        print(f"  Signature checking: {signature_time:.3f}s ({len(test_files)/signature_time:.1f} files/sec)")
        print(f"  Entropy calculation: {entropy_time:.3f}s ({len(test_files)/entropy_time:.1f} files/sec)")
        
        # Cleanup
        shutil.rmtree(benchmark_dir, ignore_errors=True)
        
        return True
    
    def create_python_bindings(self):
        """Create Python bindings for C++ components."""
        print("🐍 Creating Python bindings...")
        
        bindings_content = '''"""
Python bindings for SBARDS Static Analysis C++ components.
"""

import ctypes
import os
import json
from pathlib import Path

# Find shared libraries
lib_dir = Path(__file__).parent / "lib"

class StaticAnalysisBindings:
    """Python bindings for C++ static analysis components."""
    
    def __init__(self):
        """Initialize bindings."""
        self.signature_checker = None
        self.permission_analyzer = None
        self.entropy_checker = None
        self.hash_generator = None
        
        self._load_libraries()
    
    def _load_libraries(self):
        """Load C++ shared libraries."""
        try:
            if os.name == "nt":
                # Windows
                sig_lib = lib_dir / "signature_checker.dll"
                perm_lib = lib_dir / "permission_analyzer.dll"
                entropy_lib = lib_dir / "entropy_checker.dll"
                hash_lib = lib_dir / "hash_generator.dll"
            else:
                # Unix/Linux
                sig_lib = lib_dir / "libsignature_checker.so"
                perm_lib = lib_dir / "libpermission_analyzer.so"
                entropy_lib = lib_dir / "libentropy_checker.so"
                hash_lib = lib_dir / "libhash_generator.so"
            
            # Load libraries if they exist
            if sig_lib.exists():
                self.signature_checker = ctypes.CDLL(str(sig_lib))
                self._setup_signature_checker()
            
            if perm_lib.exists():
                self.permission_analyzer = ctypes.CDLL(str(perm_lib))
                self._setup_permission_analyzer()
            
            if entropy_lib.exists():
                self.entropy_checker = ctypes.CDLL(str(entropy_lib))
                self._setup_entropy_checker()
            
            if hash_lib.exists():
                self.hash_generator = ctypes.CDLL(str(hash_lib))
                self._setup_hash_generator()
                
        except Exception as e:
            print(f"Warning: Failed to load C++ libraries: {e}")
    
    def _setup_signature_checker(self):
        """Setup signature checker function signatures."""
        if self.signature_checker:
            self.signature_checker.create_signature_checker.restype = ctypes.c_void_p
            self.signature_checker.destroy_signature_checker.argtypes = [ctypes.c_void_p]
            self.signature_checker.check_file_signature_json.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            self.signature_checker.check_file_signature_json.restype = ctypes.c_char_p
    
    def _setup_permission_analyzer(self):
        """Setup permission analyzer function signatures."""
        if self.permission_analyzer:
            self.permission_analyzer.create_permission_analyzer.restype = ctypes.c_void_p
            self.permission_analyzer.destroy_permission_analyzer.argtypes = [ctypes.c_void_p]
            self.permission_analyzer.analyze_file_permissions_detailed.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            self.permission_analyzer.analyze_file_permissions_detailed.restype = ctypes.c_char_p
    
    def _setup_entropy_checker(self):
        """Setup entropy checker function signatures."""
        if self.entropy_checker:
            self.entropy_checker.create_entropy_checker.restype = ctypes.c_void_p
            self.entropy_checker.destroy_entropy_checker.argtypes = [ctypes.c_void_p]
            self.entropy_checker.analyze_file_entropy.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            self.entropy_checker.analyze_file_entropy.restype = ctypes.c_char_p
    
    def _setup_hash_generator(self):
        """Setup hash generator function signatures."""
        if self.hash_generator:
            self.hash_generator.create_hash_generator.restype = ctypes.c_void_p
            self.hash_generator.destroy_hash_generator.argtypes = [ctypes.c_void_p]
            self.hash_generator.generate_file_hash.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            self.hash_generator.generate_file_hash.restype = ctypes.c_char_p
    
    def check_signature(self, file_path: str) -> dict:
        """Check file signature."""
        if not self.signature_checker:
            return {"error": "Signature checker not available"}
        
        try:
            checker = self.signature_checker.create_signature_checker()
            result_json = self.signature_checker.check_file_signature_json(checker, file_path.encode())
            self.signature_checker.destroy_signature_checker(checker)
            
            return json.loads(result_json.decode())
        except Exception as e:
            return {"error": str(e)}
    
    def analyze_permissions(self, file_path: str) -> dict:
        """Analyze file permissions."""
        if not self.permission_analyzer:
            return {"error": "Permission analyzer not available"}
        
        try:
            analyzer = self.permission_analyzer.create_permission_analyzer()
            result_json = self.permission_analyzer.analyze_file_permissions_detailed(analyzer, file_path.encode())
            self.permission_analyzer.destroy_permission_analyzer(analyzer)
            
            return json.loads(result_json.decode())
        except Exception as e:
            return {"error": str(e)}
    
    def check_entropy(self, file_path: str) -> dict:
        """Check file entropy."""
        if not self.entropy_checker:
            return {"error": "Entropy checker not available"}
        
        try:
            checker = self.entropy_checker.create_entropy_checker()
            result_json = self.entropy_checker.analyze_file_entropy(checker, file_path.encode())
            self.entropy_checker.destroy_entropy_checker(checker)
            
            return json.loads(result_json.decode())
        except Exception as e:
            return {"error": str(e)}
    
    def generate_hash(self, file_path: str) -> dict:
        """Generate file hash."""
        if not self.hash_generator:
            return {"error": "Hash generator not available"}
        
        try:
            generator = self.hash_generator.create_hash_generator()
            result_json = self.hash_generator.generate_file_hash(generator, file_path.encode())
            self.hash_generator.destroy_hash_generator(generator)
            
            return json.loads(result_json.decode())
        except Exception as e:
            return {"error": str(e)}

# Global instance
static_analysis_bindings = StaticAnalysisBindings()
'''
        
        bindings_file = self.script_dir / "cpp_bindings.py"
        with open(bindings_file, 'w') as f:
            f.write(bindings_content)
        
        print(f"✅ Python bindings created: {bindings_file}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Build SBARDS Static Analysis Layer")
    parser.add_argument("--clean", action="store_true", help="Clean build directory")
    parser.add_argument("--debug", action="store_true", help="Build in debug mode")
    parser.add_argument("--test", action="store_true", help="Run tests after building")
    parser.add_argument("--install", action="store_true", help="Install dependencies")
    parser.add_argument("--setup-yara", action="store_true", help="Setup YARA rules")
    parser.add_argument("--benchmark", action="store_true", help="Run benchmarks")
    parser.add_argument("--bindings", action="store_true", help="Create Python bindings")
    
    args = parser.parse_args()
    
    # Create builder
    builder = StaticAnalysisBuilder()
    
    # Set build type
    if args.debug:
        builder.build_type = "Debug"
    
    print("🚀 SBARDS Static Analysis Layer Builder")
    print("=" * 50)
    print(f"Platform: {platform.system()} {platform.machine()}")
    print(f"Build type: {builder.build_type}")
    print(f"Generator: {builder.generator}")
    print()
    
    # Check dependencies
    if not builder.check_dependencies():
        print("\n❌ Please install missing dependencies and try again")
        sys.exit(1)
    
    # Install Python dependencies if requested
    if args.install:
        builder.install_python_dependencies()
    
    # Clean if requested
    if args.clean:
        builder.clean_build()
    
    # Setup YARA rules if requested
    if args.setup_yara:
        builder.setup_yara_rules()
    
    # Build C++ components
    if not builder.build_cpp_components():
        sys.exit(1)
    
    # Create Python bindings if requested
    if args.bindings:
        builder.create_python_bindings()
    
    # Run tests if requested
    if args.test:
        if not builder.run_tests():
            print("⚠️  Some tests failed, but build completed")
    
    # Run benchmarks if requested
    if args.benchmark:
        builder.run_benchmarks()
    
    print()
    print("🎉 Static Analysis Layer build completed successfully!")
    print()
    print("Next steps:")
    print("  1. Run tests: python build_static_analysis.py --test")
    print("  2. Setup YARA rules: python build_static_analysis.py --setup-yara")
    print("  3. Create Python bindings: python build_static_analysis.py --bindings")
    print("  4. Run benchmarks: python build_static_analysis.py --benchmark")
    print("  5. Test the layer: python test_static_analysis.py")

if __name__ == "__main__":
    main()
