/*
    Basic YARA Rules for SBARDS - Migrated from Original Project
    
    These are the foundational rules from the original SBARDSProject,
    enhanced and reorganized for better performance and accuracy.
*/

import "pe"
import "hash"
import "math"

rule ExampleRule
{
    meta:
        description = "This is an example rule - migrated from original project"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "test"
        severity = "info"
        version = "2.0"

    strings:
        $text_string = "malicious" nocase
        $hex_string = { 4D 5A 90 00 }  // MZ header for PE files

    condition:
        any of them
}

rule PotentialRansomware_Enhanced
{
    meta:
        description = "Detects potential ransomware characteristics - enhanced version"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "ransomware"
        severity = "high"
        version = "2.0"

    strings:
        // Ransom note indicators
        $ransom_note1 = "your files have been encrypted" nocase
        $ransom_note2 = "files are encrypted" nocase
        $ransom_note3 = "decrypt your files" nocase
        $ransom_note4 = "pay the ransom" nocase
        
        // Payment indicators
        $bitcoin1 = "bitcoin" nocase
        $bitcoin2 = "btc" nocase
        $payment1 = "payment" nocase
        $payment2 = "pay" nocase
        
        // Crypto indicators
        $decrypt1 = "decrypt" nocase
        $decrypt2 = "decryption" nocase
        $encrypt1 = "encrypt" nocase
        $encrypt2 = "encryption" nocase
        
        // File extension changes
        $ext1 = ".locked" nocase
        $ext2 = ".encrypted" nocase
        $ext3 = ".crypto" nocase
        
        // Common ransomware strings
        $restore = "restore" nocase
        $recover = "recover" nocase
        $key = "private key" nocase

    condition:
        (
            any of ($ransom_note*) or
            (any of ($bitcoin*) and any of ($payment*)) or
            (any of ($encrypt*) and any of ($decrypt*)) or
            any of ($ext*) or
            ($restore and $key) or
            ($recover and any of ($payment*))
        ) and
        filesize < 10MB
}

rule SuspiciousExecutable
{
    meta:
        description = "Detects suspicious executable characteristics"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "malware"
        severity = "medium"
        version = "2.0"

    strings:
        // Process manipulation
        $proc1 = "CreateRemoteThread" ascii wide
        $proc2 = "WriteProcessMemory" ascii wide
        $proc3 = "VirtualAllocEx" ascii wide
        $proc4 = "OpenProcess" ascii wide
        
        // Registry manipulation
        $reg1 = "RegCreateKey" ascii wide
        $reg2 = "RegSetValue" ascii wide
        $reg3 = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run" ascii wide
        
        // Network activity
        $net1 = "InternetOpen" ascii wide
        $net2 = "HttpSendRequest" ascii wide
        $net3 = "send" ascii wide
        $net4 = "recv" ascii wide
        
        // File operations
        $file1 = "CreateFile" ascii wide
        $file2 = "WriteFile" ascii wide
        $file3 = "DeleteFile" ascii wide

    condition:
        pe.is_pe and
        (
            (2 of ($proc*)) or
            (1 of ($proc*) and 1 of ($reg*)) or
            (1 of ($net*) and 1 of ($file*))
        ) and
        filesize < 5MB
}

rule KeyloggerBehavior
{
    meta:
        description = "Detects keylogger behavior patterns"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "spyware"
        severity = "high"
        version = "2.0"

    strings:
        // Keyboard hooks
        $hook1 = "SetWindowsHookEx" ascii wide
        $hook2 = "GetAsyncKeyState" ascii wide
        $hook3 = "GetKeyState" ascii wide
        $hook4 = "WH_KEYBOARD_LL" ascii wide
        
        // Key capture
        $key1 = "VK_RETURN" ascii wide
        $key2 = "VK_SPACE" ascii wide
        $key3 = "VK_BACK" ascii wide
        
        // Logging
        $log1 = "keylog" ascii wide nocase
        $log2 = "keys.txt" ascii wide nocase
        $log3 = "passwords" ascii wide nocase

    condition:
        pe.is_pe and
        (
            (2 of ($hook*) and 1 of ($key*)) or
            (1 of ($hook*) and 1 of ($log*))
        ) and
        filesize < 2MB
}

rule NetworkTrojan
{
    meta:
        description = "Detects network trojan characteristics"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "trojan"
        severity = "high"
        version = "2.0"

    strings:
        // Network functions
        $net1 = "socket" ascii wide
        $net2 = "connect" ascii wide
        $net3 = "bind" ascii wide
        $net4 = "listen" ascii wide
        $net5 = "accept" ascii wide
        
        // Remote access
        $remote1 = "remote" ascii wide nocase
        $remote2 = "backdoor" ascii wide nocase
        $remote3 = "shell" ascii wide nocase
        $remote4 = "cmd" ascii wide
        
        // Data exfiltration
        $exfil1 = "upload" ascii wide nocase
        $exfil2 = "download" ascii wide nocase
        $exfil3 = "transfer" ascii wide nocase

    condition:
        pe.is_pe and
        (
            (3 of ($net*)) or
            (2 of ($net*) and 1 of ($remote*)) or
            (1 of ($net*) and 1 of ($remote*) and 1 of ($exfil*))
        ) and
        filesize < 8MB
}

rule SuspiciousScript
{
    meta:
        description = "Detects suspicious script characteristics"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "script"
        severity = "medium"
        version = "2.0"

    strings:
        // PowerShell suspicious
        $ps1 = "powershell" ascii wide nocase
        $ps2 = "Invoke-Expression" ascii wide
        $ps3 = "IEX" ascii wide
        $ps4 = "DownloadString" ascii wide
        $ps5 = "EncodedCommand" ascii wide
        
        // Command execution
        $cmd1 = "cmd.exe" ascii wide
        $cmd2 = "system(" ascii wide
        $cmd3 = "exec(" ascii wide
        $cmd4 = "shell_exec" ascii wide
        
        // Obfuscation
        $obf1 = "base64" ascii wide nocase
        $obf2 = "decode" ascii wide nocase
        $obf3 = "unescape" ascii wide nocase

    condition:
        (
            (2 of ($ps*)) or
            (1 of ($ps*) and 1 of ($cmd*)) or
            (1 of ($cmd*) and 1 of ($obf*))
        ) and
        filesize < 1MB
}

rule HighEntropyFile
{
    meta:
        description = "Detects files with high entropy (possibly packed/encrypted)"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "packer"
        severity = "medium"
        version = "2.0"

    condition:
        math.entropy(0, filesize) >= 7.0 and
        filesize > 1KB and
        filesize < 50MB
}

rule SuspiciousFileExtension
{
    meta:
        description = "Detects files with suspicious double extensions"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "social_engineering"
        severity = "medium"
        version = "2.0"

    strings:
        $ext1 = ".pdf.exe" nocase
        $ext2 = ".doc.exe" nocase
        $ext3 = ".jpg.exe" nocase
        $ext4 = ".txt.exe" nocase
        $ext5 = ".mp3.exe" nocase
        $ext6 = ".avi.exe" nocase

    condition:
        any of them and
        pe.is_pe
}

rule CommonMalwareStrings
{
    meta:
        description = "Detects common malware-related strings"
        author = "SBARDS Project"
        date = "2025-05-24"
        category = "malware"
        severity = "low"
        version = "2.0"

    strings:
        // Common malware terms
        $mal1 = "virus" ascii wide nocase
        $mal2 = "trojan" ascii wide nocase
        $mal3 = "backdoor" ascii wide nocase
        $mal4 = "rootkit" ascii wide nocase
        $mal5 = "keylogger" ascii wide nocase
        $mal6 = "stealer" ascii wide nocase
        
        // Suspicious behavior
        $behav1 = "disable antivirus" ascii wide nocase
        $behav2 = "bypass security" ascii wide nocase
        $behav3 = "hide process" ascii wide nocase
        $behav4 = "inject code" ascii wide nocase

    condition:
        (
            2 of ($mal*) or
            1 of ($behav*)
        ) and
        filesize < 10MB
}
