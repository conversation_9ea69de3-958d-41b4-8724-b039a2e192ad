"""
Logging middleware for SBARDS API

This module provides comprehensive request/response logging middleware.
"""

import time
import json
from typing import Callable
from datetime import datetime

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from core.logger import get_global_logger

# Configure logging
logger = get_global_logger().get_layer_logger("api.middleware.logging")


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""
    
    def __init__(self, app: ASGIApp, log_level: str = "INFO"):
        """
        Initialize logging middleware.
        
        Args:
            app (ASGIApp): ASGI application.
            log_level (str): Logging level.
        """
        super().__init__(app)
        self.log_level = log_level.upper()
        self.logger = logger
        
        # Paths to skip logging (to reduce noise)
        self.skip_paths = {
            "/health",
            "/api/health", 
            "/metrics",
            "/favicon.ico"
        }
        
        # Sensitive headers to mask
        self.sensitive_headers = {
            "authorization",
            "x-api-key",
            "x-apikey",
            "cookie",
            "set-cookie"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with comprehensive logging.
        
        Args:
            request (Request): FastAPI request.
            call_next (Callable): Next middleware or route handler.
            
        Returns:
            Response: FastAPI response.
        """
        # Skip logging for certain paths
        if self._should_skip_logging(request.url.path):
            return await call_next(request)
        
        # Start timing
        start_time = time.time()
        request_timestamp = datetime.utcnow()
        
        # Extract request information
        request_info = await self._extract_request_info(request)
        
        # Log request
        self._log_request(request_info, request_timestamp)
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = (time.time() - start_time) * 1000
            
            # Extract response information
            response_info = self._extract_response_info(response, process_time)
            
            # Log response
            self._log_response(request_info, response_info)
            
            # Add processing time header
            response.headers["X-Process-Time"] = f"{process_time:.2f}ms"
            
            return response
            
        except Exception as e:
            # Log error
            process_time = (time.time() - start_time) * 1000
            self._log_error(request_info, str(e), process_time)
            raise
    
    def _should_skip_logging(self, path: str) -> bool:
        """
        Check if logging should be skipped for this path.
        
        Args:
            path (str): Request path.
            
        Returns:
            bool: True if logging should be skipped.
        """
        return path in self.skip_paths or path.startswith("/static/")
    
    async def _extract_request_info(self, request: Request) -> dict:
        """
        Extract relevant information from request.
        
        Args:
            request (Request): FastAPI request.
            
        Returns:
            dict: Request information.
        """
        # Get client information
        client_ip = "unknown"
        if request.client:
            client_ip = request.client.host
        
        # Check for forwarded IP headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        # Extract headers (mask sensitive ones)
        headers = {}
        for name, value in request.headers.items():
            if name.lower() in self.sensitive_headers:
                headers[name] = "***MASKED***"
            else:
                headers[name] = value
        
        # Extract query parameters
        query_params = dict(request.query_params)
        
        # Try to extract body for POST/PUT requests (be careful with large bodies)
        body_info = None
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            if "application/json" in content_type:
                try:
                    # Note: This consumes the body, so we need to be careful
                    # In production, you might want to limit body size or skip body logging
                    body_info = "JSON body present"
                except Exception:
                    body_info = "Could not parse JSON body"
            elif "multipart/form-data" in content_type:
                body_info = "Multipart form data"
            else:
                body_info = f"Body with content-type: {content_type}"
        
        return {
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "client_ip": client_ip,
            "user_agent": request.headers.get("User-Agent", "unknown"),
            "headers": headers,
            "query_params": query_params,
            "body_info": body_info
        }
    
    def _extract_response_info(self, response: Response, process_time: float) -> dict:
        """
        Extract relevant information from response.
        
        Args:
            response (Response): FastAPI response.
            process_time (float): Processing time in milliseconds.
            
        Returns:
            dict: Response information.
        """
        # Extract response headers (mask sensitive ones)
        headers = {}
        for name, value in response.headers.items():
            if name.lower() in self.sensitive_headers:
                headers[name] = "***MASKED***"
            else:
                headers[name] = value
        
        return {
            "status_code": response.status_code,
            "headers": headers,
            "process_time_ms": round(process_time, 2)
        }
    
    def _log_request(self, request_info: dict, timestamp: datetime):
        """
        Log request information.
        
        Args:
            request_info (dict): Request information.
            timestamp (datetime): Request timestamp.
        """
        log_data = {
            "event": "request",
            "timestamp": timestamp.isoformat(),
            "method": request_info["method"],
            "path": request_info["path"],
            "client_ip": request_info["client_ip"],
            "user_agent": request_info["user_agent"]
        }
        
        if self.log_level == "DEBUG":
            log_data.update({
                "url": request_info["url"],
                "headers": request_info["headers"],
                "query_params": request_info["query_params"],
                "body_info": request_info["body_info"]
            })
        
        self.logger.info(f"Request: {request_info['method']} {request_info['path']} - Client: {request_info['client_ip']}")
        
        if self.log_level == "DEBUG":
            self.logger.debug(f"Request details: {json.dumps(log_data, indent=2)}")
    
    def _log_response(self, request_info: dict, response_info: dict):
        """
        Log response information.
        
        Args:
            request_info (dict): Request information.
            response_info (dict): Response information.
        """
        log_data = {
            "event": "response",
            "method": request_info["method"],
            "path": request_info["path"],
            "client_ip": request_info["client_ip"],
            "status_code": response_info["status_code"],
            "process_time_ms": response_info["process_time_ms"]
        }
        
        if self.log_level == "DEBUG":
            log_data["response_headers"] = response_info["headers"]
        
        # Determine log level based on status code
        if response_info["status_code"] >= 500:
            log_level = "error"
        elif response_info["status_code"] >= 400:
            log_level = "warning"
        else:
            log_level = "info"
        
        message = (f"Response: {request_info['method']} {request_info['path']} - "
                  f"Status: {response_info['status_code']} - "
                  f"Time: {response_info['process_time_ms']:.2f}ms")
        
        getattr(self.logger, log_level)(message)
        
        if self.log_level == "DEBUG":
            self.logger.debug(f"Response details: {json.dumps(log_data, indent=2)}")
    
    def _log_error(self, request_info: dict, error: str, process_time: float):
        """
        Log error information.
        
        Args:
            request_info (dict): Request information.
            error (str): Error message.
            process_time (float): Processing time in milliseconds.
        """
        log_data = {
            "event": "error",
            "method": request_info["method"],
            "path": request_info["path"],
            "client_ip": request_info["client_ip"],
            "error": error,
            "process_time_ms": round(process_time, 2)
        }
        
        message = (f"Error: {request_info['method']} {request_info['path']} - "
                  f"Client: {request_info['client_ip']} - "
                  f"Error: {error} - "
                  f"Time: {process_time:.2f}ms")
        
        self.logger.error(message)
        self.logger.debug(f"Error details: {json.dumps(log_data, indent=2)}")
