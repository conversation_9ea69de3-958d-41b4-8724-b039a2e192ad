/*
    Custom YARA Rules for SBARDS - Migrated from Original Project
    
    These are custom rules from the original SBARDSProject,
    enhanced and reorganized for better performance and accuracy.
    
    Migration: SBARDSProject/rules/custom_rules.yar → SBARDS_Project/static_analysis/yara_rules/custom/custom_rules.yar
*/

import "pe"
import "hash"
import "math"

rule ExampleRule
{
    meta:
        description = "This is an example rule - migrated from original project"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "test"
        severity = "info"
        version = "2.0"
        migrated_from = "SBARDSProject/rules/custom_rules.yar"

    strings:
        $text_string = "malicious" nocase
        $hex_string = { 4D 5A 90 00 }  // MZ header for PE files

    condition:
        any of them
}

rule PotentialRansomware_Enhanced
{
    meta:
        description = "Detects potential ransomware characteristics - enhanced version"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "ransomware"
        severity = "high"
        version = "2.0"
        migrated_from = "SBARDSProject/rules/custom_rules.yar"

    strings:
        // Enhanced ransom note indicators
        $ransom_note1 = "your files have been encrypted" nocase
        $ransom_note2 = "files are encrypted" nocase
        $ransom_note3 = "decrypt your files" nocase
        $ransom_note4 = "pay the ransom" nocase
        
        // Payment indicators
        $bitcoin1 = "bitcoin" nocase
        $bitcoin2 = "btc" nocase
        $payment1 = "payment" nocase
        $payment2 = "pay" nocase
        
        // Crypto indicators
        $decrypt1 = "decrypt" nocase
        $decrypt2 = "decryption" nocase
        $encrypt1 = "encrypt" nocase
        $encrypt2 = "encryption" nocase
        
        // File extension changes
        $ext1 = ".locked" nocase
        $ext2 = ".encrypted" nocase
        $ext3 = ".crypto" nocase
        
        // Common ransomware strings
        $restore = "restore" nocase
        $recover = "recover" nocase
        $key = "private key" nocase

    condition:
        (
            any of ($ransom_note*) or
            (any of ($bitcoin*) and any of ($payment*)) or
            (any of ($encrypt*) and any of ($decrypt*)) or
            any of ($ext*) or
            ($restore and $key) or
            ($recover and any of ($payment*))
        ) and
        filesize < 10MB
}

rule CustomMalwareDetection
{
    meta:
        description = "Custom malware detection patterns"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "malware"
        severity = "medium"
        version = "2.0"

    strings:
        // Process manipulation
        $proc1 = "CreateRemoteThread" ascii wide
        $proc2 = "WriteProcessMemory" ascii wide
        $proc3 = "VirtualAllocEx" ascii wide
        $proc4 = "OpenProcess" ascii wide
        
        // Registry manipulation
        $reg1 = "RegCreateKey" ascii wide
        $reg2 = "RegSetValue" ascii wide
        $reg3 = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run" ascii wide
        
        // Network activity
        $net1 = "InternetOpen" ascii wide
        $net2 = "HttpSendRequest" ascii wide
        $net3 = "send" ascii wide
        $net4 = "recv" ascii wide

    condition:
        pe.is_pe and
        (
            (2 of ($proc*)) or
            (1 of ($proc*) and 1 of ($reg*)) or
            (1 of ($net*) and 1 of ($proc*))
        ) and
        filesize < 5MB
}

rule CustomSuspiciousScript
{
    meta:
        description = "Custom suspicious script detection"
        author = "SBARDS Project"
        date = "2025-05-25"
        category = "script"
        severity = "medium"
        version = "2.0"

    strings:
        // PowerShell suspicious
        $ps1 = "powershell" ascii wide nocase
        $ps2 = "Invoke-Expression" ascii wide
        $ps3 = "IEX" ascii wide
        $ps4 = "DownloadString" ascii wide
        $ps5 = "EncodedCommand" ascii wide
        
        // Command execution
        $cmd1 = "cmd.exe" ascii wide
        $cmd2 = "system(" ascii wide
        $cmd3 = "exec(" ascii wide
        $cmd4 = "shell_exec" ascii wide
        
        // Obfuscation
        $obf1 = "base64" ascii wide nocase
        $obf2 = "decode" ascii wide nocase
        $obf3 = "unescape" ascii wide nocase

    condition:
        (
            (2 of ($ps*)) or
            (1 of ($ps*) and 1 of ($cmd*)) or
            (1 of ($cmd*) and 1 of ($obf*))
        ) and
        filesize < 1MB
}
