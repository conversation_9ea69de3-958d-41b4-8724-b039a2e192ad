"""
Advanced Threat Intelligence Module for SBARDS v2.0

This module provides comprehensive threat intelligence capabilities for the new SBARDS architecture.
Created for the enhanced multi-layer security system.

Creation Status: ✅ NEWLY CREATED for new architecture
Creation Date: 2025-05-25
Features: Real-time threat feeds, IOC analysis, threat attribution, and intelligence correlation.
"""

import os
import sys
import json
import time
import hashlib
import requests
import threading
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import get_global_logger
from core.config import get_config
from core.constants import ThreatLevel, THREAT_LEVEL_NAMES

class ThreatType(Enum):
    """Threat type enumeration."""
    MALWARE = "malware"
    RANSOMWARE = "ransomware"
    TROJAN = "trojan"
    VIRUS = "virus"
    WORM = "worm"
    ROOTKIT = "rootkit"
    SPYWARE = "spyware"
    ADWARE = "adware"
    BACKDOOR = "backdoor"
    BOTNET = "botnet"
    APT = "apt"
    PHISHING = "phishing"
    SUSPICIOUS = "suspicious"
    UNKNOWN = "unknown"

class ThreatSource(Enum):
    """Threat intelligence source enumeration."""
    VIRUSTOTAL = "virustotal"
    MALWAREBYTES = "malwarebytes"
    KASPERSKY = "kaspersky"
    SYMANTEC = "symantec"
    CROWDSTRIKE = "crowdstrike"
    FIREEYE = "fireeye"
    INTERNAL = "internal"
    COMMUNITY = "community"
    YARA = "yara"
    SIGNATURE = "signature"

@dataclass
class ThreatIndicator:
    """Threat indicator data structure."""
    ioc_type: str  # hash, ip, domain, url, file_path
    ioc_value: str
    threat_type: ThreatType
    threat_level: ThreatLevel
    confidence: float  # 0.0 - 1.0
    source: ThreatSource
    first_seen: datetime
    last_seen: datetime
    description: str
    tags: List[str]
    attribution: Optional[str] = None
    campaign: Optional[str] = None
    family: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None

@dataclass
class ThreatReport:
    """Comprehensive threat report."""
    report_id: str
    timestamp: datetime
    file_path: str
    file_hash: str
    threat_indicators: List[ThreatIndicator]
    overall_threat_level: ThreatLevel
    confidence_score: float
    recommendation: str
    analysis_summary: str
    sources_consulted: List[str]
    processing_time: float

class ThreatIntelligenceEngine:
    """
    Advanced threat intelligence engine for SBARDS.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the threat intelligence engine.
        
        Args:
            config_path (Optional[str]): Path to configuration file
        """
        # Load configuration
        self.config = get_config(config_path)
        self.intel_config = self.config.get("threat_intelligence", {})
        
        # Initialize logging
        self.logger = get_global_logger().get_layer_logger("security")
        
        # Initialize data structures
        self.threat_cache: Dict[str, ThreatIndicator] = {}
        self.threat_feeds: Dict[str, Dict[str, Any]] = {}
        self.ioc_database: Dict[str, List[ThreatIndicator]] = {}
        
        # Threading
        self.update_lock = threading.RLock()
        self.cache_lock = threading.RLock()
        
        # API configurations
        self.api_configs = {
            "virustotal": {
                "api_key": self.intel_config.get("virustotal_api_key", ""),
                "base_url": "https://www.virustotal.com/api/v3",
                "rate_limit": 4,  # requests per minute
                "enabled": bool(self.intel_config.get("virustotal_api_key", ""))
            },
            "malwarebytes": {
                "api_key": self.intel_config.get("malwarebytes_api_key", ""),
                "base_url": "https://api.malwarebytes.com/v1",
                "rate_limit": 10,
                "enabled": bool(self.intel_config.get("malwarebytes_api_key", ""))
            }
        }
        
        # Rate limiting
        self.api_calls: Dict[str, List[float]] = {}
        
        # Initialize threat feeds
        self._initialize_threat_feeds()
        
        self.logger.info("Threat Intelligence Engine initialized")
    
    def _initialize_threat_feeds(self):
        """Initialize threat intelligence feeds."""
        try:
            # Load local threat database
            self._load_local_threat_database()
            
            # Initialize API rate limiting
            for api_name in self.api_configs:
                self.api_calls[api_name] = []
            
            # Load cached indicators
            self._load_threat_cache()
            
            self.logger.info("Threat feeds initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize threat feeds: {e}")
    
    def _load_local_threat_database(self):
        """Load local threat database."""
        try:
            # Load known malware hashes
            malware_db_path = Path("data/virus_hashes/known_malware.json")
            if malware_db_path.exists():
                with open(malware_db_path, 'r') as f:
                    malware_data = json.load(f)
                    
                for entry in malware_data.get("malware_hashes", []):
                    indicator = ThreatIndicator(
                        ioc_type="hash",
                        ioc_value=entry["hash"],
                        threat_type=ThreatType(entry.get("type", "malware")),
                        threat_level=ThreatLevel(entry.get("level", "medium")),
                        confidence=entry.get("confidence", 0.9),
                        source=ThreatSource.INTERNAL,
                        first_seen=datetime.fromisoformat(entry.get("first_seen", datetime.now().isoformat())),
                        last_seen=datetime.fromisoformat(entry.get("last_seen", datetime.now().isoformat())),
                        description=entry.get("description", ""),
                        tags=entry.get("tags", []),
                        family=entry.get("family"),
                        additional_data=entry.get("metadata", {})
                    )
                    
                    self._add_indicator_to_cache(indicator)
            
            self.logger.info("Local threat database loaded")
            
        except Exception as e:
            self.logger.error(f"Failed to load local threat database: {e}")
    
    def _load_threat_cache(self):
        """Load threat cache from disk."""
        try:
            cache_path = Path("data/threat_cache.json")
            if cache_path.exists():
                with open(cache_path, 'r') as f:
                    cache_data = json.load(f)
                    
                for ioc_value, indicator_data in cache_data.items():
                    indicator = ThreatIndicator(
                        ioc_type=indicator_data["ioc_type"],
                        ioc_value=indicator_data["ioc_value"],
                        threat_type=ThreatType(indicator_data["threat_type"]),
                        threat_level=ThreatLevel(indicator_data["threat_level"]),
                        confidence=indicator_data["confidence"],
                        source=ThreatSource(indicator_data["source"]),
                        first_seen=datetime.fromisoformat(indicator_data["first_seen"]),
                        last_seen=datetime.fromisoformat(indicator_data["last_seen"]),
                        description=indicator_data["description"],
                        tags=indicator_data["tags"],
                        attribution=indicator_data.get("attribution"),
                        campaign=indicator_data.get("campaign"),
                        family=indicator_data.get("family"),
                        additional_data=indicator_data.get("additional_data")
                    )
                    
                    self._add_indicator_to_cache(indicator)
            
            self.logger.info("Threat cache loaded")
            
        except Exception as e:
            self.logger.error(f"Failed to load threat cache: {e}")
    
    def _add_indicator_to_cache(self, indicator: ThreatIndicator):
        """Add threat indicator to cache."""
        with self.cache_lock:
            self.threat_cache[indicator.ioc_value] = indicator
            
            # Add to IOC database by type
            if indicator.ioc_type not in self.ioc_database:
                self.ioc_database[indicator.ioc_type] = []
            
            # Check if indicator already exists
            existing = False
            for existing_indicator in self.ioc_database[indicator.ioc_type]:
                if existing_indicator.ioc_value == indicator.ioc_value:
                    # Update existing indicator
                    existing_indicator.last_seen = indicator.last_seen
                    existing_indicator.confidence = max(existing_indicator.confidence, indicator.confidence)
                    existing = True
                    break
            
            if not existing:
                self.ioc_database[indicator.ioc_type].append(indicator)
    
    def _check_rate_limit(self, api_name: str) -> bool:
        """Check if API rate limit allows request."""
        if api_name not in self.api_configs:
            return False
        
        rate_limit = self.api_configs[api_name]["rate_limit"]
        current_time = time.time()
        
        # Clean old entries (older than 1 minute)
        self.api_calls[api_name] = [
            call_time for call_time in self.api_calls[api_name]
            if current_time - call_time < 60
        ]
        
        # Check if we can make a request
        if len(self.api_calls[api_name]) < rate_limit:
            self.api_calls[api_name].append(current_time)
            return True
        
        return False
    
    def analyze_file_hash(self, file_hash: str, hash_type: str = "sha256") -> ThreatReport:
        """
        Analyze a file hash for threat indicators.
        
        Args:
            file_hash (str): File hash to analyze
            hash_type (str): Type of hash (sha256, md5, sha1)
            
        Returns:
            ThreatReport: Comprehensive threat analysis report
        """
        start_time = time.time()
        report_id = f"threat_analysis_{int(start_time)}"
        
        self.logger.info(f"Analyzing file hash: {file_hash[:16]}...")
        
        # Initialize report
        threat_indicators = []
        sources_consulted = []
        
        try:
            # Check local cache first
            if file_hash in self.threat_cache:
                indicator = self.threat_cache[file_hash]
                threat_indicators.append(indicator)
                sources_consulted.append("local_cache")
                self.logger.info(f"Found threat indicator in local cache")
            
            # Check local IOC database
            hash_indicators = self.ioc_database.get("hash", [])
            for indicator in hash_indicators:
                if indicator.ioc_value == file_hash:
                    threat_indicators.append(indicator)
                    sources_consulted.append("local_database")
                    break
            
            # Query external APIs if enabled
            if self.intel_config.get("enable_external_apis", True):
                # VirusTotal API
                if self.api_configs["virustotal"]["enabled"]:
                    vt_result = self._query_virustotal_hash(file_hash)
                    if vt_result:
                        threat_indicators.extend(vt_result)
                        sources_consulted.append("virustotal")
                
                # Additional APIs can be added here
            
            # Analyze and correlate indicators
            overall_threat_level, confidence_score, recommendation = self._analyze_indicators(threat_indicators)
            
            # Generate analysis summary
            analysis_summary = self._generate_analysis_summary(threat_indicators, overall_threat_level)
            
            # Create threat report
            report = ThreatReport(
                report_id=report_id,
                timestamp=datetime.now(),
                file_path="",  # Will be set by caller
                file_hash=file_hash,
                threat_indicators=threat_indicators,
                overall_threat_level=overall_threat_level,
                confidence_score=confidence_score,
                recommendation=recommendation,
                analysis_summary=analysis_summary,
                sources_consulted=sources_consulted,
                processing_time=time.time() - start_time
            )
            
            # Cache new indicators
            for indicator in threat_indicators:
                if indicator.ioc_value not in self.threat_cache:
                    self._add_indicator_to_cache(indicator)
            
            self.logger.info(f"Threat analysis completed in {report.processing_time:.2f}s")
            return report
            
        except Exception as e:
            self.logger.error(f"Error analyzing file hash: {e}")
            
            # Return safe report on error
            return ThreatReport(
                report_id=report_id,
                timestamp=datetime.now(),
                file_path="",
                file_hash=file_hash,
                threat_indicators=[],
                overall_threat_level=ThreatLevel.UNKNOWN,
                confidence_score=0.0,
                recommendation="Unable to analyze - scan manually",
                analysis_summary="Analysis failed due to error",
                sources_consulted=sources_consulted,
                processing_time=time.time() - start_time
            )
    
    def _query_virustotal_hash(self, file_hash: str) -> List[ThreatIndicator]:
        """Query VirusTotal API for file hash."""
        if not self._check_rate_limit("virustotal"):
            self.logger.warning("VirusTotal rate limit exceeded")
            return []
        
        try:
            api_key = self.api_configs["virustotal"]["api_key"]
            base_url = self.api_configs["virustotal"]["base_url"]
            
            headers = {
                "x-apikey": api_key,
                "User-Agent": "SBARDS-v2.0"
            }
            
            url = f"{base_url}/files/{file_hash}"
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return self._parse_virustotal_response(data)
            elif response.status_code == 404:
                # File not found in VirusTotal
                return []
            else:
                self.logger.warning(f"VirusTotal API error: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error querying VirusTotal: {e}")
            return []
    
    def _parse_virustotal_response(self, data: Dict[str, Any]) -> List[ThreatIndicator]:
        """Parse VirusTotal API response."""
        indicators = []
        
        try:
            attributes = data.get("data", {}).get("attributes", {})
            stats = attributes.get("last_analysis_stats", {})
            
            malicious_count = stats.get("malicious", 0)
            suspicious_count = stats.get("suspicious", 0)
            total_engines = sum(stats.values())
            
            if malicious_count > 0 or suspicious_count > 0:
                # Determine threat level based on detection ratio
                detection_ratio = (malicious_count + suspicious_count) / max(total_engines, 1)
                
                if detection_ratio >= 0.5:
                    threat_level = ThreatLevel.HIGH
                elif detection_ratio >= 0.3:
                    threat_level = ThreatLevel.MEDIUM
                elif detection_ratio >= 0.1:
                    threat_level = ThreatLevel.LOW
                else:
                    threat_level = ThreatLevel.SUSPICIOUS
                
                # Extract threat type from scan results
                threat_type = self._extract_threat_type_from_vt(attributes)
                
                indicator = ThreatIndicator(
                    ioc_type="hash",
                    ioc_value=attributes.get("sha256", ""),
                    threat_type=threat_type,
                    threat_level=threat_level,
                    confidence=min(detection_ratio * 2, 1.0),  # Scale confidence
                    source=ThreatSource.VIRUSTOTAL,
                    first_seen=datetime.fromtimestamp(attributes.get("first_submission_date", time.time())),
                    last_seen=datetime.fromtimestamp(attributes.get("last_analysis_date", time.time())),
                    description=f"Detected by {malicious_count}/{total_engines} engines",
                    tags=attributes.get("tags", []),
                    additional_data={
                        "detection_ratio": f"{malicious_count + suspicious_count}/{total_engines}",
                        "scan_results": attributes.get("last_analysis_results", {}),
                        "file_type": attributes.get("type_description", ""),
                        "file_size": attributes.get("size", 0)
                    }
                )
                
                indicators.append(indicator)
            
        except Exception as e:
            self.logger.error(f"Error parsing VirusTotal response: {e}")
        
        return indicators
    
    def _extract_threat_type_from_vt(self, attributes: Dict[str, Any]) -> ThreatType:
        """Extract threat type from VirusTotal attributes."""
        # Check scan results for threat type indicators
        scan_results = attributes.get("last_analysis_results", {})
        threat_names = []
        
        for engine, result in scan_results.items():
            if result.get("category") == "malicious":
                threat_name = result.get("result", "").lower()
                threat_names.append(threat_name)
        
        # Analyze threat names to determine type
        threat_keywords = {
            ThreatType.RANSOMWARE: ["ransom", "crypto", "locker", "crypt"],
            ThreatType.TROJAN: ["trojan", "troj", "backdoor"],
            ThreatType.VIRUS: ["virus", "infector"],
            ThreatType.WORM: ["worm", "network"],
            ThreatType.ROOTKIT: ["rootkit", "stealth"],
            ThreatType.SPYWARE: ["spy", "keylog", "steal"],
            ThreatType.ADWARE: ["adware", "pup", "unwanted"]
        }
        
        for threat_type, keywords in threat_keywords.items():
            for keyword in keywords:
                if any(keyword in name for name in threat_names):
                    return threat_type
        
        return ThreatType.MALWARE  # Default
    
    def _analyze_indicators(self, indicators: List[ThreatIndicator]) -> Tuple[ThreatLevel, float, str]:
        """Analyze threat indicators to determine overall threat level."""
        if not indicators:
            return ThreatLevel.SAFE, 0.0, "No threat indicators found"
        
        # Calculate weighted threat score
        total_score = 0.0
        total_weight = 0.0
        
        for indicator in indicators:
            # Weight based on source reliability
            source_weight = {
                ThreatSource.VIRUSTOTAL: 0.9,
                ThreatSource.INTERNAL: 1.0,
                ThreatSource.MALWAREBYTES: 0.85,
                ThreatSource.KASPERSKY: 0.8,
                ThreatSource.COMMUNITY: 0.6
            }.get(indicator.source, 0.5)
            
            # Threat level score
            level_score = {
                ThreatLevel.CRITICAL: 1.0,
                ThreatLevel.HIGH: 0.8,
                ThreatLevel.MEDIUM: 0.6,
                ThreatLevel.LOW: 0.4,
                ThreatLevel.SUSPICIOUS: 0.2,
                ThreatLevel.SAFE: 0.0
            }.get(indicator.threat_level, 0.0)
            
            weighted_score = level_score * indicator.confidence * source_weight
            total_score += weighted_score
            total_weight += source_weight
        
        # Calculate overall confidence
        overall_confidence = total_score / max(total_weight, 1.0)
        
        # Determine overall threat level
        if overall_confidence >= 0.8:
            overall_level = ThreatLevel.CRITICAL
            recommendation = "QUARANTINE IMMEDIATELY - High confidence malware detected"
        elif overall_confidence >= 0.6:
            overall_level = ThreatLevel.HIGH
            recommendation = "QUARANTINE - Likely malware detected"
        elif overall_confidence >= 0.4:
            overall_level = ThreatLevel.MEDIUM
            recommendation = "CAUTION - Suspicious activity detected"
        elif overall_confidence >= 0.2:
            overall_level = ThreatLevel.LOW
            recommendation = "MONITOR - Low-level threats detected"
        elif overall_confidence > 0.0:
            overall_level = ThreatLevel.SUSPICIOUS
            recommendation = "INVESTIGATE - Suspicious indicators found"
        else:
            overall_level = ThreatLevel.SAFE
            recommendation = "SAFE - No significant threats detected"
        
        return overall_level, overall_confidence, recommendation
    
    def _generate_analysis_summary(self, indicators: List[ThreatIndicator], threat_level: ThreatLevel) -> str:
        """Generate human-readable analysis summary."""
        if not indicators:
            return "No threat indicators found. File appears to be clean."
        
        summary_parts = []
        
        # Count indicators by type
        type_counts = {}
        source_counts = {}
        
        for indicator in indicators:
            threat_type = indicator.threat_type.value
            source = indicator.source.value
            
            type_counts[threat_type] = type_counts.get(threat_type, 0) + 1
            source_counts[source] = source_counts.get(source, 0) + 1
        
        # Build summary
        summary_parts.append(f"Overall Threat Level: {threat_level.name}")
        summary_parts.append(f"Total Indicators: {len(indicators)}")
        
        if type_counts:
            types_str = ", ".join([f"{count} {type_name}" for type_name, count in type_counts.items()])
            summary_parts.append(f"Threat Types: {types_str}")
        
        if source_counts:
            sources_str = ", ".join([f"{count} from {source}" for source, count in source_counts.items()])
            summary_parts.append(f"Sources: {sources_str}")
        
        # Add specific recommendations
        if threat_level in [ThreatLevel.CRITICAL, ThreatLevel.HIGH]:
            summary_parts.append("IMMEDIATE ACTION REQUIRED: This file poses a significant security risk.")
        elif threat_level == ThreatLevel.MEDIUM:
            summary_parts.append("CAUTION ADVISED: This file shows suspicious characteristics.")
        
        return " | ".join(summary_parts)
    
    def get_threat_statistics(self) -> Dict[str, Any]:
        """Get threat intelligence statistics."""
        with self.cache_lock:
            stats = {
                "total_indicators": len(self.threat_cache),
                "indicators_by_type": {},
                "indicators_by_source": {},
                "indicators_by_level": {},
                "cache_size": len(self.threat_cache),
                "last_updated": datetime.now().isoformat()
            }
            
            for indicator in self.threat_cache.values():
                # Count by type
                threat_type = indicator.threat_type.value
                stats["indicators_by_type"][threat_type] = stats["indicators_by_type"].get(threat_type, 0) + 1
                
                # Count by source
                source = indicator.source.value
                stats["indicators_by_source"][source] = stats["indicators_by_source"].get(source, 0) + 1
                
                # Count by level
                level = indicator.threat_level.value
                stats["indicators_by_level"][level] = stats["indicators_by_level"].get(level, 0) + 1
            
            return stats

# Global threat intelligence engine instance
_threat_intel_engine = None

def get_threat_intel_engine() -> ThreatIntelligenceEngine:
    """Get the global threat intelligence engine instance."""
    global _threat_intel_engine
    if _threat_intel_engine is None:
        _threat_intel_engine = ThreatIntelligenceEngine()
    return _threat_intel_engine
