#!/usr/bin/env python3
"""
Run tests for the SBARDS Backend API.

This script runs the tests for the SBARDS Backend API.
"""

import os
import sys
import argparse
import pytest
import dotenv

# Load environment variables from .env file if it exists
dotenv.load_dotenv()

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="SBARDS Backend API Tests",
        usage="%(prog)s [options]"
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--coverage", "-c", action="store_true", help="Run tests with coverage")
    parser.add_argument("--html-report", action="store_true", help="Generate HTML report")
    parser.add_argument("--test-path", default="tests/", help="Path to test directory or file")

    return parser.parse_args()

def main():
    """Run the tests."""
    args = parse_arguments()

    print("Running SBARDS Backend API tests...")

    # Set up test arguments
    test_args = []

    # Add verbosity
    if args.verbose:
        test_args.append("-v")
    else:
        test_args.append("-q")

    # Add coverage
    if args.coverage:
        test_args.extend(["--cov=app", "--cov-report=term"])

        # Add HTML report
        if args.html_report:
            test_args.append("--cov-report=html")

    # Add test path
    test_args.append(args.test_path)

    # Run the tests
    result = pytest.main(test_args)

    # Print summary
    if result == 0:
        print("\nAll tests passed!")
    else:
        print(f"\nTests failed with exit code: {result}")

    return result

if __name__ == "__main__":
    sys.exit(main())
