"""
Enhanced Utility Functions for SBARDS New Architecture

This module provides comprehensive utility functions for the multi-layer SBARDS project.
Includes file operations, system utilities, security helpers, and performance monitoring.
"""

import os
import platform
import sys
import hashlib
import json
import time
import subprocess
import threading
import math
from datetime import datetime, <PERSON><PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from .constants import (
    SUPPORTED_HASH_ALGORITHMS, MAGIC_NUMBERS, MAX_FILE_SIZE,
    DANGEROUS_EXTENSIONS, EXECUTABLE_EXTENSIONS, ThreatLevel,
    ENCRYPTION_ALGORITHMS, MEMORY_ENCRYPTION_KEYS, COLD_BOOT_PROTECTION
)

class FileUtils:
    """Utility class for file operations."""

    @staticmethod
    def file_exists(file_path: str) -> bool:
        """
        Check if file exists.

        Args:
            file_path (str): Path to the file

        Returns:
            bool: True if file exists
        """
        try:
            return os.path.isfile(file_path)
        except Exception:
            return False

    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        Get file size in bytes.

        Args:
            file_path (str): Path to the file

        Returns:
            int: File size in bytes, 0 if error
        """
        try:
            return os.path.getsize(file_path)
        except Exception:
            return 0

    @staticmethod
    def get_file_hash(file_path: str, algorithm: str = "sha256") -> Optional[str]:
        """
        Calculate the hash of a file with enhanced error handling.

        Args:
            file_path (str): Path to the file
            algorithm (str): Hash algorithm (md5, sha1, sha256, sha512, ssdeep)

        Returns:
            Optional[str]: File hash or None if error
        """
        if not os.path.isfile(file_path):
            return None

        if algorithm not in SUPPORTED_HASH_ALGORITHMS:
            algorithm = "sha256"

        try:
            # Handle SSDEEP separately (requires pydeep)
            if algorithm == "ssdeep":
                try:
                    import ssdeep
                    return ssdeep.hash_from_file(file_path)
                except ImportError:
                    return None

            # Standard hash algorithms
            hasher = getattr(hashlib, algorithm)()

            with open(file_path, "rb") as f:
                # Read in larger chunks for better performance
                for chunk in iter(lambda: f.read(65536), b""):
                    hasher.update(chunk)

            return hasher.hexdigest()
        except Exception as e:
            print(f"Error calculating {algorithm} hash for {file_path}: {e}")
            return None

    @staticmethod
    def get_multiple_hashes(file_path: str, algorithms: List[str] = None) -> Dict[str, str]:
        """
        Calculate multiple hashes for a file efficiently.

        Args:
            file_path (str): Path to the file
            algorithms (List[str]): List of hash algorithms

        Returns:
            Dict[str, str]: Dictionary of algorithm -> hash
        """
        if algorithms is None:
            algorithms = ["sha256", "md5"]

        if not os.path.isfile(file_path):
            return {}

        hashers = {}
        for algo in algorithms:
            if algo in SUPPORTED_HASH_ALGORITHMS and algo != "ssdeep":
                hashers[algo] = getattr(hashlib, algo)()

        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(65536), b""):
                    for hasher in hashers.values():
                        hasher.update(chunk)

            results = {algo: hasher.hexdigest() for algo, hasher in hashers.items()}

            # Handle SSDEEP separately
            if "ssdeep" in algorithms:
                ssdeep_hash = FileUtils.get_file_hash(file_path, "ssdeep")
                if ssdeep_hash:
                    results["ssdeep"] = ssdeep_hash

            return results
        except Exception as e:
            print(f"Error calculating multiple hashes for {file_path}: {e}")
            return {}

    @staticmethod
    def get_file_signature(file_path: str) -> Optional[str]:
        """
        Get file signature based on magic numbers.

        Args:
            file_path (str): Path to the file

        Returns:
            Optional[str]: File type based on signature
        """
        try:
            with open(file_path, "rb") as f:
                header = f.read(16)  # Read first 16 bytes

            for file_type, magic in MAGIC_NUMBERS.items():
                if header.startswith(magic):
                    return file_type

            return "UNKNOWN"
        except Exception:
            return None

    @staticmethod
    def is_dangerous_file(file_path: str) -> bool:
        """
        Check if file is potentially dangerous based on extension.

        Args:
            file_path (str): Path to the file

        Returns:
            bool: True if potentially dangerous
        """
        extension = os.path.splitext(file_path)[1].lower()
        return extension in DANGEROUS_EXTENSIONS

    @staticmethod
    def get_file_entropy(file_path: str) -> float:
        """
        Calculate Shannon entropy of a file.

        Args:
            file_path (str): Path to the file

        Returns:
            float: Entropy value (0-8)
        """
        try:
            with open(file_path, "rb") as f:
                data = f.read()

            if not data:
                return 0.0

            # Count byte frequencies
            byte_counts = [0] * 256
            for byte in data:
                byte_counts[byte] += 1

            # Calculate entropy
            entropy = 0.0
            data_len = len(data)

            for count in byte_counts:
                if count > 0:
                    probability = count / data_len
                    entropy -= probability * math.log2(probability)

            return entropy
        except Exception:
            return 0.0

class SystemUtils:
    """Utility class for system operations."""

    @staticmethod
    def get_cpu_count() -> int:
        """
        Get number of CPU cores.

        Returns:
            int: Number of CPU cores
        """
        try:
            import multiprocessing
            return multiprocessing.cpu_count()
        except Exception:
            return 1

    @staticmethod
    def get_platform_info() -> Dict[str, str]:
        """
        Get comprehensive platform information.

        Returns:
            Dict[str, str]: Platform information
        """
        return {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "architecture": platform.architecture()[0],
            "python_version": platform.python_version(),
            "is_windows": platform.system() == "Windows",
            "is_linux": platform.system() == "Linux",
            "is_macos": platform.system() == "Darwin"
        }

    @staticmethod
    def get_system_resources() -> Dict[str, Any]:
        """
        Get current system resource usage.

        Returns:
            Dict[str, Any]: System resource information
        """
        try:
            import psutil

            # CPU information
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()

            # Memory information
            memory = psutil.virtual_memory()

            # Disk information
            disk = psutil.disk_usage('/')

            return {
                "cpu": {
                    "percent": cpu_percent,
                    "count": cpu_count,
                    "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used,
                    "free": memory.free
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                }
            }
        except ImportError:
            return {"error": "psutil not installed"}
        except Exception as e:
            return {"error": str(e)}

    @staticmethod
    def check_admin_privileges() -> bool:
        """
        Check if running with administrator/root privileges.

        Returns:
            bool: True if running as admin/root
        """
        try:
            if platform.system() == "Windows":
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except Exception:
            return False

    @staticmethod
    def run_command(command: List[str], timeout: int = 30) -> Tuple[int, str, str]:
        """
        Run a system command with timeout.

        Args:
            command (List[str]): Command and arguments
            timeout (int): Timeout in seconds

        Returns:
            Tuple[int, str, str]: (return_code, stdout, stderr)
        """
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "Command timed out"
        except Exception as e:
            return -1, "", str(e)

class SecurityUtils:
    """Utility class for security operations."""

    @staticmethod
    def assess_threat_level(file_path: str, yara_matches: List[str], entropy: float) -> ThreatLevel:
        """
        Assess threat level based on multiple factors.

        Args:
            file_path (str): Path to the file
            yara_matches (List[str]): List of YARA rule matches
            entropy (float): File entropy value

        Returns:
            ThreatLevel: Assessed threat level
        """
        score = 0

        # Check file extension
        if FileUtils.is_dangerous_file(file_path):
            score += 2

        # Check YARA matches
        if yara_matches:
            score += len(yara_matches)

            # High-priority rules
            high_priority_keywords = ["ransomware", "trojan", "backdoor", "keylogger"]
            for match in yara_matches:
                if any(keyword in match.lower() for keyword in high_priority_keywords):
                    score += 3

        # Check entropy
        if entropy > 7.5:
            score += 3
        elif entropy > 7.0:
            score += 2
        elif entropy > 6.5:
            score += 1

        # Determine threat level
        if score >= 8:
            return ThreatLevel.CRITICAL
        elif score >= 5:
            return ThreatLevel.HIGH
        elif score >= 3:
            return ThreatLevel.MEDIUM
        elif score >= 1:
            return ThreatLevel.LOW
        else:
            return ThreatLevel.SAFE

    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        Sanitize filename for safe storage.

        Args:
            filename (str): Original filename

        Returns:
            str: Sanitized filename
        """
        # Remove dangerous characters
        dangerous_chars = '<>:"/\\|?*'
        for char in dangerous_chars:
            filename = filename.replace(char, '_')

        # Limit length
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext

        return filename

    @staticmethod
    def generate_quarantine_name(file_path: str) -> str:
        """
        Generate a safe name for quarantined files.

        Args:
            file_path (str): Original file path

        Returns:
            str: Quarantine filename
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_hash = FileUtils.get_file_hash(file_path, "sha256")
        original_name = os.path.basename(file_path)

        if file_hash:
            return f"{timestamp}_{file_hash[:16]}_{SecurityUtils.sanitize_filename(original_name)}"
        else:
            return f"{timestamp}_{SecurityUtils.sanitize_filename(original_name)}"

class PerformanceUtils:
    """Utility class for performance monitoring and optimization."""

    @staticmethod
    def measure_time(func: Callable) -> Callable:
        """
        Decorator to measure function execution time.

        Args:
            func (Callable): Function to measure

        Returns:
            Callable: Wrapped function that returns (result, duration)
        """
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            return result, duration
        return wrapper

    @staticmethod
    def time_function(func: Callable) -> Callable:
        """
        Decorator to time function execution.

        Args:
            func (Callable): Function to time

        Returns:
            Callable: Wrapped function
        """
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            print(f"{func.__name__} executed in {execution_time:.4f} seconds")
            return result
        return wrapper

    @staticmethod
    def format_bytes(bytes_value: int) -> str:
        """
        Format bytes to human-readable string.

        Args:
            bytes_value (int): Number of bytes

        Returns:
            str: Formatted string
        """
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} PB"

    @staticmethod
    def format_time(seconds: float) -> str:
        """
        Format time in seconds to human-readable string.

        Args:
            seconds (float): Time in seconds

        Returns:
            str: Formatted time string
        """
        if seconds < 1:
            return f"{seconds*1000:.2f} ms"
        elif seconds < 60:
            return f"{seconds:.2f} seconds"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.2f} minutes"
        else:
            hours = seconds / 3600
            return f"{hours:.2f} hours"

class DataUtils:
    """Utility class for data operations."""

    @staticmethod
    def save_json(data: Any, file_path: str, indent: int = 4) -> bool:
        """
        Save data to JSON file with enhanced error handling.

        Args:
            data (Any): Data to save
            file_path (str): Path to the file
            indent (int): JSON indentation

        Returns:
            bool: True if successful
        """
        try:
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=indent, ensure_ascii=False, default=str)
            return True
        except Exception as e:
            print(f"Error saving JSON to {file_path}: {e}")
            return False

    @staticmethod
    def load_json(file_path: str) -> Optional[Any]:
        """
        Load data from JSON file with enhanced error handling.

        Args:
            file_path (str): Path to the file

        Returns:
            Optional[Any]: Loaded data or None
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"JSON file not found: {file_path}")
            return None
        except json.JSONDecodeError as e:
            print(f"Invalid JSON in {file_path}: {e}")
            return None
        except Exception as e:
            print(f"Error loading JSON from {file_path}: {e}")
            return None

    @staticmethod
    def merge_dictionaries(dict1: Dict, dict2: Dict) -> Dict:
        """
        Recursively merge two dictionaries.

        Args:
            dict1 (Dict): First dictionary
            dict2 (Dict): Second dictionary

        Returns:
            Dict: Merged dictionary
        """
        result = dict1.copy()

        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = DataUtils.merge_dictionaries(result[key], value)
            else:
                result[key] = value

        return result

# Convenience functions for backward compatibility
def get_platform() -> str:
    """Get the current platform."""
    return platform.system()

def is_windows() -> bool:
    """Check if running on Windows."""
    return platform.system() == "Windows"

def is_linux() -> bool:
    """Check if running on Linux."""
    return platform.system() == "Linux"

def is_macos() -> bool:
    """Check if running on macOS."""
    return platform.system() == "Darwin"

def get_file_hash(file_path: str, algorithm: str = "sha256") -> Optional[str]:
    """Calculate file hash (convenience function)."""
    return FileUtils.get_file_hash(file_path, algorithm)

def get_file_info(file_path: str) -> Dict[str, Any]:
    """Get comprehensive file information."""
    if not os.path.exists(file_path):
        return {"error": "File not found"}

    try:
        stat_info = os.stat(file_path)
        file_info = {
            "path": file_path,
            "size": stat_info.st_size,
            "created": stat_info.st_ctime,
            "modified": stat_info.st_mtime,
            "accessed": stat_info.st_atime,
            "is_file": os.path.isfile(file_path),
            "is_dir": os.path.isdir(file_path),
            "extension": os.path.splitext(file_path)[1].lower() if os.path.isfile(file_path) else "",
            "is_dangerous": FileUtils.is_dangerous_file(file_path),
            "signature": FileUtils.get_file_signature(file_path),
            "entropy": FileUtils.get_file_entropy(file_path) if os.path.isfile(file_path) else 0.0
        }

        # Add hashes for files under size limit
        if os.path.isfile(file_path) and stat_info.st_size < MAX_FILE_SIZE:
            file_info["hashes"] = FileUtils.get_multiple_hashes(file_path)

        return file_info
    except Exception as e:
        return {"error": str(e)}

class MemoryUtils:
    """Utility class for memory protection and encryption operations."""

    @staticmethod
    def generate_encryption_key(algorithm: str = "AES-256") -> Optional[bytes]:
        """
        Generate encryption key for memory protection.

        Args:
            algorithm (str): Encryption algorithm

        Returns:
            Optional[bytes]: Generated key or None if error
        """
        try:
            from cryptography.hazmat.primitives import hashes
            from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
            from cryptography.hazmat.backends import default_backend
            import secrets

            if algorithm == "AES-256":
                key_size = MEMORY_ENCRYPTION_KEYS["aes_256_key_size"]
            elif algorithm == "ChaCha20":
                key_size = MEMORY_ENCRYPTION_KEYS["chacha20_key_size"]
            else:
                key_size = 32  # Default

            # Generate random key
            key = secrets.token_bytes(key_size)
            return key

        except ImportError:
            print("Cryptography library not available")
            return None
        except Exception as e:
            print(f"Error generating encryption key: {e}")
            return None

    @staticmethod
    def encrypt_memory_data(data: bytes, key: bytes, algorithm: str = "AES-256") -> Optional[bytes]:
        """
        Encrypt data for memory protection.

        Args:
            data (bytes): Data to encrypt
            key (bytes): Encryption key
            algorithm (str): Encryption algorithm

        Returns:
            Optional[bytes]: Encrypted data or None if error
        """
        try:
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.backends import default_backend
            import secrets

            if algorithm == "AES-256":
                # Generate random IV
                iv = secrets.token_bytes(16)
                cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())

                # Pad data to block size
                block_size = 16
                padding_length = block_size - (len(data) % block_size)
                padded_data = data + bytes([padding_length] * padding_length)

                encryptor = cipher.encryptor()
                encrypted_data = encryptor.update(padded_data) + encryptor.finalize()

                return iv + encrypted_data

            elif algorithm == "ChaCha20":
                from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

                nonce = secrets.token_bytes(MEMORY_ENCRYPTION_KEYS["nonce_size"])
                cipher = Cipher(algorithms.ChaCha20(key, nonce), None, backend=default_backend())

                encryptor = cipher.encryptor()
                encrypted_data = encryptor.update(data) + encryptor.finalize()

                return nonce + encrypted_data

        except ImportError:
            print("Cryptography library not available")
            return None
        except Exception as e:
            print(f"Error encrypting memory data: {e}")
            return None

    @staticmethod
    def decrypt_memory_data(encrypted_data: bytes, key: bytes, algorithm: str = "AES-256") -> Optional[bytes]:
        """
        Decrypt memory data.

        Args:
            encrypted_data (bytes): Encrypted data
            key (bytes): Decryption key
            algorithm (str): Encryption algorithm

        Returns:
            Optional[bytes]: Decrypted data or None if error
        """
        try:
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.backends import default_backend

            if algorithm == "AES-256":
                iv = encrypted_data[:16]
                ciphertext = encrypted_data[16:]

                cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
                decryptor = cipher.decryptor()
                padded_data = decryptor.update(ciphertext) + decryptor.finalize()

                # Remove padding
                padding_length = padded_data[-1]
                data = padded_data[:-padding_length]

                return data

            elif algorithm == "ChaCha20":
                nonce = encrypted_data[:MEMORY_ENCRYPTION_KEYS["nonce_size"]]
                ciphertext = encrypted_data[MEMORY_ENCRYPTION_KEYS["nonce_size"]:]

                cipher = Cipher(algorithms.ChaCha20(key, nonce), None, backend=default_backend())
                decryptor = cipher.decryptor()
                data = decryptor.update(ciphertext) + decryptor.finalize()

                return data

        except ImportError:
            print("Cryptography library not available")
            return None
        except Exception as e:
            print(f"Error decrypting memory data: {e}")
            return None

    @staticmethod
    def secure_memory_wipe(memory_region: bytearray, patterns: List[int] = None) -> bool:
        """
        Securely wipe memory region using multiple patterns.

        Args:
            memory_region (bytearray): Memory region to wipe
            patterns (List[int]): Wipe patterns to use

        Returns:
            bool: True if successful
        """
        try:
            if patterns is None:
                patterns = COLD_BOOT_PROTECTION["memory_wipe_patterns"]

            # Apply multiple wipe patterns
            for pattern in patterns:
                for i in range(len(memory_region)):
                    memory_region[i] = pattern

            # Final random pattern
            import secrets
            for i in range(len(memory_region)):
                memory_region[i] = secrets.randbits(8)

            return True

        except Exception as e:
            print(f"Error wiping memory: {e}")
            return False

    @staticmethod
    def scramble_key(key: bytes) -> bytes:
        """
        Scramble encryption key for cold boot protection.

        Args:
            key (bytes): Original key

        Returns:
            bytes: Scrambled key
        """
        try:
            import secrets

            # XOR with random data
            scrambled = bytearray(key)
            for i in range(len(scrambled)):
                scrambled[i] ^= secrets.randbits(8)

            return bytes(scrambled)

        except Exception as e:
            print(f"Error scrambling key: {e}")
            return key

class BlockchainUtils:
    """Utility class for blockchain integration operations."""

    @staticmethod
    def generate_hash_for_blockchain(file_hash: str, metadata: Dict[str, Any]) -> str:
        """
        Generate a hash suitable for blockchain storage.

        Args:
            file_hash (str): Original file hash
            metadata (Dict[str, Any]): Additional metadata

        Returns:
            str: Blockchain-ready hash
        """
        try:
            import hashlib
            import json

            # Combine file hash with metadata
            combined_data = {
                "file_hash": file_hash,
                "metadata": metadata,
                "timestamp": datetime.now().isoformat()
            }

            # Create deterministic hash
            json_str = json.dumps(combined_data, sort_keys=True)
            blockchain_hash = hashlib.sha256(json_str.encode()).hexdigest()

            return blockchain_hash

        except Exception as e:
            print(f"Error generating blockchain hash: {e}")
            return file_hash

    @staticmethod
    def verify_blockchain_integrity(stored_hash: str, file_hash: str, metadata: Dict[str, Any]) -> bool:
        """
        Verify integrity of blockchain-stored hash.

        Args:
            stored_hash (str): Hash stored in blockchain
            file_hash (str): Current file hash
            metadata (Dict[str, Any]): Metadata

        Returns:
            bool: True if integrity verified
        """
        try:
            # Regenerate hash and compare
            regenerated_hash = BlockchainUtils.generate_hash_for_blockchain(file_hash, metadata)
            return stored_hash == regenerated_hash

        except Exception as e:
            print(f"Error verifying blockchain integrity: {e}")
            return False
