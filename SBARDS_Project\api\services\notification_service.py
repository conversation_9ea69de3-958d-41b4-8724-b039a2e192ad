"""
SBARDS Advanced Notification Service
Real-time notification system with multiple channels and alert rules
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Setup logger
logger = logging.getLogger("notification_service")

class NotificationLevel(Enum):
    """Notification severity levels"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class NotificationChannel(Enum):
    """Notification delivery channels"""
    WEBSOCKET = "websocket"
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    DATABASE = "database"

class NotificationRule:
    """Notification rule definition"""
    def __init__(self,
                 rule_id: str,
                 name: str,
                 condition: str,
                 level: NotificationLevel,
                 channels: List[NotificationChannel],
                 enabled: bool = True,
                 cooldown_minutes: int = 5):
        self.rule_id = rule_id
        self.name = name
        self.condition = condition
        self.level = level
        self.channels = channels
        self.enabled = enabled
        self.cooldown_minutes = cooldown_minutes
        self.last_triggered = None

class Notification:
    """Notification message"""
    def __init__(self,
                 title: str,
                 message: str,
                 level: NotificationLevel,
                 source: str = "system",
                 data: Optional[Dict] = None):
        self.id = f"notif_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        self.title = title
        self.message = message
        self.level = level
        self.source = source
        self.data = data or {}
        self.timestamp = datetime.now()
        self.delivered_channels = []
        self.failed_channels = []

class NotificationService:
    """Advanced notification service with multiple channels and rules"""

    def __init__(self):
        self.rules: Dict[str, NotificationRule] = {}
        self.notification_history: List[Notification] = []
        self.websocket_manager = None
        self.email_config = {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "enabled": False
        }
        self.sms_config = {
            "api_key": "",
            "api_url": "",
            "enabled": False
        }
        self.max_history = 1000
        self.setup_default_rules()

    def setup_default_rules(self):
        """Setup default notification rules"""
        # High CPU usage rule
        self.add_rule(NotificationRule(
            rule_id="high_cpu",
            name="High CPU Usage",
            condition="system_cpu_usage > 80",
            level=NotificationLevel.WARNING,
            channels=[NotificationChannel.WEBSOCKET],
            cooldown_minutes=10
        ))

        # High memory usage rule
        self.add_rule(NotificationRule(
            rule_id="high_memory",
            name="High Memory Usage",
            condition="system_memory_usage > 85",
            level=NotificationLevel.WARNING,
            channels=[NotificationChannel.WEBSOCKET],
            cooldown_minutes=10
        ))

        # Threat detected rule
        self.add_rule(NotificationRule(
            rule_id="threat_detected",
            name="Threat Detected",
            condition="metrics_threats_detected > 0",
            level=NotificationLevel.CRITICAL,
            channels=[NotificationChannel.WEBSOCKET],
            cooldown_minutes=1
        ))

        # Capture layer down rule
        self.add_rule(NotificationRule(
            rule_id="capture_down",
            name="Capture Layer Down",
            condition="capture_running == False",
            level=NotificationLevel.ERROR,
            channels=[NotificationChannel.WEBSOCKET],
            cooldown_minutes=5
        ))

        # System health critical rule
        self.add_rule(NotificationRule(
            rule_id="system_critical",
            name="System Health Critical",
            condition="system_health == 'critical'",
            level=NotificationLevel.CRITICAL,
            channels=[NotificationChannel.WEBSOCKET],
            cooldown_minutes=2
        ))

    def set_websocket_manager(self, websocket_manager):
        """Set WebSocket manager for real-time notifications"""
        self.websocket_manager = websocket_manager

    def configure_email(self, smtp_server: str, smtp_port: int, username: str, password: str):
        """Configure email notification settings"""
        self.email_config.update({
            "smtp_server": smtp_server,
            "smtp_port": smtp_port,
            "username": username,
            "password": password,
            "enabled": True
        })
        logger.info("Email notifications configured")

    def configure_sms(self, api_key: str, api_url: str):
        """Configure SMS notification settings"""
        self.sms_config.update({
            "api_key": api_key,
            "api_url": api_url,
            "enabled": True
        })
        logger.info("SMS notifications configured")

    def add_rule(self, rule: NotificationRule):
        """Add notification rule"""
        self.rules[rule.rule_id] = rule
        logger.info(f"Added notification rule: {rule.name}")

    def remove_rule(self, rule_id: str):
        """Remove notification rule"""
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"Removed notification rule: {rule_id}")

    def enable_rule(self, rule_id: str):
        """Enable notification rule"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = True
            logger.info(f"Enabled notification rule: {rule_id}")

    def disable_rule(self, rule_id: str):
        """Disable notification rule"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = False
            logger.info(f"Disabled notification rule: {rule_id}")

    async def check_rules(self, system_data: Dict[str, Any]):
        """Check all rules against system data and trigger notifications"""
        try:
            for rule in self.rules.values():
                if not rule.enabled:
                    continue

                # Check cooldown
                if rule.last_triggered:
                    time_since_last = datetime.now() - rule.last_triggered
                    if time_since_last < timedelta(minutes=rule.cooldown_minutes):
                        continue

                # Evaluate condition
                if self._evaluate_condition(rule.condition, system_data):
                    # Create notification
                    notification = Notification(
                        title=rule.name,
                        message=f"Alert: {rule.name} triggered",
                        level=rule.level,
                        source="rule_engine",
                        data={"rule_id": rule.rule_id, "system_data": system_data}
                    )

                    # Send notification
                    await self.send_notification(notification, rule.channels)

                    # Update last triggered time
                    rule.last_triggered = datetime.now()

        except Exception as e:
            logger.error(f"Error checking notification rules: {e}")

    def _evaluate_condition(self, condition: str, data: Dict[str, Any]) -> bool:
        """Evaluate notification condition against system data"""
        try:
            # Flatten data for easier access
            flat_data = self._flatten_dict(data)

            # Create safe evaluation context
            safe_context = {}
            for key, value in flat_data.items():
                # Convert to safe variable names
                safe_key = key.replace('-', '_').replace(' ', '_')
                safe_context[safe_key] = value

            # Replace condition with safe variable names
            safe_condition = condition
            for key, value in flat_data.items():
                safe_key = key.replace('-', '_').replace(' ', '_')
                safe_condition = safe_condition.replace(key, safe_key)

            # Evaluate the condition safely with restricted context
            allowed_names = {
                "__builtins__": {},
                **safe_context
            }

            return eval(safe_condition, allowed_names)

        except Exception as e:
            logger.warning(f"Could not evaluate condition '{condition}': {e}")
            return False

    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_') -> Dict:
        """Flatten nested dictionary"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)

    async def send_notification(self, notification: Notification, channels: List[NotificationChannel] = None):
        """Send notification through specified channels"""
        if channels is None:
            channels = [NotificationChannel.WEBSOCKET]

        for channel in channels:
            try:
                if channel == NotificationChannel.WEBSOCKET:
                    await self._send_websocket(notification)
                elif channel == NotificationChannel.EMAIL:
                    await self._send_email(notification)
                elif channel == NotificationChannel.SMS:
                    await self._send_sms(notification)
                elif channel == NotificationChannel.DATABASE:
                    await self._save_to_database(notification)

                notification.delivered_channels.append(channel)

            except Exception as e:
                logger.error(f"Failed to send notification via {channel.value}: {e}")
                notification.failed_channels.append(channel)

        # Add to history
        self.notification_history.append(notification)

        # Limit history size
        if len(self.notification_history) > self.max_history:
            self.notification_history = self.notification_history[-self.max_history:]

        logger.info(f"Notification sent: {notification.title} via {[c.value for c in notification.delivered_channels]}")

    async def _send_websocket(self, notification: Notification):
        """Send notification via WebSocket"""
        if self.websocket_manager and self.websocket_manager.active_connections:
            message = {
                "type": "notification",
                "id": notification.id,
                "title": notification.title,
                "message": notification.message,
                "level": notification.level.value,
                "source": notification.source,
                "timestamp": notification.timestamp.isoformat(),
                "data": notification.data
            }
            await self.websocket_manager.broadcast_json(message)

    async def _send_email(self, notification: Notification):
        """Send notification via email"""
        if not self.email_config["enabled"]:
            raise Exception("Email notifications not configured")

        # Create email message
        msg = MIMEMultipart()
        msg['From'] = self.email_config["username"]
        msg['To'] = "<EMAIL>"  # Configure recipient
        msg['Subject'] = f"SBARDS Alert: {notification.title}"

        body = f"""
        SBARDS Security Alert

        Title: {notification.title}
        Level: {notification.level.value.upper()}
        Message: {notification.message}
        Source: {notification.source}
        Time: {notification.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

        Additional Data:
        {json.dumps(notification.data, indent=2)}

        ---
        SBARDS Security System
        """

        msg.attach(MIMEText(body, 'plain'))

        # Send email
        server = smtplib.SMTP(self.email_config["smtp_server"], self.email_config["smtp_port"])
        server.starttls()
        server.login(self.email_config["username"], self.email_config["password"])
        text = msg.as_string()
        server.sendmail(self.email_config["username"], "<EMAIL>", text)
        server.quit()

    async def _send_sms(self, notification: Notification):
        """Send notification via SMS"""
        if not self.sms_config["enabled"]:
            raise Exception("SMS notifications not configured")

        # Implement SMS sending logic here
        # This would typically use a service like Twilio, AWS SNS, etc.
        logger.info(f"SMS notification would be sent: {notification.title}")

    async def _save_to_database(self, notification: Notification):
        """Save notification to database"""
        # Implement database saving logic here
        logger.info(f"Notification saved to database: {notification.title}")

    def get_notification_history(self, limit: int = 50) -> List[Dict]:
        """Get notification history"""
        recent_notifications = self.notification_history[-limit:]
        return [
            {
                "id": n.id,
                "title": n.title,
                "message": n.message,
                "level": n.level.value,
                "source": n.source,
                "timestamp": n.timestamp.isoformat(),
                "delivered_channels": [c.value for c in n.delivered_channels],
                "failed_channels": [c.value for c in n.failed_channels]
            }
            for n in recent_notifications
        ]

    def get_rules_status(self) -> List[Dict]:
        """Get status of all notification rules"""
        return [
            {
                "rule_id": rule.rule_id,
                "name": rule.name,
                "condition": rule.condition,
                "level": rule.level.value,
                "channels": [c.value for c in rule.channels],
                "enabled": rule.enabled,
                "cooldown_minutes": rule.cooldown_minutes,
                "last_triggered": rule.last_triggered.isoformat() if rule.last_triggered else None
            }
            for rule in self.rules.values()
        ]

# Global notification service instance
notification_service = NotificationService()
