"""
System Router for SBARDS API

This module provides the system router for the SBARDS API.
"""

import os
import platform
import logging
import psutil
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# Create router
router = APIRouter()

# Models
class SystemInfo(BaseModel):
    """System information model."""
    platform: str
    platform_release: str
    platform_version: str
    architecture: str
    hostname: str
    processor: str
    cpu_count: int
    memory_total: float
    memory_available: float
    disk_usage: Dict[str, Any]

class ProcessInfo(BaseModel):
    """Process information model."""
    pid: int
    name: str
    username: str
    cpu_percent: float
    memory_percent: float
    status: str
    create_time: float

class SystemStatus(BaseModel):
    """System status model."""
    status: str
    uptime: float
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_processes: int
    network_connections: int

# Endpoints
@router.get("/info", response_model=SystemInfo)
async def get_system_info():
    """Get system information."""
    try:
        # Get disk usage for all drives
        disk_usage = {}
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage[partition.mountpoint] = {
                    "total": usage.total / (1024 * 1024 * 1024),  # GB
                    "used": usage.used / (1024 * 1024 * 1024),  # GB
                    "free": usage.free / (1024 * 1024 * 1024),  # GB
                    "percent": usage.percent
                }
            except Exception as e:
                logging.warning(f"Error getting disk usage for {partition.mountpoint}: {e}")
        
        # Get system information
        return SystemInfo(
            platform=platform.system(),
            platform_release=platform.release(),
            platform_version=platform.version(),
            architecture=platform.machine(),
            hostname=platform.node(),
            processor=platform.processor(),
            cpu_count=psutil.cpu_count(),
            memory_total=psutil.virtual_memory().total / (1024 * 1024 * 1024),  # GB
            memory_available=psutil.virtual_memory().available / (1024 * 1024 * 1024),  # GB
            disk_usage=disk_usage
        )
    except Exception as e:
        logging.error(f"Error getting system information: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system information: {str(e)}")

@router.get("/status", response_model=SystemStatus)
async def get_system_status():
    """Get system status."""
    try:
        # Get system metrics
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Get primary disk usage
        disk = psutil.disk_usage('/')
        if platform.system() == "Windows":
            disk = psutil.disk_usage('C:\\')
        
        # Get process and connection counts
        active_processes = len(psutil.pids())
        network_connections = len(psutil.net_connections())
        
        return SystemStatus(
            status="healthy",
            uptime=psutil.boot_time(),
            cpu_usage=cpu_usage,
            memory_usage=memory.percent,
            disk_usage=disk.percent,
            active_processes=active_processes,
            network_connections=network_connections
        )
    except Exception as e:
        logging.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system status: {str(e)}")

@router.get("/processes", response_model=List[ProcessInfo])
async def get_processes(limit: int = 20):
    """Get current processes."""
    try:
        # Get processes
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'status', 'create_time']):
            try:
                processes.append(ProcessInfo(
                    pid=proc.info['pid'],
                    name=proc.info['name'],
                    username=proc.info['username'] or "",
                    cpu_percent=proc.info['cpu_percent'] or 0.0,
                    memory_percent=proc.info['memory_percent'] or 0.0,
                    status=proc.info['status'] or "",
                    create_time=proc.info['create_time'] or 0.0
                ))
            except Exception as e:
                logging.warning(f"Error getting process info for PID {proc.info.get('pid')}: {e}")
        
        # Sort by CPU usage and limit
        processes.sort(key=lambda x: x.cpu_percent, reverse=True)
        return processes[:limit]
    except Exception as e:
        logging.error(f"Error getting processes: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting processes: {str(e)}")

@router.get("/health")
async def get_system_health():
    """Get system health status."""
    try:
        # Check system health
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Determine health status
        health_status = "healthy"
        if cpu_usage > 90 or memory.percent > 90:
            health_status = "warning"
        if cpu_usage > 95 or memory.percent > 95:
            health_status = "critical"
        
        return {
            "status": health_status,
            "service": "system",
            "version": "2.0.0",
            "metrics": {
                "cpu_usage": cpu_usage,
                "memory_usage": memory.percent,
                "available_memory": memory.available / (1024 * 1024 * 1024)  # GB
            },
            "capabilities": [
                "system_monitoring",
                "process_tracking",
                "resource_monitoring",
                "health_checking"
            ]
        }
    except Exception as e:
        logging.error(f"Error getting system health: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system health: {str(e)}")

@router.get("/metrics")
async def get_system_metrics():
    """Get detailed system metrics."""
    try:
        # Get CPU metrics
        cpu_times = psutil.cpu_times()
        cpu_stats = psutil.cpu_stats()
        
        # Get memory metrics
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Get disk I/O metrics
        disk_io = psutil.disk_io_counters()
        
        # Get network I/O metrics
        network_io = psutil.net_io_counters()
        
        return {
            "cpu": {
                "usage_percent": psutil.cpu_percent(interval=1),
                "user_time": cpu_times.user,
                "system_time": cpu_times.system,
                "idle_time": cpu_times.idle,
                "interrupts": cpu_stats.interrupts,
                "soft_interrupts": cpu_stats.soft_interrupts,
                "syscalls": cpu_stats.syscalls
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used,
                "free": memory.free,
                "buffers": getattr(memory, 'buffers', 0),
                "cached": getattr(memory, 'cached', 0)
            },
            "swap": {
                "total": swap.total,
                "used": swap.used,
                "free": swap.free,
                "percent": swap.percent
            },
            "disk_io": {
                "read_count": disk_io.read_count if disk_io else 0,
                "write_count": disk_io.write_count if disk_io else 0,
                "read_bytes": disk_io.read_bytes if disk_io else 0,
                "write_bytes": disk_io.write_bytes if disk_io else 0
            },
            "network_io": {
                "bytes_sent": network_io.bytes_sent if network_io else 0,
                "bytes_recv": network_io.bytes_recv if network_io else 0,
                "packets_sent": network_io.packets_sent if network_io else 0,
                "packets_recv": network_io.packets_recv if network_io else 0
            }
        }
    except Exception as e:
        logging.error(f"Error getting system metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system metrics: {str(e)}")
