#!/usr/bin/env python3
"""
SBARDS System Test Report Generator

Generates comprehensive test report for all developed components.
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

def check_file_exists(file_path):
    """Check if file exists and return size."""
    if os.path.exists(file_path):
        size = os.path.getsize(file_path)
        return True, size
    return False, 0

def check_directory_structure():
    """Check project directory structure."""
    print("🏗️  Checking Project Structure...")
    
    expected_dirs = [
        "core", "capture", "static_analysis", "api", "ui", 
        "tests", "docs", "config", "data", "logs"
    ]
    
    structure_report = {}
    
    for dir_name in expected_dirs:
        if os.path.exists(dir_name):
            files = []
            for root, dirs, filenames in os.walk(dir_name):
                for filename in filenames:
                    if not filename.startswith('.') and not filename.endswith('.pyc'):
                        files.append(os.path.join(root, filename))
            
            structure_report[dir_name] = {
                "exists": True,
                "file_count": len(files),
                "files": files[:5]  # Show first 5 files
            }
            print(f"  ✅ {dir_name}: {len(files)} files")
        else:
            structure_report[dir_name] = {"exists": False, "file_count": 0}
            print(f"  ❌ {dir_name}: Missing")
    
    return structure_report

def check_core_layer():
    """Check core layer components."""
    print("\n🔧 Checking Core Layer...")
    
    core_files = [
        "core/config.py",
        "core/logger.py", 
        "core/constants.py",
        "core/utils.py",
        "core/test_core.py"
    ]
    
    core_report = {}
    
    for file_path in core_files:
        exists, size = check_file_exists(file_path)
        core_report[file_path] = {"exists": exists, "size": size}
        
        if exists:
            print(f"  ✅ {file_path}: {size} bytes")
        else:
            print(f"  ❌ {file_path}: Missing")
    
    # Test imports
    try:
        sys.path.append('.')
        from core.config import ConfigManager
        from core.logger import get_global_logger
        from core.constants import ThreatLevel, FileStatus
        from core.utils import FileUtils, SecurityUtils, PerformanceUtils
        
        core_report["imports"] = {"success": True, "error": None}
        print("  ✅ Core imports: Success")
    except Exception as e:
        core_report["imports"] = {"success": False, "error": str(e)}
        print(f"  ❌ Core imports: {e}")
    
    return core_report

def check_capture_layer():
    """Check capture layer components."""
    print("\n📥 Checking Capture Layer...")
    
    capture_files = [
        "capture/cpp/file_monitor.cpp",
        "capture/cpp/permission_manager.cpp",
        "capture/cpp/CMakeLists.txt",
        "capture/python/file_interceptor.py",
        "capture/python/redis_queue.py",
        "capture/test_capture_layer.py",
        "capture/build_capture.py"
    ]
    
    capture_report = {}
    
    for file_path in capture_files:
        exists, size = check_file_exists(file_path)
        capture_report[file_path] = {"exists": exists, "size": size}
        
        if exists:
            print(f"  ✅ {file_path}: {size} bytes")
        else:
            print(f"  ❌ {file_path}: Missing")
    
    # Test imports
    try:
        from capture.python.file_interceptor import FileInterceptor
        from capture.python.redis_queue import RedisQueueManager
        
        capture_report["imports"] = {"success": True, "error": None}
        print("  ✅ Capture imports: Success")
    except Exception as e:
        capture_report["imports"] = {"success": False, "error": str(e)}
        print(f"  ❌ Capture imports: {e}")
    
    return capture_report

def check_static_analysis_layer():
    """Check static analysis layer components."""
    print("\n🔍 Checking Static Analysis Layer...")
    
    static_files = [
        "static_analysis/cpp/signature_checker.cpp",
        "static_analysis/cpp/permission_analyzer.cpp",
        "static_analysis/cpp/entropy_checker.cpp",
        "static_analysis/cpp/hash_generator.cpp",
        "static_analysis/cpp/CMakeLists.txt",
        "static_analysis/python/yara_scanner.py",
        "static_analysis/python/virus_total.py",
        "static_analysis/python/report_generator.py",
        "static_analysis/yara_rules/index.yar",
        "static_analysis/test_static_analysis.py",
        "static_analysis/build_static_analysis.py"
    ]
    
    static_report = {}
    
    for file_path in static_files:
        exists, size = check_file_exists(file_path)
        static_report[file_path] = {"exists": exists, "size": size}
        
        if exists:
            print(f"  ✅ {file_path}: {size} bytes")
        else:
            print(f"  ❌ {file_path}: Missing")
    
    # Test imports
    try:
        from static_analysis.python.yara_scanner import YaraScanner
        from static_analysis.python.report_generator import StaticAnalysisReportGenerator
        
        static_report["imports"] = {"success": True, "error": None}
        print("  ✅ Static Analysis imports: Success")
    except Exception as e:
        static_report["imports"] = {"success": False, "error": str(e)}
        print(f"  ❌ Static Analysis imports: {e}")
    
    return static_report

def check_dependencies():
    """Check required dependencies."""
    print("\n📦 Checking Dependencies...")
    
    dependencies = [
        "fastapi", "uvicorn", "pydantic", "sqlalchemy", 
        "redis", "requests", "cryptography", "pathlib"
    ]
    
    optional_deps = ["yara", "pymongo", "docker"]
    
    dep_report = {"required": {}, "optional": {}}
    
    for dep in dependencies:
        try:
            __import__(dep)
            dep_report["required"][dep] = {"available": True, "error": None}
            print(f"  ✅ {dep}: Available")
        except ImportError as e:
            dep_report["required"][dep] = {"available": False, "error": str(e)}
            print(f"  ❌ {dep}: Missing")
    
    for dep in optional_deps:
        try:
            __import__(dep)
            dep_report["optional"][dep] = {"available": True, "error": None}
            print(f"  ✅ {dep} (optional): Available")
        except ImportError as e:
            dep_report["optional"][dep] = {"available": False, "error": str(e)}
            print(f"  ⚠️  {dep} (optional): Missing")
    
    return dep_report

def check_configuration():
    """Check configuration files."""
    print("\n⚙️  Checking Configuration...")
    
    config_files = [
        "config.json",
        "requirements.txt",
        "README.md",
        "run.py"
    ]
    
    config_report = {}
    
    for file_path in config_files:
        exists, size = check_file_exists(file_path)
        config_report[file_path] = {"exists": exists, "size": size}
        
        if exists:
            print(f"  ✅ {file_path}: {size} bytes")
        else:
            print(f"  ❌ {file_path}: Missing")
    
    return config_report

def generate_summary_report(all_reports):
    """Generate summary report."""
    print("\n" + "="*60)
    print("📊 SBARDS SYSTEM TEST REPORT SUMMARY")
    print("="*60)
    
    # Count totals
    total_files = 0
    existing_files = 0
    total_size = 0
    
    for layer_name, layer_report in all_reports.items():
        if layer_name == "dependencies":
            continue
            
        for file_path, file_info in layer_report.items():
            if isinstance(file_info, dict) and "exists" in file_info:
                total_files += 1
                if file_info["exists"]:
                    existing_files += 1
                    total_size += file_info.get("size", 0)
    
    print(f"📁 Total Files: {total_files}")
    print(f"✅ Existing Files: {existing_files}")
    print(f"❌ Missing Files: {total_files - existing_files}")
    print(f"💾 Total Size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
    
    completion_rate = (existing_files / total_files * 100) if total_files > 0 else 0
    print(f"📈 Completion Rate: {completion_rate:.1f}%")
    
    # Layer status
    print(f"\n🏗️  Layer Status:")
    
    layer_status = {
        "structure": "Project Structure",
        "core": "Core Layer", 
        "capture": "Capture Layer",
        "static_analysis": "Static Analysis Layer",
        "config": "Configuration"
    }
    
    for layer_key, layer_name in layer_status.items():
        if layer_key in all_reports:
            layer_report = all_reports[layer_key]
            layer_files = sum(1 for item in layer_report.values() 
                            if isinstance(item, dict) and item.get("exists", False))
            layer_total = sum(1 for item in layer_report.values() 
                            if isinstance(item, dict) and "exists" in item)
            
            if layer_total > 0:
                layer_completion = (layer_files / layer_total * 100)
                status = "✅" if layer_completion >= 80 else "⚠️" if layer_completion >= 50 else "❌"
                print(f"  {status} {layer_name}: {layer_completion:.1f}% ({layer_files}/{layer_total})")
    
    # Dependencies status
    if "dependencies" in all_reports:
        dep_report = all_reports["dependencies"]
        required_available = sum(1 for dep in dep_report["required"].values() if dep["available"])
        required_total = len(dep_report["required"])
        optional_available = sum(1 for dep in dep_report["optional"].values() if dep["available"])
        optional_total = len(dep_report["optional"])
        
        print(f"\n📦 Dependencies:")
        print(f"  ✅ Required: {required_available}/{required_total}")
        print(f"  ⚠️  Optional: {optional_available}/{optional_total}")
    
    # Overall assessment
    print(f"\n🎯 Overall Assessment:")
    if completion_rate >= 90:
        print("  🎉 Excellent! System is nearly complete and ready for testing.")
    elif completion_rate >= 70:
        print("  👍 Good! Most components are in place, minor work needed.")
    elif completion_rate >= 50:
        print("  ⚠️  Fair! Significant components missing, more development needed.")
    else:
        print("  ❌ Poor! Major components missing, substantial work required.")
    
    return {
        "total_files": total_files,
        "existing_files": existing_files,
        "completion_rate": completion_rate,
        "total_size": total_size,
        "timestamp": datetime.now().isoformat()
    }

def main():
    """Main test report function."""
    print("🧪 SBARDS SYSTEM COMPREHENSIVE TEST REPORT")
    print("=" * 50)
    print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🖥️  Platform: {os.name}")
    print(f"🐍 Python: {sys.version_info.major}.{sys.version_info.minor}")
    
    # Run all checks
    all_reports = {}
    
    all_reports["structure"] = check_directory_structure()
    all_reports["core"] = check_core_layer()
    all_reports["capture"] = check_capture_layer()
    all_reports["static_analysis"] = check_static_analysis_layer()
    all_reports["dependencies"] = check_dependencies()
    all_reports["config"] = check_configuration()
    
    # Generate summary
    summary = generate_summary_report(all_reports)
    
    # Save detailed report
    report_data = {
        "summary": summary,
        "detailed_reports": all_reports,
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}",
            "platform": os.name,
            "working_directory": os.getcwd()
        }
    }
    
    report_file = "test_report.json"
    with open(report_file, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\n💾 Detailed report saved to: {report_file}")
    print(f"📊 Report size: {os.path.getsize(report_file):,} bytes")
    
    return summary["completion_rate"] >= 70

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
