"""
Formatting utilities for SBARDS API

This module provides formatting functions for API responses.
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional

from core.logger import get_global_logger

# Configure logging
logger = get_global_logger().get_layer_logger("api.utils.formatters")


def format_scan_report(scan_report: Any) -> Dict[str, Any]:
    """
    Format scan report for API response.
    
    Args:
        scan_report: Database scan report object.
        
    Returns:
        dict: Formatted scan report.
    """
    try:
        formatted = {
            "id": scan_report.id,
            "scan_id": scan_report.scan_id,
            "timestamp": scan_report.timestamp.isoformat() if scan_report.timestamp else None,
            "scan_path": scan_report.scan_path,
            "files_scanned": scan_report.files_scanned,
            "threats_found": scan_report.threats_found,
            "report_path": scan_report.report_path,
            "scan_type": scan_report.scan_type or "standard"
        }
        
        # Parse performance metrics if available
        if scan_report.performance_metrics:
            try:
                formatted["performance_metrics"] = json.loads(scan_report.performance_metrics)
            except json.JSONDecodeError:
                formatted["performance_metrics"] = None
        else:
            formatted["performance_metrics"] = None
        
        # Add file results if available
        if hasattr(scan_report, 'file_results') and scan_report.file_results:
            formatted["file_results"] = [
                format_file_result(file_result) for file_result in scan_report.file_results
            ]
        else:
            formatted["file_results"] = []
        
        return formatted
        
    except Exception as e:
        logger.error(f"Error formatting scan report: {e}")
        return {
            "id": getattr(scan_report, 'id', None),
            "scan_id": getattr(scan_report, 'scan_id', 'unknown'),
            "error": f"Formatting error: {str(e)}"
        }


def format_file_result(file_result: Any) -> Dict[str, Any]:
    """
    Format file result for API response.
    
    Args:
        file_result: Database file result object.
        
    Returns:
        dict: Formatted file result.
    """
    try:
        formatted = {
            "id": file_result.id,
            "file_path": file_result.file_path,
            "file_hash": file_result.file_hash,
            "file_size": file_result.file_size or 0,
            "file_type": file_result.file_type or "unknown",
            "is_threat": file_result.is_threat,
            "threat_type": file_result.threat_type,
            "threat_level": file_result.threat_level or "safe",
            "entropy_score": file_result.entropy_score,
            "signature_info": file_result.signature_info
        }
        
        # Parse JSON fields if available
        if file_result.virustotal_result:
            try:
                formatted["virustotal_result"] = json.loads(file_result.virustotal_result)
            except json.JSONDecodeError:
                formatted["virustotal_result"] = None
        else:
            formatted["virustotal_result"] = None
        
        if file_result.static_analysis_result:
            try:
                formatted["static_analysis_result"] = json.loads(file_result.static_analysis_result)
            except json.JSONDecodeError:
                formatted["static_analysis_result"] = None
        else:
            formatted["static_analysis_result"] = None
        
        return formatted
        
    except Exception as e:
        logger.error(f"Error formatting file result: {e}")
        return {
            "id": getattr(file_result, 'id', None),
            "file_path": getattr(file_result, 'file_path', 'unknown'),
            "error": f"Formatting error: {str(e)}"
        }


def format_timestamp(timestamp: Optional[datetime]) -> Optional[str]:
    """
    Format timestamp for API response.
    
    Args:
        timestamp: Datetime object.
        
    Returns:
        str or None: ISO formatted timestamp.
    """
    try:
        if timestamp:
            return timestamp.isoformat()
        return None
    except Exception as e:
        logger.error(f"Error formatting timestamp: {e}")
        return None


def format_file_size(size_bytes: int) -> Dict[str, Any]:
    """
    Format file size with human readable format.
    
    Args:
        size_bytes (int): Size in bytes.
        
    Returns:
        dict: Size information.
    """
    try:
        if size_bytes == 0:
            return {
                "bytes": 0,
                "human_readable": "0 B"
            }
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return {
            "bytes": size_bytes,
            "human_readable": f"{size:.1f} {size_names[i]}"
        }
        
    except Exception as e:
        logger.error(f"Error formatting file size: {e}")
        return {
            "bytes": size_bytes,
            "human_readable": "Unknown",
            "error": str(e)
        }


def format_threat_level(threat_level: str) -> Dict[str, Any]:
    """
    Format threat level with additional information.
    
    Args:
        threat_level (str): Threat level.
        
    Returns:
        dict: Threat level information.
    """
    try:
        level_info = {
            "safe": {
                "level": "safe",
                "severity": 0,
                "color": "green",
                "description": "No threats detected"
            },
            "low": {
                "level": "low", 
                "severity": 1,
                "color": "yellow",
                "description": "Low risk threat detected"
            },
            "medium": {
                "level": "medium",
                "severity": 2,
                "color": "orange", 
                "description": "Medium risk threat detected"
            },
            "high": {
                "level": "high",
                "severity": 3,
                "color": "red",
                "description": "High risk threat detected"
            },
            "critical": {
                "level": "critical",
                "severity": 4,
                "color": "darkred",
                "description": "Critical threat detected"
            }
        }
        
        return level_info.get(threat_level.lower(), {
            "level": threat_level,
            "severity": 0,
            "color": "gray",
            "description": "Unknown threat level"
        })
        
    except Exception as e:
        logger.error(f"Error formatting threat level: {e}")
        return {
            "level": threat_level,
            "severity": 0,
            "color": "gray",
            "description": "Error formatting threat level",
            "error": str(e)
        }
