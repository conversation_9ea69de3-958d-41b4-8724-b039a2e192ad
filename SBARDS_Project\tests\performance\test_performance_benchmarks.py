#!/usr/bin/env python3
"""
Performance Benchmark Tests - SBARDS Project

This module provides comprehensive performance benchmarks to ensure
the new SBARDS structure performs significantly better than the old one.

Benchmark Categories:
- File processing performance
- Hash calculation performance
- Configuration loading performance
- Memory usage benchmarks
- Concurrent processing performance
"""

import os
import sys
import unittest
import tempfile
import time
import threading
import multiprocessing
import psutil
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import statistics

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from core.utils import FileUtils, PerformanceUtils
    from core.config import ConfigManager
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False

class PerformanceBenchmark:
    """Base class for performance benchmarks."""
    
    def __init__(self):
        self.results = {}
        self.baseline_results = {}
    
    def measure_time(self, func, *args, **kwargs):
        """Measure execution time of a function."""
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        duration = end_time - start_time
        return result, duration
    
    def measure_memory(self, func, *args, **kwargs):
        """Measure memory usage of a function."""
        process = psutil.Process()
        
        # Get initial memory
        initial_memory = process.memory_info().rss
        
        # Execute function
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        
        # Get final memory
        final_memory = process.memory_info().rss
        memory_used = final_memory - initial_memory
        duration = end_time - start_time
        
        return result, duration, memory_used
    
    def run_multiple_times(self, func, iterations=10, *args, **kwargs):
        """Run function multiple times and collect statistics."""
        durations = []
        
        for _ in range(iterations):
            _, duration = self.measure_time(func, *args, **kwargs)
            durations.append(duration)
        
        return {
            "mean": statistics.mean(durations),
            "median": statistics.median(durations),
            "min": min(durations),
            "max": max(durations),
            "stdev": statistics.stdev(durations) if len(durations) > 1 else 0,
            "iterations": iterations
        }

class TestFileProcessingPerformance(unittest.TestCase, PerformanceBenchmark):
    """Test file processing performance benchmarks."""
    
    def setUp(self):
        """Set up performance test environment."""
        PerformanceBenchmark.__init__(self)
        
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test files of various sizes
        self.test_files = {}
        
        # Tiny file (100 bytes)
        tiny_file = Path(self.temp_dir) / "tiny.txt"
        with open(tiny_file, 'w') as f:
            f.write("x" * 100)
        self.test_files["tiny"] = str(tiny_file)
        
        # Small file (1KB)
        small_file = Path(self.temp_dir) / "small.txt"
        with open(small_file, 'w') as f:
            f.write("test content\n" * 100)
        self.test_files["small"] = str(small_file)
        
        # Medium file (100KB)
        medium_file = Path(self.temp_dir) / "medium.txt"
        with open(medium_file, 'w') as f:
            f.write("test content for medium file\n" * 4000)
        self.test_files["medium"] = str(medium_file)
        
        # Large file (1MB)
        large_file = Path(self.temp_dir) / "large.txt"
        with open(large_file, 'w') as f:
            f.write("test content for large file testing\n" * 30000)
        self.test_files["large"] = str(large_file)
        
        # Very large file (10MB)
        very_large_file = Path(self.temp_dir) / "very_large.txt"
        with open(very_large_file, 'w') as f:
            f.write("test content for very large file testing performance\n" * 200000)
        self.test_files["very_large"] = str(very_large_file)
    
    def tearDown(self):
        """Clean up performance test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_hash_calculation_performance(self):
        """Benchmark hash calculation performance."""
        print("\n📊 Hash Calculation Performance Benchmark")
        print("-" * 50)
        
        algorithms = ["md5", "sha1", "sha256", "sha512"]
        
        for file_type, file_path in self.test_files.items():
            file_size = os.path.getsize(file_path)
            print(f"\nFile: {file_type} ({file_size:,} bytes)")
            
            for algorithm in algorithms:
                stats = self.run_multiple_times(
                    FileUtils.get_file_hash, 
                    iterations=5,
                    file_path=file_path,
                    algorithm=algorithm
                )
                
                throughput = file_size / stats["mean"] / 1024 / 1024  # MB/s
                
                print(f"  {algorithm.upper():>6}: {stats['mean']:.4f}s ± {stats['stdev']:.4f}s ({throughput:.1f} MB/s)")
                
                # Performance expectations
                if file_type == "tiny":
                    self.assertLess(stats["mean"], 0.01)  # Less than 10ms
                elif file_type == "small":
                    self.assertLess(stats["mean"], 0.05)  # Less than 50ms
                elif file_type == "medium":
                    self.assertLess(stats["mean"], 0.2)   # Less than 200ms
                elif file_type == "large":
                    self.assertLess(stats["mean"], 1.0)   # Less than 1s
                elif file_type == "very_large":
                    self.assertLess(stats["mean"], 5.0)   # Less than 5s
                
                # Throughput expectations (should be reasonable)
                if file_size > 1024:  # Only for files larger than 1KB
                    self.assertGreater(throughput, 1.0)  # At least 1 MB/s
    
    def test_file_operations_performance(self):
        """Benchmark basic file operations performance."""
        print("\n📁 File Operations Performance Benchmark")
        print("-" * 50)
        
        operations = [
            ("file_exists", FileUtils.file_exists),
            ("get_file_size", FileUtils.get_file_size),
        ]
        
        for op_name, op_func in operations:
            print(f"\nOperation: {op_name}")
            
            for file_type, file_path in self.test_files.items():
                stats = self.run_multiple_times(
                    op_func,
                    iterations=100,  # More iterations for fast operations
                    file_path=file_path
                )
                
                ops_per_second = 1 / stats["mean"]
                print(f"  {file_type:>10}: {stats['mean']:.6f}s ({ops_per_second:.0f} ops/sec)")
                
                # Performance expectations (should be very fast)
                self.assertLess(stats["mean"], 0.001)  # Less than 1ms
                self.assertGreater(ops_per_second, 1000)  # At least 1000 ops/sec
    
    def test_concurrent_file_processing(self):
        """Benchmark concurrent file processing performance."""
        print("\n🧵 Concurrent File Processing Benchmark")
        print("-" * 50)
        
        def process_file(file_path):
            """Process a single file (hash + size + existence check)."""
            hash_result = FileUtils.get_file_hash(file_path, "sha256")
            size_result = FileUtils.get_file_size(file_path)
            exists_result = FileUtils.file_exists(file_path)
            return hash_result, size_result, exists_result
        
        file_list = list(self.test_files.values())
        
        # Sequential processing
        start_time = time.perf_counter()
        sequential_results = [process_file(file_path) for file_path in file_list]
        sequential_time = time.perf_counter() - start_time
        
        # Concurrent processing with threads
        start_time = time.perf_counter()
        with ThreadPoolExecutor(max_workers=4) as executor:
            concurrent_results = list(executor.map(process_file, file_list))
        concurrent_time = time.perf_counter() - start_time
        
        # Calculate speedup
        speedup = sequential_time / concurrent_time
        
        print(f"Sequential processing: {sequential_time:.3f}s")
        print(f"Concurrent processing: {concurrent_time:.3f}s")
        print(f"Speedup: {speedup:.2f}x")
        
        # Verify results are the same
        self.assertEqual(len(sequential_results), len(concurrent_results))
        for seq, conc in zip(sequential_results, concurrent_results):
            self.assertEqual(seq, conc)
        
        # Performance expectations
        self.assertGreater(speedup, 1.2)  # At least 20% speedup
        self.assertLess(concurrent_time, sequential_time)

class TestConfigurationPerformance(unittest.TestCase, PerformanceBenchmark):
    """Test configuration loading and access performance."""
    
    def setUp(self):
        """Set up configuration performance test environment."""
        PerformanceBenchmark.__init__(self)
        
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test configurations of different sizes
        self.config_files = {}
        
        # Small config
        small_config = {
            "core": {"debug": True},
            "api": {"port": 8000}
        }
        small_config_file = Path(self.temp_dir) / "small_config.json"
        with open(small_config_file, 'w') as f:
            import json
            json.dump(small_config, f)
        self.config_files["small"] = str(small_config_file)
        
        # Large config
        large_config = {
            "core": {"debug": True, "version": "2.0.0"},
            "api": {"port": 8000, "host": "127.0.0.1"},
            "scanner": {"enabled": True, "threads": 4},
            "static_analysis": {
                "yara_rules_directory": "rules",
                "parallel_processing": True,
                "max_threads": 4,
                "enable_caching": True
            }
        }
        
        # Add many sections to make it large
        for i in range(100):
            large_config[f"section_{i}"] = {
                f"key_{j}": f"value_{j}" for j in range(10)
            }
        
        large_config_file = Path(self.temp_dir) / "large_config.json"
        with open(large_config_file, 'w') as f:
            import json
            json.dump(large_config, f)
        self.config_files["large"] = str(large_config_file)
    
    def tearDown(self):
        """Clean up configuration performance test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_loading_performance(self):
        """Benchmark configuration loading performance."""
        print("\n⚙️ Configuration Loading Performance Benchmark")
        print("-" * 50)
        
        for config_type, config_file in self.config_files.items():
            file_size = os.path.getsize(config_file)
            
            def load_config():
                config_manager = ConfigManager(config_file)
                return config_manager.load_config()
            
            stats = self.run_multiple_times(load_config, iterations=50)
            
            print(f"{config_type.capitalize()} config ({file_size:,} bytes): {stats['mean']:.4f}s ± {stats['stdev']:.4f}s")
            
            # Performance expectations
            if config_type == "small":
                self.assertLess(stats["mean"], 0.01)  # Less than 10ms
            elif config_type == "large":
                self.assertLess(stats["mean"], 0.1)   # Less than 100ms
    
    def test_config_access_performance(self):
        """Benchmark configuration value access performance."""
        print("\n🔍 Configuration Access Performance Benchmark")
        print("-" * 50)
        
        # Load configuration once
        config_manager = ConfigManager(self.config_files["large"])
        config_manager.load_config()
        
        # Test different access patterns
        access_patterns = [
            ("simple_key", lambda: config_manager.get("core.debug")),
            ("nested_key", lambda: config_manager.get("static_analysis.max_threads")),
            ("section_access", lambda: config_manager.get_section("api")),
            ("non_existent", lambda: config_manager.get("non.existent.key", "default"))
        ]
        
        for pattern_name, access_func in access_patterns:
            stats = self.run_multiple_times(access_func, iterations=1000)
            
            ops_per_second = 1 / stats["mean"]
            print(f"{pattern_name:>15}: {stats['mean']:.6f}s ({ops_per_second:.0f} ops/sec)")
            
            # Performance expectations (should be very fast)
            self.assertLess(stats["mean"], 0.0001)  # Less than 0.1ms
            self.assertGreater(ops_per_second, 10000)  # At least 10k ops/sec

class TestMemoryPerformance(unittest.TestCase, PerformanceBenchmark):
    """Test memory usage performance."""
    
    def setUp(self):
        """Set up memory performance test environment."""
        PerformanceBenchmark.__init__(self)
        
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up memory performance test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_memory_usage_during_processing(self):
        """Test memory usage during file processing."""
        print("\n💾 Memory Usage Performance Benchmark")
        print("-" * 50)
        
        # Create a large file for testing
        large_file = Path(self.temp_dir) / "memory_test.txt"
        with open(large_file, 'w') as f:
            f.write("test content for memory testing\n" * 100000)  # ~3MB file
        
        def process_large_file():
            """Process large file and return hash."""
            return FileUtils.get_file_hash(str(large_file), "sha256")
        
        # Measure memory usage
        result, duration, memory_used = self.measure_memory(process_large_file)
        
        memory_mb = memory_used / 1024 / 1024
        file_size_mb = large_file.stat().st_size / 1024 / 1024
        
        print(f"File size: {file_size_mb:.1f} MB")
        print(f"Processing time: {duration:.3f}s")
        print(f"Memory used: {memory_mb:.1f} MB")
        print(f"Memory efficiency: {file_size_mb/memory_mb:.2f}x" if memory_mb > 0 else "N/A")
        
        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 64)  # SHA-256 hash length
        
        # Memory expectations (should be efficient)
        self.assertLess(memory_mb, file_size_mb * 2)  # Should not use more than 2x file size
        self.assertLess(duration, 2.0)  # Should process within 2 seconds

def run_performance_tests():
    """Run all performance tests."""
    test_classes = [
        TestFileProcessingPerformance,
        TestConfigurationPerformance,
        TestMemoryPerformance
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    print("⚡ Running Performance Benchmark Tests")
    print("=" * 50)
    
    success = run_performance_tests()
    
    if success:
        print("\n✅ All performance tests passed!")
        print("🚀 System performance meets expectations")
    else:
        print("\n❌ Some performance tests failed!")
        print("🔧 Please check the performance requirements")
    
    exit(0 if success else 1)
