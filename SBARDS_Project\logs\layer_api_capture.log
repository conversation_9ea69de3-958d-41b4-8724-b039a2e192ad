2025-05-25 22:50:18,687 - layer.api_capture - WARNING - [capture.py:132] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-25 22:50:18,691 - layer.api_capture - INFO - [capture.py:147] - initialize_capture_layer() - Capture layer initialized successfully
2025-05-25 22:53:48,415 - layer.api_capture - WARNING - [capture.py:132] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-25 22:53:48,419 - layer.api_capture - INFO - [capture.py:147] - initialize_capture_layer() - Capture layer initialized successfully
2025-05-25 22:58:37,645 - layer.api_capture - INFO - [capture.py:162] - shutdown_capture_layer() - File interceptor stopped
2025-05-26 02:01:03,756 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:01:03,757 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:01:05,821 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_020105_756529_mock_file.txt
2025-05-26 02:03:42,682 - layer.api_capture - INFO - [capture.py:300] - upload_file() - File uploaded successfully through True Capture Layer: test.txt -> 6905d5624faa1a5f (size: 27, time: 0.145s)
2025-05-26 02:03:42,682 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_020342_536582_test.txt
2025-05-26 02:11:42,872 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:11:42,872 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:11:44,934 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_021144_872645_mock_file.txt
2025-05-26 02:15:43,240 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:15:43,241 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:15:45,300 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_021545_241320_mock_file.txt
2025-05-26 02:20:00,431 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 02:24:46,813 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:24:46,813 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:24:48,869 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_022448_813938_mock_file.txt
2025-05-26 02:48:13,602 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:48:13,602 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:48:15,658 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_024815_602867_mock_file.txt
2025-05-26 02:48:36,891 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 03:10:38,765 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 03:10:38,766 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 03:10:40,832 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_031040_766508_mock_file.txt
2025-05-26 03:12:53,989 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 03:12:53,991 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 03:12:56,047 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_031255_989892_mock_file.txt
2025-05-26 03:39:34,237 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 03:39:34,238 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 03:39:36,382 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_033936_238811_mock_file.txt
2025-05-26 03:40:05,597 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 04:26:56,402 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 04:26:56,403 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 04:26:58,489 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_042658_404480_mock_file.txt
2025-05-26 04:29:38,394 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 04:29:38,394 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 04:29:40,410 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 04:29:55,751 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
