2025-05-26 04:43:32,197 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 04:43:32,198 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 04:43:32,198 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 04:43:35,126 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 04:43:35,126 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 04:43:35,368 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 04:43:35,369 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 04:43:35,369 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 04:43:35,370 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 04:43:35,370 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 04:43:35,371 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 04:43:35,371 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 04:43:35,372 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 04:43:35,372 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:43:35,372 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 04:43:35,373 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 04:43:35,373 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 04:43:35,374 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 04:43:35,374 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 04:43:35,375 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 04:43:35,375 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:43:35,375 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 04:43:37,428 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_044337_369110_mock_file.txt
