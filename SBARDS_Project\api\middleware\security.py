"""
Security middleware for SBARDS API

This module provides security headers and protection mechanisms.
"""

import re
from typing import Callable, List, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from core.logger import get_global_logger

# Configure logging
logger = get_global_logger().get_layer_logger("api.middleware.security")


class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers and protections."""
    
    def __init__(
        self,
        app: ASGIApp,
        enable_security_headers: bool = True,
        enable_input_validation: bool = True,
        allowed_hosts: Optional[List[str]] = None,
        max_request_size: int = 50 * 1024 * 1024  # 50MB
    ):
        """
        Initialize security middleware.
        
        Args:
            app (ASGIApp): ASGI application.
            enable_security_headers (bool): Whether to add security headers.
            enable_input_validation (bool): Whether to validate inputs.
            allowed_hosts (List[str]): List of allowed hosts.
            max_request_size (int): Maximum request size in bytes.
        """
        super().__init__(app)
        self.enable_security_headers = enable_security_headers
        self.enable_input_validation = enable_input_validation
        self.allowed_hosts = allowed_hosts or []
        self.max_request_size = max_request_size
        self.logger = logger
        
        # Compile regex patterns for input validation
        self._compile_validation_patterns()
    
    def _compile_validation_patterns(self):
        """Compile regex patterns for input validation."""
        # SQL injection patterns
        self.sql_injection_patterns = [
            re.compile(r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)", re.IGNORECASE),
            re.compile(r"(\b(OR|AND)\s+\d+\s*=\s*\d+)", re.IGNORECASE),
            re.compile(r"('|\"|;|--|\*|\/\*|\*\/)", re.IGNORECASE),
        ]
        
        # XSS patterns
        self.xss_patterns = [
            re.compile(r"<script[^>]*>.*?</script>", re.IGNORECASE | re.DOTALL),
            re.compile(r"javascript:", re.IGNORECASE),
            re.compile(r"on\w+\s*=", re.IGNORECASE),
            re.compile(r"<iframe[^>]*>", re.IGNORECASE),
        ]
        
        # Path traversal patterns
        self.path_traversal_patterns = [
            re.compile(r"\.\.\/"),
            re.compile(r"\.\.\\"),
            re.compile(r"%2e%2e%2f", re.IGNORECASE),
            re.compile(r"%2e%2e%5c", re.IGNORECASE),
        ]
        
        # Command injection patterns
        self.command_injection_patterns = [
            re.compile(r"[;&|`$(){}[\]<>]"),
            re.compile(r"\b(cat|ls|dir|type|echo|ping|wget|curl|nc|netcat)\b", re.IGNORECASE),
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with security checks.
        
        Args:
            request (Request): FastAPI request.
            call_next (Callable): Next middleware or route handler.
            
        Returns:
            Response: FastAPI response.
        """
        # Validate host
        if not self._validate_host(request):
            self.logger.warning(f"Invalid host header: {request.headers.get('host', 'unknown')}")
            return Response(
                content="Invalid host header",
                status_code=400,
                headers={"Content-Type": "text/plain"}
            )
        
        # Check request size
        if not await self._validate_request_size(request):
            self.logger.warning(f"Request size too large from {self._get_client_ip(request)}")
            return Response(
                content="Request entity too large",
                status_code=413,
                headers={"Content-Type": "text/plain"}
            )
        
        # Validate input if enabled
        if self.enable_input_validation:
            validation_result = await self._validate_input(request)
            if not validation_result["valid"]:
                self.logger.warning(f"Malicious input detected: {validation_result['reason']} from {self._get_client_ip(request)}")
                return Response(
                    content=f"Invalid input detected: {validation_result['reason']}",
                    status_code=400,
                    headers={"Content-Type": "text/plain"}
                )
        
        # Process request
        response = await call_next(request)
        
        # Add security headers if enabled
        if self.enable_security_headers:
            self._add_security_headers(response)
        
        return response
    
    def _validate_host(self, request: Request) -> bool:
        """
        Validate host header.
        
        Args:
            request (Request): FastAPI request.
            
        Returns:
            bool: True if host is valid.
        """
        if not self.allowed_hosts:
            return True  # No host validation if no allowed hosts specified
        
        host = request.headers.get("host", "").lower()
        
        # Remove port if present
        if ":" in host:
            host = host.split(":")[0]
        
        return host in self.allowed_hosts
    
    async def _validate_request_size(self, request: Request) -> bool:
        """
        Validate request size.
        
        Args:
            request (Request): FastAPI request.
            
        Returns:
            bool: True if request size is acceptable.
        """
        content_length = request.headers.get("content-length")
        
        if content_length:
            try:
                size = int(content_length)
                return size <= self.max_request_size
            except ValueError:
                return False
        
        return True  # No content-length header, assume it's fine
    
    async def _validate_input(self, request: Request) -> dict:
        """
        Validate request input for malicious patterns.
        
        Args:
            request (Request): FastAPI request.
            
        Returns:
            dict: Validation result with 'valid' and 'reason' keys.
        """
        # Check URL path
        path = request.url.path
        if self._check_malicious_patterns(path):
            return {"valid": False, "reason": "Malicious pattern in URL path"}
        
        # Check query parameters
        for key, value in request.query_params.items():
            if self._check_malicious_patterns(key) or self._check_malicious_patterns(value):
                return {"valid": False, "reason": "Malicious pattern in query parameters"}
        
        # Check headers (excluding sensitive ones)
        safe_headers = ["user-agent", "referer", "accept", "accept-language"]
        for name, value in request.headers.items():
            if name.lower() in safe_headers:
                if self._check_malicious_patterns(value):
                    return {"valid": False, "reason": f"Malicious pattern in {name} header"}
        
        # For POST/PUT requests, we would check body here
        # Note: This is a simplified check. In production, you might want more sophisticated validation
        
        return {"valid": True, "reason": ""}
    
    def _check_malicious_patterns(self, text: str) -> bool:
        """
        Check text for malicious patterns.
        
        Args:
            text (str): Text to check.
            
        Returns:
            bool: True if malicious patterns found.
        """
        if not text:
            return False
        
        # Check SQL injection patterns
        for pattern in self.sql_injection_patterns:
            if pattern.search(text):
                return True
        
        # Check XSS patterns
        for pattern in self.xss_patterns:
            if pattern.search(text):
                return True
        
        # Check path traversal patterns
        for pattern in self.path_traversal_patterns:
            if pattern.search(text):
                return True
        
        # Check command injection patterns
        for pattern in self.command_injection_patterns:
            if pattern.search(text):
                return True
        
        return False
    
    def _add_security_headers(self, response: Response):
        """
        Add security headers to response.
        
        Args:
            response (Response): FastAPI response.
        """
        # Content Security Policy
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        
        # X-Content-Type-Options
        response.headers["X-Content-Type-Options"] = "nosniff"
        
        # X-Frame-Options
        response.headers["X-Frame-Options"] = "DENY"
        
        # X-XSS-Protection
        response.headers["X-XSS-Protection"] = "1; mode=block"
        
        # Referrer Policy
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Strict Transport Security (only for HTTPS)
        # response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # Permissions Policy
        response.headers["Permissions-Policy"] = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )
        
        # Remove server information
        if "server" in response.headers:
            del response.headers["server"]
        
        # Add custom security header
        response.headers["X-Security-Framework"] = "SBARDS-API"
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Get client IP address.
        
        Args:
            request (Request): FastAPI request.
            
        Returns:
            str: Client IP address.
        """
        # Try to get real IP from headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to client IP
        if request.client:
            return request.client.host
        
        return "unknown"
