#!/usr/bin/env python3
"""
Quick Test Suite for SBARDS Core Layer

Tests basic functionality of core components.
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.config import ConfigManager
    from core.logger import get_global_logger
    from core.constants import *
    from core.utils import FileUtils, SecurityUtils, PerformanceUtils
    CORE_AVAILABLE = True
except ImportError as e:
    print(f"Core modules not available: {e}")
    CORE_AVAILABLE = False

class TestCoreComponents(unittest.TestCase):
    """Test core components."""
    
    def setUp(self):
        """Set up test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core modules not available")
        
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_config_manager(self):
        """Test ConfigManager functionality."""
        config_file = os.path.join(self.test_dir, "test_config.json")
        
        # Create test config
        test_config = {
            "test_setting": "test_value",
            "numeric_setting": 42
        }
        
        import json
        with open(config_file, 'w') as f:
            json.dump(test_config, f)
        
        # Test config loading
        config_manager = ConfigManager(config_file)
        self.assertEqual(config_manager.get("test_setting"), "test_value")
        self.assertEqual(config_manager.get("numeric_setting"), 42)
        
        print("✅ ConfigManager test passed")
    
    def test_logger(self):
        """Test logger functionality."""
        logger = get_global_logger()
        self.assertIsNotNone(logger)
        
        # Test layer logger
        layer_logger = logger.get_layer_logger("test_layer")
        self.assertIsNotNone(layer_logger)
        
        # Test logging
        layer_logger.info("Test log message")
        
        print("✅ Logger test passed")
    
    def test_constants(self):
        """Test constants availability."""
        # Test that constants are defined
        self.assertIsNotNone(ThreatLevel)
        self.assertIsNotNone(FileStatus)
        
        print("✅ Constants test passed")
    
    def test_file_utils(self):
        """Test FileUtils functionality."""
        # Create test file
        test_file = os.path.join(self.test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        # Test file operations
        self.assertTrue(FileUtils.file_exists(test_file))
        
        file_size = FileUtils.get_file_size(test_file)
        self.assertGreater(file_size, 0)
        
        print("✅ FileUtils test passed")
    
    def test_security_utils(self):
        """Test SecurityUtils functionality."""
        # Test hash calculation
        test_data = b"test data for hashing"
        hash_result = SecurityUtils.calculate_sha256(test_data)
        self.assertIsNotNone(hash_result)
        self.assertEqual(len(hash_result), 64)  # SHA-256 hex length
        
        print("✅ SecurityUtils test passed")
    
    def test_performance_utils(self):
        """Test PerformanceUtils functionality."""
        # Test timing
        with PerformanceUtils.Timer() as timer:
            import time
            time.sleep(0.01)  # 10ms
        
        self.assertGreater(timer.elapsed, 0)
        
        print("✅ PerformanceUtils test passed")

def main():
    """Run core tests."""
    print("🧪 SBARDS Core Layer Tests")
    print("=" * 30)
    
    if not CORE_AVAILABLE:
        print("❌ Core modules not available - skipping tests")
        return False
    
    # Run tests
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCoreComponents)
    runner = unittest.TextTestRunner(verbosity=1)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n📊 Test Results:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    if success:
        print("🎉 All core tests passed!")
    else:
        print("❌ Some tests failed")
        
    return success

if __name__ == "__main__":
    main()
