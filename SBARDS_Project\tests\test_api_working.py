#!/usr/bin/env python3
"""
اختبار API للتأكد من أنه يعمل بشكل صحيح
Test API to ensure it's working correctly
"""

import requests
import json
import time
from pathlib import Path

# API base URL
BASE_URL = "http://127.0.0.1:8000"

def test_api_endpoints():
    """اختبار جميع endpoints في API"""
    print("🧪 اختبار SBARDS API")
    print("=" * 50)
    
    tests_passed = 0
    tests_total = 0
    
    # Test 1: Root endpoint
    print("\n1️⃣ اختبار الصفحة الرئيسية...")
    tests_total += 1
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل بنجاح")
            tests_passed += 1
        else:
            print(f"❌ خطأ في الصفحة الرئيسية: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # Test 2: Dashboard
    print("\n2️⃣ اختبار لوحة التحكم...")
    tests_total += 1
    try:
        response = requests.get(f"{BASE_URL}/dashboard")
        if response.status_code == 200 and "SBARDS Dashboard" in response.text:
            print("✅ لوحة التحكم تعمل بنجاح")
            tests_passed += 1
        else:
            print(f"❌ خطأ في لوحة التحكم: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # Test 3: API root
    print("\n3️⃣ اختبار API root...")
    tests_total += 1
    try:
        response = requests.get(f"{BASE_URL}/api")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API root يعمل: {data['message']}")
            print(f"   الإصدار: {data['version']}")
            tests_passed += 1
        else:
            print(f"❌ خطأ في API root: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # Test 4: Health check
    print("\n4️⃣ اختبار فحص الصحة...")
    tests_total += 1
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ فحص الصحة يعمل: {data['status']}")
            print(f"   طبقة الالتقاط: {data['capture_layer']}")
            tests_passed += 1
        else:
            print(f"❌ خطأ في فحص الصحة: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # Test 5: File upload
    print("\n5️⃣ اختبار رفع الملفات...")
    tests_total += 1
    try:
        # Create a test file
        test_content = "This is a test file for SBARDS API"
        files = {'file': ('test.txt', test_content, 'text/plain')}
        
        response = requests.post(f"{BASE_URL}/api/upload", files=files)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ رفع الملف نجح: {data['message']}")
            print(f"   اسم الملف: {data['file_info']['filename']}")
            print(f"   حجم الملف: {data['file_info']['size']} بايت")
            tests_passed += 1
        else:
            print(f"❌ خطأ في رفع الملف: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # Test 6: Capture status (if available)
    print("\n6️⃣ اختبار حالة طبقة الالتقاط...")
    tests_total += 1
    try:
        response = requests.get(f"{BASE_URL}/api/capture/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ حالة طبقة الالتقاط: {data.get('status', 'unknown')}")
            tests_passed += 1
        else:
            print(f"⚠️ طبقة الالتقاط غير متاحة: {response.status_code}")
            # لا نعتبر هذا فشل لأن طبقة الالتقاط قد تكون معطلة
            tests_passed += 1
    except Exception as e:
        print(f"⚠️ طبقة الالتقاط غير متاحة: {e}")
        tests_passed += 1
    
    # Test 7: API Documentation
    print("\n7️⃣ اختبار توثيق API...")
    tests_total += 1
    try:
        response = requests.get(f"{BASE_URL}/api/docs")
        if response.status_code == 200 and "swagger" in response.text.lower():
            print("✅ توثيق API متاح")
            tests_passed += 1
        else:
            print(f"❌ خطأ في توثيق API: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # Results
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    print(f"   اختبارات نجحت: {tests_passed}/{tests_total}")
    print(f"   معدل النجاح: {(tests_passed/tests_total)*100:.1f}%")
    
    if tests_passed == tests_total:
        print("🎉 جميع الاختبارات نجحت! API يعمل بشكل مثالي")
    elif tests_passed >= tests_total * 0.8:
        print("✅ معظم الاختبارات نجحت! API يعمل بشكل جيد")
    else:
        print("⚠️ بعض الاختبارات فشلت. يحتاج API إلى مراجعة")
    
    return tests_passed, tests_total

def test_api_performance():
    """اختبار أداء API"""
    print("\n🚀 اختبار أداء API")
    print("=" * 30)
    
    # Test response time
    start_time = time.time()
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        print(f"⏱️ زمن الاستجابة: {response_time:.2f} مللي ثانية")
        
        if response_time < 100:
            print("🚀 أداء ممتاز!")
        elif response_time < 500:
            print("✅ أداء جيد")
        else:
            print("⚠️ أداء بطيء")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {e}")

def main():
    """الدالة الرئيسية"""
    print("🛡️ SBARDS API Test Suite")
    print("=" * 60)
    print("اختبار شامل لواجهة برمجة التطبيقات")
    print("=" * 60)
    
    # Test if server is running
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        print("✅ السيرفر يعمل ومتاح")
    except Exception as e:
        print(f"❌ السيرفر غير متاح: {e}")
        print("تأكد من تشغيل السيرفر باستخدام: python api/main.py")
        return
    
    # Run tests
    passed, total = test_api_endpoints()
    test_api_performance()
    
    print("\n" + "=" * 60)
    print("🏁 انتهى الاختبار")
    print("=" * 60)
    
    # URLs for manual testing
    print("\n🔗 روابط للاختبار اليدوي:")
    print(f"   🏠 الصفحة الرئيسية: {BASE_URL}/")
    print(f"   📊 لوحة التحكم: {BASE_URL}/dashboard")
    print(f"   📖 توثيق API: {BASE_URL}/api/docs")
    print(f"   🏥 فحص الصحة: {BASE_URL}/api/health")
    print(f"   📁 حالة الالتقاط: {BASE_URL}/api/capture/status")

if __name__ == "__main__":
    main()
