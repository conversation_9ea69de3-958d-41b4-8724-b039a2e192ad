#!/bin/bash

# SBARDS Project - Enhanced Pre-scanning Phase Runner for Linux
# This script runs the enhanced pre-scanning phase of the SBARDS Project on Linux

# Set up colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== SBARDS PROJECT - ENHANCED PRE-SCANNING PHASE ===${NC}"
echo "Platform: Linux"
echo "Date: $(date)"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: Python 3 is not installed. Please install Python 3 and try again.${NC}"
    exit 1
fi

# Check if pipenv is installed
if ! command -v pipenv &> /dev/null; then
    echo -e "${YELLOW}Pipenv is not installed. Installing pipenv...${NC}"
    pip3 install pipenv
fi

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p rules samples logs output scanner_core/utils

# Check if the configuration file exists
if [ ! -f "config.json" ]; then
    echo -e "${YELLOW}Configuration file not found. Creating default configuration...${NC}"
    cat > config.json << 'EOL'
{
    "scanner": {
        "target_directory": "E:\\WA",
        "recursive": true,
        "max_depth": 10,
        "exclude_dirs": [
            "System Volume Information",
            "$RECYCLE.BIN",
            "Windows",
            "Program Files",
            "Program Files (x86)"
        ],
        "exclude_extensions": [
            ".exe",
            ".dll",
            ".sys"
        ],
        "max_file_size_mb": 100
    },
    "rules": {
        "rule_files": [
            "rules/custom_rules.yar",
            "rules/malware_rules.yar"
        ],
        "enable_categories": [
            "ransomware",
            "trojan",
            "backdoor",
            "keylogger",
            "spyware",
            "general",
            "document",
            "executable",
            "test"
        ]
    },
    "output": {
        "log_directory": "logs",
        "output_directory": "output",
        "json_output": true,
        "csv_output": true,
        "html_report": true,
        "log_level": "info"
    },
    "performance": {
        "threads": 4,
        "batch_size": 10,
        "timeout_seconds": 30
    }
}
EOL
fi

# Check if the YARA rules files exist
if [ ! -f "rules/custom_rules.yar" ]; then
    echo -e "${YELLOW}YARA rules file not found. Creating sample rules files...${NC}"
    cat > rules/custom_rules.yar << 'EOL'
rule ExampleRule
{
    meta:
        description = "This is an example rule"
        author = "SBARDS Project"
        date = "2025-05-13"
        category = "test"
        severity = "info"

    strings:
        $text_string = "malicious" nocase
        $hex_string = { 4D 5A 90 00 }  // MZ header for PE files

    condition:
        any of them
}

rule PotentialRansomware
{
    meta:
        description = "Detects potential ransomware characteristics"
        author = "SBARDS Project"
        date = "2025-05-13"
        category = "ransomware"
        severity = "high"

    strings:
        $ransom_note = "your files have been encrypted" nocase
        $bitcoin = "bitcoin" nocase
        $payment = "payment" nocase
        $decrypt = "decrypt" nocase
        $encrypt = "encrypt" nocase

    condition:
        $ransom_note or ($bitcoin and $payment) or ($encrypt and $decrypt)
}
EOL
fi

# Check if the target directory exists
TARGET_DIR=$(grep -o '"target_directory": "[^"]*"' config.json | cut -d'"' -f4)
if [[ "$TARGET_DIR" == E:\\* ]]; then
    echo -e "${YELLOW}Warning: Target directory is set to a Windows path: $TARGET_DIR${NC}"
    echo -e "${YELLOW}Updating configuration to use a Linux path...${NC}"

    # Update the target directory to a Linux path
    NEW_TARGET_DIR="/tmp/scan_target"
    mkdir -p "$NEW_TARGET_DIR"

    # Create a sample file in the target directory
    echo "Creating a sample file in the target directory..."
    cat > "$NEW_TARGET_DIR/test_sample.txt" << 'EOL'
This is a test file that contains the word malicious to trigger our YARA rule.
It does not contain any actual malware but is used for testing the scanner functionality.

The file also mentions the words encrypt and decrypt to potentially trigger the PotentialRansomware rule.
EOL

    # Update the configuration file
    sed -i "s|\"target_directory\": \"$TARGET_DIR\"|\"target_directory\": \"$NEW_TARGET_DIR\"|g" config.json
    echo -e "${GREEN}Updated target directory to: $NEW_TARGET_DIR${NC}"
fi

# Install dependencies
echo -e "${BLUE}Installing dependencies...${NC}"
pipenv install

# Run the pre-scanning phase
echo -e "${GREEN}Running the enhanced pre-scanning phase...${NC}"
pipenv run python3 run_prescanning.py

echo -e "${BLUE}Pre-scanning phase complete!${NC}"
echo "Check the output directory for scan results."
