"""
Scan Router for SBARDS API

This module provides the scan router for the SBARDS API.
"""

import os
import logging
import json
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from pydantic import BaseModel

# Create router
router = APIRouter()

# Models
class ScanOptions(BaseModel):
    """Scan options model."""
    target_directory: str
    recursive: bool = True
    max_depth: int = 5
    exclude_dirs: List[str] = []
    exclude_extensions: List[str] = []
    max_file_size_mb: int = 100
    enable_yara: bool = True
    enable_hash_check: bool = True
    enable_av_integration: bool = True
    enable_fast_track: bool = True
    threads: int = 4
    batch_size: int = 20

class ScanResult(BaseModel):
    """Scan result model."""
    scan_id: str
    status: str
    target_directory: str
    total_files: int
    scanned_files: int
    matched_files: int
    elapsed_time: float
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    results: List[Dict[str, Any]] = []

class FileResult(BaseModel):
    """File scan result model."""
    file_path: str
    file_size: int
    file_hash: Optional[str] = None
    scan_time: float
    matches: List[Dict[str, Any]] = []
    threat_level: str = "safe"
    recommendations: List[str] = []

# Global scan storage (in production, use a database)
_active_scans = {}
_scan_results = {}

# Endpoints
@router.post("/start", response_model=ScanResult)
async def start_scan(
    scan_options: ScanOptions,
    background_tasks: BackgroundTasks
):
    """Start a comprehensive scan."""
    try:
        # Validate target directory
        if not os.path.exists(scan_options.target_directory):
            raise HTTPException(status_code=400, detail=f"Target directory not found: {scan_options.target_directory}")
        
        # Generate scan ID
        import uuid
        scan_id = str(uuid.uuid4())
        
        # Create initial scan result
        scan_result = ScanResult(
            scan_id=scan_id,
            status="running",
            target_directory=scan_options.target_directory,
            total_files=0,
            scanned_files=0,
            matched_files=0,
            elapsed_time=0.0,
            start_time=None,
            end_time=None,
            results=[]
        )
        
        # Store scan in active scans
        _active_scans[scan_id] = scan_options
        _scan_results[scan_id] = scan_result
        
        # Start scan in background
        background_tasks.add_task(run_scan_background, scan_id, scan_options)
        
        return scan_result
    except Exception as e:
        logging.error(f"Error starting scan: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting scan: {str(e)}")

async def run_scan_background(scan_id: str, scan_options: ScanOptions):
    """Run scan in background."""
    try:
        import time
        from core.utils import FileUtils
        
        start_time = time.time()
        
        # Update scan status
        _scan_results[scan_id].status = "running"
        _scan_results[scan_id].start_time = start_time
        
        # Discover files
        files_to_scan = []
        for root, dirs, files in os.walk(scan_options.target_directory):
            # Apply exclusions
            if scan_options.exclude_dirs:
                dirs[:] = [d for d in dirs if d not in scan_options.exclude_dirs]
            
            for file in files:
                file_path = os.path.join(root, file)
                
                # Check file size
                try:
                    file_size = os.path.getsize(file_path)
                    if file_size > scan_options.max_file_size_mb * 1024 * 1024:
                        continue
                except:
                    continue
                
                # Check extension exclusions
                if scan_options.exclude_extensions:
                    _, ext = os.path.splitext(file)
                    if ext.lower() in scan_options.exclude_extensions:
                        continue
                
                files_to_scan.append(file_path)
        
        # Update total files
        _scan_results[scan_id].total_files = len(files_to_scan)
        
        # Scan files
        scanned_count = 0
        matched_count = 0
        
        for file_path in files_to_scan:
            try:
                file_start_time = time.time()
                
                # Basic file analysis
                file_size = FileUtils.get_file_size(file_path)
                file_hash = FileUtils.get_file_hash(file_path, "sha256") if scan_options.enable_hash_check else None
                
                file_end_time = time.time()
                scan_time = file_end_time - file_start_time
                
                # Create file result
                file_result = {
                    "file_path": file_path,
                    "file_size": file_size,
                    "file_hash": file_hash,
                    "scan_time": scan_time,
                    "matches": [],
                    "threat_level": "safe",
                    "recommendations": []
                }
                
                # Add to results
                _scan_results[scan_id].results.append(file_result)
                scanned_count += 1
                
                # Update progress
                _scan_results[scan_id].scanned_files = scanned_count
                _scan_results[scan_id].matched_files = matched_count
                
            except Exception as e:
                logging.warning(f"Error scanning file {file_path}: {e}")
                continue
        
        # Complete scan
        end_time = time.time()
        _scan_results[scan_id].status = "completed"
        _scan_results[scan_id].end_time = end_time
        _scan_results[scan_id].elapsed_time = end_time - start_time
        
        # Remove from active scans
        if scan_id in _active_scans:
            del _active_scans[scan_id]
        
    except Exception as e:
        logging.error(f"Error in background scan {scan_id}: {e}")
        _scan_results[scan_id].status = "failed"
        if scan_id in _active_scans:
            del _active_scans[scan_id]

@router.get("/result/{scan_id}", response_model=ScanResult)
async def get_scan_result(scan_id: str):
    """Get scan result."""
    try:
        if scan_id not in _scan_results:
            raise HTTPException(status_code=404, detail=f"Scan result not found: {scan_id}")
        
        return _scan_results[scan_id]
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting scan result: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scan result: {str(e)}")

@router.get("/results", response_model=List[ScanResult])
async def get_all_scan_results():
    """Get all scan results."""
    try:
        return list(_scan_results.values())
    except Exception as e:
        logging.error(f"Error getting scan results: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scan results: {str(e)}")

@router.delete("/result/{scan_id}")
async def delete_scan_result(scan_id: str):
    """Delete scan result."""
    try:
        if scan_id not in _scan_results:
            raise HTTPException(status_code=404, detail=f"Scan result not found: {scan_id}")
        
        # Remove from both active and results
        if scan_id in _active_scans:
            del _active_scans[scan_id]
        del _scan_results[scan_id]
        
        return {"status": "deleted", "scan_id": scan_id}
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error deleting scan result: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting scan result: {str(e)}")

@router.get("/active")
async def get_active_scans():
    """Get active scans."""
    try:
        active_scans = []
        for scan_id in _active_scans:
            if scan_id in _scan_results:
                active_scans.append(_scan_results[scan_id])
        return active_scans
    except Exception as e:
        logging.error(f"Error getting active scans: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting active scans: {str(e)}")

@router.get("/health")
async def get_scan_health():
    """Get scan service health."""
    try:
        return {
            "status": "healthy",
            "service": "scan",
            "version": "2.0.0",
            "active_scans": len(_active_scans),
            "total_results": len(_scan_results),
            "capabilities": [
                "file_scanning",
                "hash_calculation",
                "yara_integration",
                "background_processing",
                "real_time_progress"
            ]
        }
    except Exception as e:
        logging.error(f"Error getting scan health: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scan health: {str(e)}")

@router.websocket("/ws/{scan_id}")
async def websocket_scan_progress(websocket: WebSocket, scan_id: str):
    """WebSocket endpoint for real-time scan progress."""
    await websocket.accept()
    
    try:
        while True:
            # Check if scan exists
            if scan_id in _scan_results:
                scan_result = _scan_results[scan_id]
                
                # Send progress update
                progress_data = {
                    "scan_id": scan_id,
                    "status": scan_result.status,
                    "progress": {
                        "total_files": scan_result.total_files,
                        "scanned_files": scan_result.scanned_files,
                        "matched_files": scan_result.matched_files,
                        "elapsed_time": scan_result.elapsed_time
                    }
                }
                
                await websocket.send_text(json.dumps(progress_data))
                
                # If scan is completed or failed, break
                if scan_result.status in ["completed", "failed"]:
                    break
            else:
                # Scan not found
                await websocket.send_text(json.dumps({
                    "error": f"Scan {scan_id} not found"
                }))
                break
            
            # Wait before next update
            import asyncio
            await asyncio.sleep(1)
            
    except WebSocketDisconnect:
        logging.info(f"WebSocket disconnected for scan {scan_id}")
    except Exception as e:
        logging.error(f"WebSocket error for scan {scan_id}: {e}")
        await websocket.close()
