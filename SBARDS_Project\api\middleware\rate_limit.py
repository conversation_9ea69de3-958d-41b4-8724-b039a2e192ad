"""
Rate limiting middleware for SBARDS API

This module provides rate limiting functionality to prevent abuse.
"""

import time
from typing import Callable, Dict, Optional
from collections import defaultdict, deque

from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from core.logger import get_global_logger

# Configure logging
logger = get_global_logger().get_layer_logger("api.middleware.rate_limit")


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting HTTP requests."""
    
    def __init__(
        self, 
        app: ASGIApp,
        calls: int = 100,
        period: int = 60,
        per_endpoint_limits: Optional[Dict[str, Dict[str, int]]] = None
    ):
        """
        Initialize rate limiting middleware.
        
        Args:
            app (ASGIApp): ASGI application.
            calls (int): Number of calls allowed per period.
            period (int): Time period in seconds.
            per_endpoint_limits (dict): Specific limits per endpoint.
        """
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.per_endpoint_limits = per_endpoint_limits or {}
        self.logger = logger
        
        # Storage for tracking requests
        # Format: {client_ip: deque([timestamp1, timestamp2, ...])}
        self.request_history: Dict[str, deque] = defaultdict(lambda: deque())
        
        # Endpoint-specific tracking
        # Format: {(client_ip, endpoint): deque([timestamp1, timestamp2, ...])}
        self.endpoint_history: Dict[tuple, deque] = defaultdict(lambda: deque())
        
        # Cleanup interval (remove old entries every 5 minutes)
        self.last_cleanup = time.time()
        self.cleanup_interval = 300  # 5 minutes
        
        # Paths to exclude from rate limiting
        self.excluded_paths = {
            "/health",
            "/api/health",
            "/metrics",
            "/api/docs",
            "/api/redoc",
            "/api/openapi.json"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with rate limiting.
        
        Args:
            request (Request): FastAPI request.
            call_next (Callable): Next middleware or route handler.
            
        Returns:
            Response: FastAPI response.
        """
        # Skip rate limiting for excluded paths
        if self._should_skip_rate_limiting(request.url.path):
            return await call_next(request)
        
        # Get client identifier
        client_id = self._get_client_identifier(request)
        
        # Perform cleanup if needed
        await self._cleanup_old_entries()
        
        # Check rate limits
        if not self._check_rate_limit(client_id, request):
            # Rate limit exceeded
            self.logger.warning(f"Rate limit exceeded for client: {client_id} on path: {request.url.path}")
            
            # Get retry-after time
            retry_after = self._get_retry_after(client_id, request)
            
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Rate limit exceeded",
                    "message": "Too many requests. Please try again later.",
                    "retry_after": retry_after
                },
                headers={"Retry-After": str(retry_after)}
            )
        
        # Record the request
        self._record_request(client_id, request)
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers to response
        self._add_rate_limit_headers(response, client_id, request)
        
        return response
    
    def _should_skip_rate_limiting(self, path: str) -> bool:
        """
        Check if rate limiting should be skipped for this path.
        
        Args:
            path (str): Request path.
            
        Returns:
            bool: True if rate limiting should be skipped.
        """
        return path in self.excluded_paths or path.startswith("/static/")
    
    def _get_client_identifier(self, request: Request) -> str:
        """
        Get client identifier for rate limiting.
        
        Args:
            request (Request): FastAPI request.
            
        Returns:
            str: Client identifier.
        """
        # Try to get real IP from headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to client IP
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _get_endpoint_limits(self, request: Request) -> tuple:
        """
        Get rate limits for specific endpoint.
        
        Args:
            request (Request): FastAPI request.
            
        Returns:
            tuple: (calls, period) for the endpoint.
        """
        path = request.url.path
        method = request.method
        
        # Check for endpoint-specific limits
        endpoint_key = f"{method} {path}"
        if endpoint_key in self.per_endpoint_limits:
            limits = self.per_endpoint_limits[endpoint_key]
            return limits.get("calls", self.calls), limits.get("period", self.period)
        
        # Check for path-based limits
        for pattern, limits in self.per_endpoint_limits.items():
            if path.startswith(pattern):
                return limits.get("calls", self.calls), limits.get("period", self.period)
        
        # Return default limits
        return self.calls, self.period
    
    def _check_rate_limit(self, client_id: str, request: Request) -> bool:
        """
        Check if request is within rate limits.
        
        Args:
            client_id (str): Client identifier.
            request (Request): FastAPI request.
            
        Returns:
            bool: True if within limits, False if exceeded.
        """
        current_time = time.time()
        
        # Get limits for this endpoint
        calls_limit, period_limit = self._get_endpoint_limits(request)
        
        # Check global rate limit for client
        client_history = self.request_history[client_id]
        
        # Remove old entries
        while client_history and current_time - client_history[0] > self.period:
            client_history.popleft()
        
        # Check if global limit exceeded
        if len(client_history) >= self.calls:
            return False
        
        # Check endpoint-specific rate limit
        endpoint_key = (client_id, f"{request.method} {request.url.path}")
        endpoint_history = self.endpoint_history[endpoint_key]
        
        # Remove old entries
        while endpoint_history and current_time - endpoint_history[0] > period_limit:
            endpoint_history.popleft()
        
        # Check if endpoint limit exceeded
        if len(endpoint_history) >= calls_limit:
            return False
        
        return True
    
    def _record_request(self, client_id: str, request: Request):
        """
        Record a request for rate limiting tracking.
        
        Args:
            client_id (str): Client identifier.
            request (Request): FastAPI request.
        """
        current_time = time.time()
        
        # Record in global history
        self.request_history[client_id].append(current_time)
        
        # Record in endpoint-specific history
        endpoint_key = (client_id, f"{request.method} {request.url.path}")
        self.endpoint_history[endpoint_key].append(current_time)
    
    def _get_retry_after(self, client_id: str, request: Request) -> int:
        """
        Get retry-after time in seconds.
        
        Args:
            client_id (str): Client identifier.
            request (Request): FastAPI request.
            
        Returns:
            int: Retry-after time in seconds.
        """
        current_time = time.time()
        
        # Check global rate limit
        client_history = self.request_history[client_id]
        if client_history:
            oldest_request = client_history[0]
            global_retry_after = int(self.period - (current_time - oldest_request)) + 1
        else:
            global_retry_after = 1
        
        # Check endpoint-specific rate limit
        endpoint_key = (client_id, f"{request.method} {request.url.path}")
        endpoint_history = self.endpoint_history[endpoint_key]
        
        if endpoint_history:
            _, period_limit = self._get_endpoint_limits(request)
            oldest_endpoint_request = endpoint_history[0]
            endpoint_retry_after = int(period_limit - (current_time - oldest_endpoint_request)) + 1
        else:
            endpoint_retry_after = 1
        
        # Return the maximum retry-after time
        return max(global_retry_after, endpoint_retry_after)
    
    def _add_rate_limit_headers(self, response: Response, client_id: str, request: Request):
        """
        Add rate limit headers to response.
        
        Args:
            response (Response): FastAPI response.
            client_id (str): Client identifier.
            request (Request): FastAPI request.
        """
        current_time = time.time()
        
        # Get limits for this endpoint
        calls_limit, period_limit = self._get_endpoint_limits(request)
        
        # Calculate remaining requests for global limit
        client_history = self.request_history[client_id]
        global_remaining = max(0, self.calls - len(client_history))
        
        # Calculate remaining requests for endpoint limit
        endpoint_key = (client_id, f"{request.method} {request.url.path}")
        endpoint_history = self.endpoint_history[endpoint_key]
        endpoint_remaining = max(0, calls_limit - len(endpoint_history))
        
        # Use the more restrictive limit
        remaining = min(global_remaining, endpoint_remaining)
        
        # Calculate reset time
        if client_history:
            global_reset = int(client_history[0] + self.period)
        else:
            global_reset = int(current_time + self.period)
        
        if endpoint_history:
            endpoint_reset = int(endpoint_history[0] + period_limit)
        else:
            endpoint_reset = int(current_time + period_limit)
        
        reset_time = max(global_reset, endpoint_reset)
        
        # Add headers
        response.headers["X-RateLimit-Limit"] = str(min(self.calls, calls_limit))
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(reset_time)
        response.headers["X-RateLimit-Period"] = str(min(self.period, period_limit))
    
    async def _cleanup_old_entries(self):
        """Clean up old entries from rate limiting storage."""
        current_time = time.time()
        
        # Only cleanup every cleanup_interval seconds
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
        
        self.last_cleanup = current_time
        
        # Cleanup global history
        clients_to_remove = []
        for client_id, history in self.request_history.items():
            # Remove old entries
            while history and current_time - history[0] > self.period:
                history.popleft()
            
            # Mark empty histories for removal
            if not history:
                clients_to_remove.append(client_id)
        
        # Remove empty client histories
        for client_id in clients_to_remove:
            del self.request_history[client_id]
        
        # Cleanup endpoint history
        endpoints_to_remove = []
        for endpoint_key, history in self.endpoint_history.items():
            # Remove old entries (use maximum period for safety)
            max_period = max(self.period, 3600)  # At least 1 hour
            while history and current_time - history[0] > max_period:
                history.popleft()
            
            # Mark empty histories for removal
            if not history:
                endpoints_to_remove.append(endpoint_key)
        
        # Remove empty endpoint histories
        for endpoint_key in endpoints_to_remove:
            del self.endpoint_history[endpoint_key]
        
        self.logger.debug(f"Rate limit cleanup completed. Removed {len(clients_to_remove)} client histories and {len(endpoints_to_remove)} endpoint histories.")
