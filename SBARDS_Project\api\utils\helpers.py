"""
Helper utilities for SBARDS API

This module provides helper functions for common operations.
"""

import os
import hashlib
from typing import Optional

from core.logger import get_global_logger
from core.utils import FileUtils

# Configure logging
logger = get_global_logger().get_layer_logger("api.utils.helpers")


def calculate_file_hash(file_path: str, algorithm: str = "sha256") -> Optional[str]:
    """
    Calculate hash of a file.
    
    Args:
        file_path (str): Path to the file.
        algorithm (str): Hash algorithm to use.
        
    Returns:
        str or None: File hash or None if error.
    """
    try:
        return FileUtils.get_file_hash(file_path, algorithm)
    except Exception as e:
        logger.error(f"Error calculating file hash: {e}")
        return None


def get_file_type(file_path: str) -> str:
    """
    Determine file type based on extension and content.
    
    Args:
        file_path (str): Path to the file.
        
    Returns:
        str: File type.
    """
    try:
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # Map extensions to types
        type_mapping = {
            '.exe': 'executable',
            '.dll': 'executable', 
            '.sys': 'executable',
            '.bat': 'script',
            '.cmd': 'script',
            '.ps1': 'script',
            '.pdf': 'document',
            '.doc': 'document',
            '.docx': 'document',
            '.xls': 'document',
            '.xlsx': 'document',
            '.zip': 'archive',
            '.rar': 'archive',
            '.7z': 'archive',
            '.tar': 'archive',
            '.gz': 'archive',
            '.jpg': 'image',
            '.jpeg': 'image',
            '.png': 'image',
            '.gif': 'image',
            '.mp3': 'audio',
            '.wav': 'audio',
            '.mp4': 'video',
            '.avi': 'video',
            '.txt': 'text',
            '.log': 'text',
            '.cfg': 'config',
            '.ini': 'config'
        }
        
        return type_mapping.get(file_ext, 'unknown')
        
    except Exception as e:
        logger.error(f"Error determining file type: {e}")
        return 'unknown'


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format.
    
    Args:
        size_bytes (int): Size in bytes.
        
    Returns:
        str: Formatted size.
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def is_suspicious_file_type(file_path: str) -> bool:
    """
    Check if file type is potentially suspicious.
    
    Args:
        file_path (str): Path to the file.
        
    Returns:
        bool: True if file type is suspicious.
    """
    file_type = get_file_type(file_path)
    suspicious_types = {'executable', 'script'}
    return file_type in suspicious_types
