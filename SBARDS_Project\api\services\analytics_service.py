"""
SBARDS Advanced Analytics Service
Comprehensive data analysis, trend detection, and reporting system
"""

import json
import logging
import sqlite3
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
import os

# Setup logger
logger = logging.getLogger("analytics_service")

@dataclass
class MetricPoint:
    """Single metric data point"""
    timestamp: datetime
    value: float
    metadata: Dict[str, Any] = None

@dataclass
class TrendAnalysis:
    """Trend analysis result"""
    metric_name: str
    trend_direction: str  # 'increasing', 'decreasing', 'stable'
    trend_strength: float  # 0.0 to 1.0
    prediction: float
    confidence: float
    analysis_period: str

@dataclass
class Report:
    """Analytics report"""
    report_id: str
    title: str
    report_type: str
    generated_at: datetime
    data: Dict[str, Any]
    summary: str

class AnalyticsService:
    """Advanced analytics service for SBARDS"""
    
    def __init__(self, db_path: str = "analytics.db"):
        self.db_path = db_path
        self.metrics_buffer: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.reports: List[Report] = []
        self.max_reports = 100
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database for analytics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    value REAL NOT NULL,
                    metadata TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    event_data TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    severity TEXT,
                    source TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create reports table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    report_id TEXT UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    report_type TEXT NOT NULL,
                    generated_at DATETIME NOT NULL,
                    data TEXT NOT NULL,
                    summary TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_metrics_name_time ON metrics(metric_name, timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_type_time ON events(event_type, timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_reports_type ON reports(report_type)')
            
            conn.commit()
            conn.close()
            logger.info("Analytics database initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing analytics database: {e}")
    
    def record_metric(self, metric_name: str, value: float, metadata: Dict[str, Any] = None):
        """Record a metric data point"""
        try:
            timestamp = datetime.now()
            point = MetricPoint(timestamp, value, metadata or {})
            
            # Add to buffer
            self.metrics_buffer[metric_name].append(point)
            
            # Save to database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                'INSERT INTO metrics (metric_name, timestamp, value, metadata) VALUES (?, ?, ?, ?)',
                (metric_name, timestamp, value, json.dumps(metadata or {}))
            )
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error recording metric {metric_name}: {e}")
    
    def record_event(self, event_type: str, event_data: Dict[str, Any], severity: str = "info", source: str = "system"):
        """Record an event"""
        try:
            timestamp = datetime.now()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                'INSERT INTO events (event_type, event_data, timestamp, severity, source) VALUES (?, ?, ?, ?, ?)',
                (event_type, json.dumps(event_data), timestamp, severity, source)
            )
            conn.commit()
            conn.close()
            
            logger.info(f"Event recorded: {event_type} ({severity})")
            
        except Exception as e:
            logger.error(f"Error recording event {event_type}: {e}")
    
    def get_metric_history(self, metric_name: str, hours: int = 24) -> List[MetricPoint]:
        """Get metric history for specified time period"""
        try:
            start_time = datetime.now() - timedelta(hours=hours)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                'SELECT timestamp, value, metadata FROM metrics WHERE metric_name = ? AND timestamp >= ? ORDER BY timestamp',
                (metric_name, start_time)
            )
            
            results = []
            for row in cursor.fetchall():
                timestamp = datetime.fromisoformat(row[0])
                value = row[1]
                metadata = json.loads(row[2]) if row[2] else {}
                results.append(MetricPoint(timestamp, value, metadata))
            
            conn.close()
            return results
            
        except Exception as e:
            logger.error(f"Error getting metric history for {metric_name}: {e}")
            return []
    
    def analyze_trend(self, metric_name: str, hours: int = 24) -> TrendAnalysis:
        """Analyze trend for a specific metric"""
        try:
            history = self.get_metric_history(metric_name, hours)
            
            if len(history) < 2:
                return TrendAnalysis(
                    metric_name=metric_name,
                    trend_direction="unknown",
                    trend_strength=0.0,
                    prediction=0.0,
                    confidence=0.0,
                    analysis_period=f"{hours}h"
                )
            
            values = [point.value for point in history]
            
            # Calculate trend using linear regression
            n = len(values)
            x_values = list(range(n))
            
            # Simple linear regression
            x_mean = statistics.mean(x_values)
            y_mean = statistics.mean(values)
            
            numerator = sum((x_values[i] - x_mean) * (values[i] - y_mean) for i in range(n))
            denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
            
            if denominator == 0:
                slope = 0
            else:
                slope = numerator / denominator
            
            # Determine trend direction and strength
            if abs(slope) < 0.01:
                direction = "stable"
                strength = 0.0
            elif slope > 0:
                direction = "increasing"
                strength = min(abs(slope) / (max(values) - min(values) + 0.001), 1.0)
            else:
                direction = "decreasing"
                strength = min(abs(slope) / (max(values) - min(values) + 0.001), 1.0)
            
            # Simple prediction (next value)
            intercept = y_mean - slope * x_mean
            prediction = slope * n + intercept
            
            # Confidence based on R-squared
            y_pred = [slope * x + intercept for x in x_values]
            ss_res = sum((values[i] - y_pred[i]) ** 2 for i in range(n))
            ss_tot = sum((values[i] - y_mean) ** 2 for i in range(n))
            
            if ss_tot == 0:
                confidence = 1.0
            else:
                r_squared = 1 - (ss_res / ss_tot)
                confidence = max(0.0, min(1.0, r_squared))
            
            return TrendAnalysis(
                metric_name=metric_name,
                trend_direction=direction,
                trend_strength=strength,
                prediction=prediction,
                confidence=confidence,
                analysis_period=f"{hours}h"
            )
            
        except Exception as e:
            logger.error(f"Error analyzing trend for {metric_name}: {e}")
            return TrendAnalysis(
                metric_name=metric_name,
                trend_direction="error",
                trend_strength=0.0,
                prediction=0.0,
                confidence=0.0,
                analysis_period=f"{hours}h"
            )
    
    def generate_system_report(self) -> Report:
        """Generate comprehensive system report"""
        try:
            report_id = f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Analyze key metrics
            cpu_trend = self.analyze_trend("cpu_usage", 24)
            memory_trend = self.analyze_trend("memory_usage", 24)
            files_trend = self.analyze_trend("files_processed", 24)
            threats_trend = self.analyze_trend("threats_detected", 24)
            
            # Get recent events
            recent_events = self.get_recent_events(24)
            
            # Calculate statistics
            cpu_history = self.get_metric_history("cpu_usage", 24)
            memory_history = self.get_metric_history("memory_usage", 24)
            
            cpu_avg = statistics.mean([p.value for p in cpu_history]) if cpu_history else 0
            memory_avg = statistics.mean([p.value for p in memory_history]) if memory_history else 0
            
            # Generate summary
            summary_parts = []
            
            if cpu_trend.trend_direction == "increasing" and cpu_trend.trend_strength > 0.5:
                summary_parts.append("CPU usage is trending upward")
            
            if memory_trend.trend_direction == "increasing" and memory_trend.trend_strength > 0.5:
                summary_parts.append("Memory usage is trending upward")
            
            if threats_trend.trend_direction == "increasing":
                summary_parts.append("Threat detection activity is increasing")
            
            if not summary_parts:
                summary_parts.append("System performance is stable")
            
            summary = ". ".join(summary_parts) + "."
            
            report_data = {
                "analysis_period": "24 hours",
                "trends": {
                    "cpu_usage": {
                        "direction": cpu_trend.trend_direction,
                        "strength": cpu_trend.trend_strength,
                        "prediction": cpu_trend.prediction,
                        "confidence": cpu_trend.confidence
                    },
                    "memory_usage": {
                        "direction": memory_trend.trend_direction,
                        "strength": memory_trend.trend_strength,
                        "prediction": memory_trend.prediction,
                        "confidence": memory_trend.confidence
                    },
                    "files_processed": {
                        "direction": files_trend.trend_direction,
                        "strength": files_trend.trend_strength,
                        "prediction": files_trend.prediction,
                        "confidence": files_trend.confidence
                    },
                    "threats_detected": {
                        "direction": threats_trend.trend_direction,
                        "strength": threats_trend.trend_strength,
                        "prediction": threats_trend.prediction,
                        "confidence": threats_trend.confidence
                    }
                },
                "statistics": {
                    "avg_cpu_usage": round(cpu_avg, 2),
                    "avg_memory_usage": round(memory_avg, 2),
                    "total_events": len(recent_events),
                    "critical_events": len([e for e in recent_events if e.get("severity") == "critical"]),
                    "warning_events": len([e for e in recent_events if e.get("severity") == "warning"])
                },
                "recent_events": recent_events[:10],  # Last 10 events
                "recommendations": self._generate_recommendations(cpu_trend, memory_trend, threats_trend)
            }
            
            report = Report(
                report_id=report_id,
                title="System Performance Report",
                report_type="system",
                generated_at=datetime.now(),
                data=report_data,
                summary=summary
            )
            
            # Save report
            self._save_report(report)
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating system report: {e}")
            return Report(
                report_id="error_report",
                title="Error Report",
                report_type="error",
                generated_at=datetime.now(),
                data={"error": str(e)},
                summary="Error generating report"
            )
    
    def _generate_recommendations(self, cpu_trend: TrendAnalysis, memory_trend: TrendAnalysis, threats_trend: TrendAnalysis) -> List[str]:
        """Generate recommendations based on trends"""
        recommendations = []
        
        if cpu_trend.trend_direction == "increasing" and cpu_trend.trend_strength > 0.7:
            recommendations.append("Consider optimizing CPU-intensive processes or scaling resources")
        
        if memory_trend.trend_direction == "increasing" and memory_trend.trend_strength > 0.7:
            recommendations.append("Monitor memory usage closely and consider increasing available memory")
        
        if threats_trend.trend_direction == "increasing":
            recommendations.append("Review security policies and update threat detection rules")
        
        if cpu_trend.prediction > 90:
            recommendations.append("CPU usage predicted to exceed 90% - immediate attention required")
        
        if memory_trend.prediction > 90:
            recommendations.append("Memory usage predicted to exceed 90% - immediate attention required")
        
        if not recommendations:
            recommendations.append("System performance is within normal parameters")
        
        return recommendations
    
    def get_recent_events(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get recent events"""
        try:
            start_time = datetime.now() - timedelta(hours=hours)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                'SELECT event_type, event_data, timestamp, severity, source FROM events WHERE timestamp >= ? ORDER BY timestamp DESC',
                (start_time,)
            )
            
            events = []
            for row in cursor.fetchall():
                events.append({
                    "event_type": row[0],
                    "event_data": json.loads(row[1]),
                    "timestamp": row[2],
                    "severity": row[3],
                    "source": row[4]
                })
            
            conn.close()
            return events
            
        except Exception as e:
            logger.error(f"Error getting recent events: {e}")
            return []
    
    def _save_report(self, report: Report):
        """Save report to database and memory"""
        try:
            # Save to database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                'INSERT INTO reports (report_id, title, report_type, generated_at, data, summary) VALUES (?, ?, ?, ?, ?, ?)',
                (report.report_id, report.title, report.report_type, report.generated_at, json.dumps(report.data), report.summary)
            )
            conn.commit()
            conn.close()
            
            # Add to memory
            self.reports.append(report)
            
            # Limit memory reports
            if len(self.reports) > self.max_reports:
                self.reports = self.reports[-self.max_reports:]
            
            logger.info(f"Report saved: {report.report_id}")
            
        except Exception as e:
            logger.error(f"Error saving report: {e}")
    
    def get_reports(self, report_type: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get reports"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if report_type:
                cursor.execute(
                    'SELECT report_id, title, report_type, generated_at, summary FROM reports WHERE report_type = ? ORDER BY generated_at DESC LIMIT ?',
                    (report_type, limit)
                )
            else:
                cursor.execute(
                    'SELECT report_id, title, report_type, generated_at, summary FROM reports ORDER BY generated_at DESC LIMIT ?',
                    (limit,)
                )
            
            reports = []
            for row in cursor.fetchall():
                reports.append({
                    "report_id": row[0],
                    "title": row[1],
                    "report_type": row[2],
                    "generated_at": row[3],
                    "summary": row[4]
                })
            
            conn.close()
            return reports
            
        except Exception as e:
            logger.error(f"Error getting reports: {e}")
            return []
    
    def get_report_details(self, report_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed report data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                'SELECT title, report_type, generated_at, data, summary FROM reports WHERE report_id = ?',
                (report_id,)
            )
            
            row = cursor.fetchone()
            if row:
                return {
                    "report_id": report_id,
                    "title": row[0],
                    "report_type": row[1],
                    "generated_at": row[2],
                    "data": json.loads(row[3]),
                    "summary": row[4]
                }
            
            conn.close()
            return None
            
        except Exception as e:
            logger.error(f"Error getting report details: {e}")
            return None

# Global analytics service instance
analytics_service = AnalyticsService()
