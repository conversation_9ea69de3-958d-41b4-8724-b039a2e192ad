"""
SBARDS Advanced Cache Manager
High-performance caching system with multiple backends and intelligent eviction
"""

import json
import time
import hashlib
import logging
import threading
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass
from collections import OrderedDict
import pickle
import os

# Setup logger
logger = logging.getLogger("cache_manager")

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int
    last_accessed: datetime
    size_bytes: int
    tags: List[str]

class LRUCache:
    """Least Recently Used cache implementation"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            if key in self.cache:
                # Move to end (most recently used)
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
            return None
    
    def set(self, key: str, value: Any) -> None:
        with self.lock:
            if key in self.cache:
                # Update existing
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # Remove least recently used
                self.cache.popitem(last=False)
            
            self.cache[key] = value
    
    def delete(self, key: str) -> bool:
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
    
    def size(self) -> int:
        return len(self.cache)

class TTLCache:
    """Time-To-Live cache implementation"""
    
    def __init__(self, default_ttl: int = 3600):
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheEntry] = {}
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                
                # Check if expired
                if entry.expires_at and datetime.now() > entry.expires_at:
                    del self.cache[key]
                    return None
                
                # Update access info
                entry.access_count += 1
                entry.last_accessed = datetime.now()
                
                return entry.value
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        with self.lock:
            ttl = ttl or self.default_ttl
            expires_at = datetime.now() + timedelta(seconds=ttl) if ttl > 0 else None
            
            # Calculate size
            try:
                size_bytes = len(pickle.dumps(value))
            except:
                size_bytes = len(str(value).encode('utf-8'))
            
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at,
                access_count=0,
                last_accessed=datetime.now(),
                size_bytes=size_bytes,
                tags=[]
            )
            
            self.cache[key] = entry
    
    def delete(self, key: str) -> bool:
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
    
    def cleanup_expired(self) -> int:
        """Remove expired entries"""
        with self.lock:
            now = datetime.now()
            expired_keys = [
                key for key, entry in self.cache.items()
                if entry.expires_at and now > entry.expires_at
            ]
            
            for key in expired_keys:
                del self.cache[key]
            
            return len(expired_keys)
    
    def size(self) -> int:
        return len(self.cache)

class CacheManager:
    """Advanced cache manager with multiple backends and strategies"""
    
    def __init__(self):
        self.memory_cache = TTLCache(default_ttl=3600)  # 1 hour default
        self.lru_cache = LRUCache(max_size=1000)
        self.file_cache_dir = "cache"
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "evictions": 0
        }
        self.lock = threading.RLock()
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
        
        # Create cache directory
        os.makedirs(self.file_cache_dir, exist_ok=True)
        
        # Start cleanup thread
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self):
        """Start background cleanup thread"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self.cleanup_interval)
                    self._cleanup_expired()
                except Exception as e:
                    logger.error(f"Error in cleanup thread: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_expired(self):
        """Clean up expired cache entries"""
        try:
            expired_count = self.memory_cache.cleanup_expired()
            if expired_count > 0:
                self.stats["evictions"] += expired_count
                logger.info(f"Cleaned up {expired_count} expired cache entries")
        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")
    
    def _generate_key(self, key: str, namespace: str = "default") -> str:
        """Generate cache key with namespace"""
        return f"{namespace}:{key}"
    
    def _hash_key(self, key: str) -> str:
        """Generate hash for complex keys"""
        if isinstance(key, (dict, list, tuple)):
            key = json.dumps(key, sort_keys=True)
        return hashlib.md5(str(key).encode()).hexdigest()
    
    def get(self, key: str, namespace: str = "default") -> Optional[Any]:
        """Get value from cache"""
        try:
            cache_key = self._generate_key(key, namespace)
            
            # Try memory cache first
            value = self.memory_cache.get(cache_key)
            if value is not None:
                self.stats["hits"] += 1
                return value
            
            # Try LRU cache
            value = self.lru_cache.get(cache_key)
            if value is not None:
                self.stats["hits"] += 1
                return value
            
            # Try file cache
            value = self._get_from_file(cache_key)
            if value is not None:
                # Promote to memory cache
                self.memory_cache.set(cache_key, value)
                self.stats["hits"] += 1
                return value
            
            self.stats["misses"] += 1
            return None
            
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            self.stats["misses"] += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, namespace: str = "default", 
            persist: bool = False) -> bool:
        """Set value in cache"""
        try:
            cache_key = self._generate_key(key, namespace)
            
            # Set in memory cache
            self.memory_cache.set(cache_key, value, ttl)
            
            # Set in LRU cache for frequently accessed items
            self.lru_cache.set(cache_key, value)
            
            # Optionally persist to file
            if persist:
                self._set_to_file(cache_key, value, ttl)
            
            self.stats["sets"] += 1
            return True
            
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False
    
    def delete(self, key: str, namespace: str = "default") -> bool:
        """Delete value from cache"""
        try:
            cache_key = self._generate_key(key, namespace)
            
            # Delete from all caches
            deleted = False
            deleted |= self.memory_cache.delete(cache_key)
            deleted |= self.lru_cache.delete(cache_key)
            deleted |= self._delete_from_file(cache_key)
            
            if deleted:
                self.stats["deletes"] += 1
            
            return deleted
            
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False
    
    def clear(self, namespace: str = None) -> bool:
        """Clear cache entries"""
        try:
            if namespace:
                # Clear specific namespace
                prefix = f"{namespace}:"
                
                # Clear from memory cache
                keys_to_delete = [k for k in self.memory_cache.cache.keys() if k.startswith(prefix)]
                for key in keys_to_delete:
                    self.memory_cache.delete(key)
                
                # Clear from LRU cache
                keys_to_delete = [k for k in self.lru_cache.cache.keys() if k.startswith(prefix)]
                for key in keys_to_delete:
                    self.lru_cache.delete(key)
                
                # Clear from file cache
                self._clear_file_namespace(namespace)
            else:
                # Clear all caches
                self.memory_cache.clear()
                self.lru_cache.clear()
                self._clear_all_files()
            
            return True
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False
    
    def _get_from_file(self, key: str) -> Optional[Any]:
        """Get value from file cache"""
        try:
            file_path = os.path.join(self.file_cache_dir, f"{self._hash_key(key)}.cache")
            
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
            
            # Check expiration
            if data.get('expires_at') and datetime.now() > data['expires_at']:
                os.remove(file_path)
                return None
            
            return data['value']
            
        except Exception as e:
            logger.error(f"Error reading file cache for key {key}: {e}")
            return None
    
    def _set_to_file(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value to file cache"""
        try:
            file_path = os.path.join(self.file_cache_dir, f"{self._hash_key(key)}.cache")
            
            expires_at = None
            if ttl and ttl > 0:
                expires_at = datetime.now() + timedelta(seconds=ttl)
            
            data = {
                'key': key,
                'value': value,
                'created_at': datetime.now(),
                'expires_at': expires_at
            }
            
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            
            return True
            
        except Exception as e:
            logger.error(f"Error writing file cache for key {key}: {e}")
            return False
    
    def _delete_from_file(self, key: str) -> bool:
        """Delete value from file cache"""
        try:
            file_path = os.path.join(self.file_cache_dir, f"{self._hash_key(key)}.cache")
            
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting file cache for key {key}: {e}")
            return False
    
    def _clear_file_namespace(self, namespace: str):
        """Clear file cache for specific namespace"""
        try:
            prefix = f"{namespace}:"
            for filename in os.listdir(self.file_cache_dir):
                if filename.endswith('.cache'):
                    file_path = os.path.join(self.file_cache_dir, filename)
                    try:
                        with open(file_path, 'rb') as f:
                            data = pickle.load(f)
                        
                        if data.get('key', '').startswith(prefix):
                            os.remove(file_path)
                    except:
                        continue
        except Exception as e:
            logger.error(f"Error clearing file namespace {namespace}: {e}")
    
    def _clear_all_files(self):
        """Clear all file cache"""
        try:
            for filename in os.listdir(self.file_cache_dir):
                if filename.endswith('.cache'):
                    file_path = os.path.join(self.file_cache_dir, filename)
                    os.remove(file_path)
        except Exception as e:
            logger.error(f"Error clearing all file cache: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "hits": self.stats["hits"],
            "misses": self.stats["misses"],
            "hit_rate": round(hit_rate, 2),
            "sets": self.stats["sets"],
            "deletes": self.stats["deletes"],
            "evictions": self.stats["evictions"],
            "memory_cache_size": self.memory_cache.size(),
            "lru_cache_size": self.lru_cache.size(),
            "total_requests": total_requests
        }
    
    def cache_decorator(self, ttl: int = 3600, namespace: str = "default", persist: bool = False):
        """Decorator for caching function results"""
        def decorator(func: Callable):
            def wrapper(*args, **kwargs):
                # Generate cache key from function name and arguments
                key_data = {
                    "func": func.__name__,
                    "args": args,
                    "kwargs": kwargs
                }
                cache_key = self._hash_key(key_data)
                
                # Try to get from cache
                result = self.get(cache_key, namespace)
                if result is not None:
                    return result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl, namespace, persist)
                
                return result
            
            return wrapper
        return decorator
    
    def invalidate_pattern(self, pattern: str, namespace: str = "default") -> int:
        """Invalidate cache entries matching pattern"""
        try:
            prefix = self._generate_key(pattern, namespace)
            count = 0
            
            # Invalidate from memory cache
            keys_to_delete = [k for k in self.memory_cache.cache.keys() if pattern in k]
            for key in keys_to_delete:
                self.memory_cache.delete(key)
                count += 1
            
            # Invalidate from LRU cache
            keys_to_delete = [k for k in self.lru_cache.cache.keys() if pattern in k]
            for key in keys_to_delete:
                self.lru_cache.delete(key)
                count += 1
            
            return count
            
        except Exception as e:
            logger.error(f"Error invalidating pattern {pattern}: {e}")
            return 0

# Global cache manager instance
cache_manager = CacheManager()
