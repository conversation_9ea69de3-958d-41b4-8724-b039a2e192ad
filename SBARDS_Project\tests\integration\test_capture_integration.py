#!/usr/bin/env python3
"""
Integration Tests for SBARDS Capture Layer

This module provides comprehensive integration tests for the capture layer,
testing the interaction between C++ and Python components.

Features:
- C++/Python integration testing
- Performance benchmarking
- File monitoring validation
- Permission analysis verification
- Redis queue testing
"""

import os
import sys
import time
import tempfile
import threading
import unittest
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(project_root))

from core.logger import setup_logging
from core.utils import FileUtils, PerformanceUtils
from capture.python.file_interceptor import FileInterceptor, FileInterceptionEvent
from capture.python.redis_queue import RedisQueueManager

class CaptureLayerIntegrationTest(unittest.TestCase):
    """Integration tests for the capture layer."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.logger = setup_logging().get_logger("CaptureIntegrationTest")
        cls.test_dir = Path(tempfile.mkdtemp(prefix="sbards_test_"))
        cls.sample_files = []
        
        # Create test files
        cls._create_test_files()
        
        cls.logger.info(f"Test environment set up in {cls.test_dir}")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment."""
        import shutil
        if cls.test_dir.exists():
            shutil.rmtree(cls.test_dir)
        cls.logger.info("Test environment cleaned up")
    
    @classmethod
    def _create_test_files(cls):
        """Create test files for scanning."""
        test_files = [
            ("normal_file.txt", "This is a normal text file."),
            ("suspicious_file.exe", b"\x4D\x5A" + b"A" * 1000),  # PE header + data
            ("large_file.dat", "X" * (5 * 1024 * 1024)),  # 5MB file
            ("script_file.py", "print('Hello, World!')"),
            ("config_file.json", '{"test": true}'),
            ("hidden_file.tmp", "Temporary data"),
        ]
        
        for filename, content in test_files:
            file_path = cls.test_dir / filename
            
            if isinstance(content, str):
                file_path.write_text(content)
            else:
                file_path.write_bytes(content)
            
            cls.sample_files.append(str(file_path))
        
        # Create subdirectory with files
        subdir = cls.test_dir / "subdir"
        subdir.mkdir()
        
        subdir_file = subdir / "nested_file.txt"
        subdir_file.write_text("Nested file content")
        cls.sample_files.append(str(subdir_file))
    
    def setUp(self):
        """Set up each test."""
        self.config = {
            "target_directories": [str(self.test_dir)],
            "recursive": True,
            "max_depth": 5,
            "exclude_dirs": [".git", "__pycache__"],
            "exclude_extensions": [".log"],
            "max_file_size_mb": 100,
            "threads": 2,
            "enable_real_time": False  # Disable for testing
        }
    
    def test_file_interceptor_initialization(self):
        """Test file interceptor initialization."""
        self.logger.info("Testing file interceptor initialization...")
        
        interceptor = FileInterceptor(self.config)
        
        # Check configuration
        self.assertEqual(interceptor.target_directories, [str(self.test_dir)])
        self.assertTrue(interceptor.recursive)
        self.assertEqual(interceptor.max_depth, 5)
        
        self.logger.info("✅ File interceptor initialization test passed")
    
    def test_directory_scanning(self):
        """Test directory scanning functionality."""
        self.logger.info("Testing directory scanning...")
        
        interceptor = FileInterceptor(self.config)
        
        # Perform scan
        start_time = time.time()
        events = interceptor.scan_directories()
        scan_time = time.time() - start_time
        
        # Validate results
        self.assertGreater(len(events), 0, "Should find at least some files")
        
        # Check that we found our test files
        found_files = {event.file_path for event in events}
        
        for sample_file in self.sample_files:
            if not sample_file.endswith('.tmp'):  # tmp files are excluded
                self.assertIn(sample_file, found_files, 
                            f"Should find test file: {sample_file}")
        
        # Check event properties
        for event in events:
            self.assertIsInstance(event, FileInterceptionEvent)
            self.assertTrue(os.path.exists(event.file_path))
            self.assertGreater(event.file_size, 0)
            self.assertIsInstance(event.is_suspicious, bool)
            self.assertIn(event.priority, [1, 2, 3, 4])
        
        self.logger.info(f"✅ Directory scanning test passed: "
                        f"{len(events)} files found in {scan_time:.3f}s")
    
    def test_suspicious_file_detection(self):
        """Test suspicious file detection."""
        self.logger.info("Testing suspicious file detection...")
        
        interceptor = FileInterceptor(self.config)
        events = interceptor.scan_directories()
        
        # Find suspicious files
        suspicious_events = [e for e in events if e.is_suspicious]
        
        # Should detect the .exe file as suspicious
        exe_files = [e for e in suspicious_events if e.file_path.endswith('.exe')]
        self.assertGreater(len(exe_files), 0, "Should detect .exe files as suspicious")
        
        # Check priority assignment
        for event in suspicious_events:
            self.assertGreaterEqual(event.priority, 2, 
                                  "Suspicious files should have higher priority")
        
        self.logger.info(f"✅ Suspicious file detection test passed: "
                        f"{len(suspicious_events)} suspicious files detected")
    
    def test_file_filtering(self):
        """Test file filtering functionality."""
        self.logger.info("Testing file filtering...")
        
        # Configure with exclusions
        config_with_exclusions = self.config.copy()
        config_with_exclusions["exclude_extensions"] = [".tmp", ".dat"]
        config_with_exclusions["max_file_size_mb"] = 1  # Exclude large files
        
        interceptor = FileInterceptor(config_with_exclusions)
        events = interceptor.scan_directories()
        
        # Check that excluded files are not present
        found_extensions = {Path(e.file_path).suffix for e in events}
        
        self.assertNotIn(".tmp", found_extensions, "Should exclude .tmp files")
        self.assertNotIn(".dat", found_extensions, "Should exclude .dat files")
        
        # Check file sizes
        for event in events:
            size_mb = event.file_size / (1024 * 1024)
            self.assertLessEqual(size_mb, 1.0, "Should exclude files larger than 1MB")
        
        self.logger.info("✅ File filtering test passed")
    
    def test_performance_benchmarking(self):
        """Test performance of file scanning."""
        self.logger.info("Testing performance benchmarking...")
        
        interceptor = FileInterceptor(self.config)
        
        # Perform multiple scans to get average performance
        scan_times = []
        file_counts = []
        
        for i in range(3):
            start_time = time.time()
            events = interceptor.scan_directories()
            scan_time = time.time() - start_time
            
            scan_times.append(scan_time)
            file_counts.append(len(events))
        
        avg_scan_time = sum(scan_times) / len(scan_times)
        avg_file_count = sum(file_counts) / len(file_counts)
        
        if avg_scan_time > 0:
            files_per_second = avg_file_count / avg_scan_time
        else:
            files_per_second = float('inf')
        
        self.logger.info(f"Performance metrics:")
        self.logger.info(f"  Average scan time: {avg_scan_time:.3f}s")
        self.logger.info(f"  Average files found: {avg_file_count:.1f}")
        self.logger.info(f"  Processing rate: {files_per_second:.1f} files/second")
        
        # Performance assertions
        self.assertLess(avg_scan_time, 5.0, "Scan should complete within 5 seconds")
        self.assertGreater(files_per_second, 1.0, "Should process at least 1 file per second")
        
        self.logger.info("✅ Performance benchmarking test passed")
    
    def test_threaded_processing(self):
        """Test threaded file processing."""
        self.logger.info("Testing threaded processing...")
        
        # Enable threading
        config_threaded = self.config.copy()
        config_threaded["threads"] = 4
        
        interceptor = FileInterceptor(config_threaded)
        
        # Track processed files
        processed_files = []
        processing_lock = threading.Lock()
        
        def file_callback(event: FileInterceptionEvent):
            with processing_lock:
                processed_files.append(event.file_path)
        
        interceptor.add_callback(file_callback)
        
        # Start interceptor
        self.assertTrue(interceptor.start(), "Should start successfully")
        
        try:
            # Perform scan
            events = interceptor.scan_directories()
            
            # Process events
            for event in events:
                interceptor.intercept_file(event.file_path, "test")
            
            # Wait for processing
            time.sleep(2)
            
            # Check results
            with processing_lock:
                self.assertGreater(len(processed_files), 0, 
                                 "Should process at least some files")
        
        finally:
            interceptor.stop()
        
        self.logger.info(f"✅ Threaded processing test passed: "
                        f"{len(processed_files)} files processed")
    
    @unittest.skipIf(not hasattr(sys.modules.get('capture.python.redis_queue', None), 'REDIS_AVAILABLE') 
                     or not getattr(sys.modules.get('capture.python.redis_queue', None), 'REDIS_AVAILABLE', False),
                     "Redis not available")
    def test_redis_queue_integration(self):
        """Test Redis queue integration."""
        self.logger.info("Testing Redis queue integration...")
        
        try:
            # Configure Redis queue
            redis_config = {
                "redis_host": "localhost",
                "redis_port": 6379,
                "redis_db": 15,  # Use test database
                "queue_prefix": "sbards_test",
                "max_retries": 2
            }
            
            queue_manager = RedisQueueManager(redis_config)
            
            if not queue_manager.is_available():
                self.skipTest("Redis server not available")
            
            # Clear test queues
            queue_manager.clear_queues()
            
            # Test message sending
            test_event = FileInterceptionEvent(
                file_path="/test/file.txt",
                event_type="test",
                timestamp=time.time(),
                file_size=1024,
                file_extension=".txt",
                is_suspicious=False,
                priority=1
            )
            
            # Send event
            success = queue_manager.send_file_event(test_event, "normal")
            self.assertTrue(success, "Should send event successfully")
            
            # Check queue sizes
            sizes = queue_manager.get_queue_sizes()
            self.assertGreater(sizes.get("normal_priority", 0), 0, 
                             "Should have message in normal priority queue")
            
            self.logger.info("✅ Redis queue integration test passed")
            
        except Exception as e:
            self.skipTest(f"Redis test failed: {e}")
    
    def test_error_handling(self):
        """Test error handling in various scenarios."""
        self.logger.info("Testing error handling...")
        
        # Test with non-existent directory
        bad_config = self.config.copy()
        bad_config["target_directories"] = ["/non/existent/directory"]
        
        interceptor = FileInterceptor(bad_config)
        events = interceptor.scan_directories()
        
        # Should handle gracefully
        self.assertEqual(len(events), 0, "Should return empty list for non-existent directory")
        
        # Test with permission denied (simulate)
        # This would require platform-specific setup
        
        self.logger.info("✅ Error handling test passed")
    
    def test_memory_usage(self):
        """Test memory usage during processing."""
        self.logger.info("Testing memory usage...")
        
        try:
            import psutil
            process = psutil.Process()
            
            # Get initial memory usage
            initial_memory = process.memory_info().rss
            
            interceptor = FileInterceptor(self.config)
            
            # Perform multiple scans
            for _ in range(10):
                events = interceptor.scan_directories()
            
            # Get final memory usage
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            self.logger.info(f"Memory usage:")
            self.logger.info(f"  Initial: {PerformanceUtils.format_bytes(initial_memory)}")
            self.logger.info(f"  Final: {PerformanceUtils.format_bytes(final_memory)}")
            self.logger.info(f"  Increase: {PerformanceUtils.format_bytes(memory_increase)}")
            
            # Memory should not increase excessively
            max_increase = 50 * 1024 * 1024  # 50MB
            self.assertLess(memory_increase, max_increase, 
                          f"Memory increase should be less than {PerformanceUtils.format_bytes(max_increase)}")
            
            self.logger.info("✅ Memory usage test passed")
            
        except ImportError:
            self.skipTest("psutil not available for memory testing")

def run_integration_tests():
    """Run all integration tests."""
    # Set up test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(CaptureLayerIntegrationTest)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "="*60)
    print("INTEGRATION TEST SUMMARY")
    print("="*60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nResult: {'✅ PASSED' if success else '❌ FAILED'}")
    print("="*60)
    
    return success

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
