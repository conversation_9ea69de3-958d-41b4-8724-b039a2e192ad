2025-05-26 05:50:18,961 - layer.main - INFO - [run.py:379] - Running api layer only
2025-05-26 05:50:18,962 - layer.main - INFO - [run.py:77] - Initializing layers: ['api']
2025-05-26 05:50:18,963 - layer.main - INFO - [run.py:117] - Initializing api layer...
2025-05-26 05:50:22,231 - analytics_service - INFO - [analytics_service.py:108] - Analytics database initialized successfully
2025-05-26 05:50:22,265 - layer.api - INFO - [main.py:136] - Analytics router included successfully
2025-05-26 05:50:22,295 - notification_service - INFO - [notification_service.py:173] - Added notification rule: High CPU Usage
2025-05-26 05:50:22,296 - notification_service - INFO - [notification_service.py:173] - Added notification rule: High Memory Usage
2025-05-26 05:50:22,297 - notification_service - INFO - [notification_service.py:173] - Added notification rule: Threat Detected
2025-05-26 05:50:22,297 - notification_service - INFO - [notification_service.py:173] - Added notification rule: Capture Layer Down
2025-05-26 05:50:22,298 - notification_service - INFO - [notification_service.py:173] - Added notification rule: System Health Critical
2025-05-26 05:50:22,335 - layer.api - INFO - [main.py:146] - Notifications router included successfully
2025-05-26 05:50:22,342 - layer.main - INFO - [run.py:306] - Initializing API layer with FastAPI...
2025-05-26 05:50:22,343 - layer.main - INFO - [run.py:148] - Initializing True Capture Layer components...
2025-05-26 05:50:22,540 - layer.capture - INFO - [true_file_interceptor.py:240] - TrueFileInterceptor initialized
2025-05-26 05:50:22,541 - layer.capture - INFO - [integrated_capture_layer.py:64] - IntegratedCaptureLayer initialized
2025-05-26 05:50:22,542 - layer.capture - INFO - [true_file_interceptor.py:245] - Static analysis callback registered
2025-05-26 05:50:22,543 - layer.capture - INFO - [integrated_capture_layer.py:70] - Static analysis callback registered
2025-05-26 05:50:22,544 - layer.capture - INFO - [integrated_capture_layer.py:125] - Starting Integrated Capture Layer...
2025-05-26 05:50:22,545 - layer.capture - INFO - [cpp_integration.py:216] - Mock C++ interceptor initialized
2025-05-26 05:50:22,546 - layer.capture - INFO - [cpp_integration.py:221] - Mock Python callback registered
2025-05-26 05:50:22,547 - layer.capture - INFO - [true_file_interceptor.py:322] - Worker loop started
2025-05-26 05:50:22,548 - layer.capture - INFO - [true_file_interceptor.py:260] - TrueFileInterceptor started successfully
2025-05-26 05:50:22,549 - layer.capture - INFO - [cpp_integration.py:225] - Mock C++ interceptor started
2025-05-26 05:50:22,551 - layer.capture - INFO - [integrated_capture_layer.py:149] - Integrated Capture Layer started successfully
2025-05-26 05:50:22,551 - layer.main - INFO - [run.py:181] - True Capture Layer started successfully
2025-05-26 05:50:22,552 - layer.main - INFO - [run.py:231] - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 05:50:22,553 - layer.main - INFO - [run.py:332] - API layer initialized successfully
2025-05-26 05:50:22,554 - layer.main - INFO - [run.py:82] - Successfully initialized api layer
2025-05-26 05:50:22,554 - layer.main - INFO - [run.py:389] - api layer is running...
2025-05-26 05:50:22,555 - layer.main - INFO - [run.py:421] - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 05:50:22,555 - layer.main - INFO - [run.py:422] - ============================================================
2025-05-26 05:50:22,555 - layer.main - INFO - [run.py:423] - Available Endpoints:
2025-05-26 05:50:22,556 - layer.main - INFO - [run.py:427] -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 05:50:22,557 - layer.main - INFO - [run.py:427] -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 05:50:22,557 - layer.main - INFO - [run.py:427] -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 05:50:22,558 - layer.main - INFO - [run.py:427] -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 05:50:22,558 - layer.main - INFO - [run.py:427] -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 05:50:22,559 - layer.main - INFO - [run.py:427] -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 05:50:22,560 - layer.main - INFO - [run.py:429] - ============================================================
2025-05-26 05:50:22,560 - layer.main - INFO - [run.py:430] - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 05:50:22,561 - layer.main - INFO - [run.py:431] - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 05:50:22,631 - layer.api - INFO - [main.py:155] - Starting SBARDS API server...
2025-05-26 05:50:22,846 - layer.capture - INFO - [true_file_interceptor.py:240] - TrueFileInterceptor initialized
2025-05-26 05:50:22,847 - layer.capture - INFO - [integrated_capture_layer.py:64] - IntegratedCaptureLayer initialized
2025-05-26 05:50:22,847 - layer.capture - INFO - [true_file_interceptor.py:245] - Static analysis callback registered
2025-05-26 05:50:22,849 - layer.capture - INFO - [integrated_capture_layer.py:70] - Static analysis callback registered
2025-05-26 05:50:22,850 - layer.capture - INFO - [integrated_capture_layer.py:125] - Starting Integrated Capture Layer...
2025-05-26 05:50:22,851 - layer.capture - INFO - [cpp_integration.py:216] - Mock C++ interceptor initialized
2025-05-26 05:50:22,851 - layer.capture - INFO - [cpp_integration.py:221] - Mock Python callback registered
2025-05-26 05:50:22,852 - layer.capture - INFO - [true_file_interceptor.py:322] - Worker loop started
2025-05-26 05:50:22,853 - layer.capture - INFO - [true_file_interceptor.py:260] - TrueFileInterceptor started successfully
2025-05-26 05:50:22,854 - layer.capture - INFO - [cpp_integration.py:225] - Mock C++ interceptor started
2025-05-26 05:50:22,855 - layer.capture - INFO - [integrated_capture_layer.py:149] - Integrated Capture Layer started successfully
2025-05-26 05:50:22,855 - layer.api_capture - INFO - [capture.py:144] - True Capture Layer initialized successfully
2025-05-26 05:50:22,856 - layer.api_capture - WARNING - [capture.py:179] - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 05:50:22,857 - layer.api - INFO - [main.py:161] - Capture layer initialized successfully on startup
2025-05-26 05:50:22,858 - layer.api - INFO - [main.py:729] - Notification service initialized with WebSocket manager
2025-05-26 05:50:23,999 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
2025-05-26 05:50:24,551 - layer.capture - INFO - [integrated_capture_layer.py:95] - File intercepted by C++: /tmp/mock_intercepted_file.txt
2025-05-26 05:50:24,646 - layer.capture - INFO - [true_file_interceptor.py:125] - File stored securely: 20250526_055024_553945_mock_file.txt (hash: 65c92b167a9717fa...)
2025-05-26 05:50:24,647 - layer.capture - INFO - [true_file_interceptor.py:313] - File intercepted: mock_file.txt -> capture\temp_storage\incoming\20250526_055024_553945_mock_file.txt
2025-05-26 05:50:24,647 - layer.capture - INFO - [integrated_capture_layer.py:110] - File passed to Python processing: 65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699
2025-05-26 05:50:24,648 - layer.main - INFO - [run.py:172] - Static analysis request for: capture\temp_storage\incoming\20250526_055024_553945_mock_file.txt
2025-05-26 05:50:24,652 - layer.capture - ERROR - [true_file_interceptor.py:157] - Failed to restore file to /home/<USER>/Downloads/mock_file.txt: [Errno 13] Permission denied: '/home/<USER>/Downloads/mock_file.txt'
2025-05-26 05:50:24,653 - layer.capture - WARNING - [true_file_interceptor.py:402] - File quarantined: capture\temp_storage\incoming\20250526_055024_553945_mock_file.txt - Reason: Failed to restore
2025-05-26 05:50:24,654 - layer.capture - INFO - [true_file_interceptor.py:422] - Quarantine notification: {'type': 'quarantine', 'original_filename': 'mock_file.txt', 'file_hash': '65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699', 'reason': 'Failed to restore', 'quarantine_path': 'capture\\temp_storage\\quarantine\\20250526_055024_553945_mock_file.txt', 'timestamp': '2025-05-26T05:50:24.654047'}
2025-05-26 05:50:24,855 - layer.capture - INFO - [integrated_capture_layer.py:95] - File intercepted by C++: /tmp/mock_intercepted_file.txt
2025-05-26 05:50:24,964 - layer.capture - INFO - [true_file_interceptor.py:125] - File stored securely: 20250526_055024_856087_mock_file.txt (hash: 65c92b167a9717fa...)
2025-05-26 05:50:24,965 - layer.capture - INFO - [true_file_interceptor.py:313] - File intercepted: mock_file.txt -> capture\temp_storage\incoming\20250526_055024_856087_mock_file.txt
2025-05-26 05:50:24,966 - layer.capture - INFO - [integrated_capture_layer.py:110] - File passed to Python processing: 65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699
2025-05-26 05:50:24,966 - layer.api_capture - INFO - [capture.py:135] - Static analysis request for: capture\temp_storage\incoming\20250526_055024_856087_mock_file.txt
2025-05-26 05:50:24,971 - layer.capture - ERROR - [true_file_interceptor.py:157] - Failed to restore file to /home/<USER>/Downloads/mock_file.txt: [Errno 13] Permission denied: '/home/<USER>/Downloads/mock_file.txt'
2025-05-26 05:50:24,972 - layer.capture - WARNING - [true_file_interceptor.py:402] - File quarantined: capture\temp_storage\incoming\20250526_055024_856087_mock_file.txt - Reason: Failed to restore
2025-05-26 05:50:24,973 - layer.capture - INFO - [true_file_interceptor.py:422] - Quarantine notification: {'type': 'quarantine', 'original_filename': 'mock_file.txt', 'file_hash': '65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699', 'reason': 'Failed to restore', 'quarantine_path': 'capture\\temp_storage\\quarantine\\20250526_055024_856087_mock_file.txt', 'timestamp': '2025-05-26T05:50:24.973035'}
2025-05-26 05:51:25,211 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
2025-05-26 05:52:26,405 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
2025-05-26 05:53:27,395 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
2025-05-26 05:54:29,262 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
2025-05-26 05:55:30,282 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
