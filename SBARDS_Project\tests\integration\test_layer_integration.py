#!/usr/bin/env python3
"""
Integration Tests for Layer Communication - SBARDS Project

This module tests the integration between different layers of the SBARDS system
to ensure seamless communication and data flow.

Test Categories:
- Core to Static Analysis integration
- Static Analysis to API integration
- Configuration propagation
- Data flow between layers
- Error handling across layers
"""

import os
import sys
import unittest
import tempfile
import json
import time
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from core.config import ConfigManager
    from core.utils import FileUtils
    from core.logger import setup_logging, get_global_logger
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False

try:
    from static_analysis.python.yara_scanner import YaraScanner
    STATIC_ANALYSIS_AVAILABLE = True
except ImportError:
    STATIC_ANALYSIS_AVAILABLE = False

try:
    from api.main import app
    import fastapi
    API_AVAILABLE = True
except ImportError:
    API_AVAILABLE = False

class TestCoreStaticAnalysisIntegration(unittest.TestCase):
    """Test integration between Core and Static Analysis layers."""
    
    def setUp(self):
        """Set up integration test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "integration_config.json"
        
        # Create integration test configuration
        self.test_config = {
            "core": {
                "project_name": "SBARDS_Integration_Test",
                "debug_mode": True
            },
            "static_analysis": {
                "yara_rules_directory": str(Path(self.temp_dir) / "yara_rules"),
                "parallel_processing": True,
                "max_threads": 2,
                "enable_caching": True,
                "timeout_seconds": 10
            },
            "scanner": {
                "enabled": True,
                "use_cpp_components": False,  # Use Python for testing
                "performance_mode": "normal"
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(self.test_config, f)
        
        # Create test YARA rules directory
        self.yara_rules_dir = Path(self.temp_dir) / "yara_rules"
        self.yara_rules_dir.mkdir(exist_ok=True)
        
        # Create a simple test rule
        test_rule = '''
rule TestRule {
    meta:
        description = "Test rule for integration testing"
        author = "SBARDS Test"
        category = "test"
        severity = "low"
    strings:
        $test_string = "SBARDS_TEST_PATTERN"
    condition:
        $test_string
}
'''
        with open(self.yara_rules_dir / "test_rule.yar", 'w') as f:
            f.write(test_rule)
        
        # Create test files
        self.test_file_clean = Path(self.temp_dir) / "clean_file.txt"
        self.test_file_suspicious = Path(self.temp_dir) / "suspicious_file.txt"
        
        with open(self.test_file_clean, 'w') as f:
            f.write("This is a clean test file with normal content.")
        
        with open(self.test_file_suspicious, 'w') as f:
            f.write("This file contains SBARDS_TEST_PATTERN for testing.")
    
    def tearDown(self):
        """Clean up integration test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_to_static_analysis_flow(self):
        """Test configuration flow to static analysis."""
        # Load configuration
        config_manager = ConfigManager(str(self.config_file))
        self.assertTrue(config_manager.load_config())
        
        # Test configuration access for static analysis
        static_config = config_manager.get_section("static_analysis")
        self.assertIsNotNone(static_config)
        self.assertEqual(static_config["max_threads"], 2)
        self.assertTrue(static_config["enable_caching"])
    
    @unittest.skipIf(not STATIC_ANALYSIS_AVAILABLE, "Static analysis not available")
    def test_yara_scanner_integration(self):
        """Test YARA scanner integration with core configuration."""
        # Load configuration
        config_manager = ConfigManager(str(self.config_file))
        config_manager.load_config()
        
        # Get static analysis configuration
        static_config = config_manager.get_section("static_analysis")
        
        # Initialize YARA scanner with configuration
        scanner = YaraScanner(static_config)
        
        # Test scanning clean file
        result_clean = scanner.scan_file(str(self.test_file_clean))
        self.assertIsNotNone(result_clean)
        self.assertEqual(result_clean.file_path, str(self.test_file_clean))
        self.assertGreaterEqual(len(result_clean.matches), 0)
        
        # Test scanning suspicious file
        result_suspicious = scanner.scan_file(str(self.test_file_suspicious))
        self.assertIsNotNone(result_suspicious)
        self.assertEqual(result_suspicious.file_path, str(self.test_file_suspicious))
        
        # Should detect the test pattern
        if result_suspicious.matches:
            self.assertGreater(len(result_suspicious.matches), 0)
            # Check if our test rule was triggered
            rule_names = [match.rule_name for match in result_suspicious.matches]
            self.assertIn("TestRule", rule_names)
    
    def test_logging_integration(self):
        """Test logging integration across layers."""
        # Setup logging
        log_file = Path(self.temp_dir) / "integration.log"
        logger_manager = setup_logging(
            log_file=str(log_file),
            log_level="INFO"
        )
        
        # Test logging from different layers
        core_logger = get_global_logger().get_logger("core")
        static_logger = get_global_logger().get_layer_logger("static_analysis")
        
        core_logger.info("Core layer integration test message")
        static_logger.info("Static analysis layer integration test message")
        
        # Verify messages are logged
        self.assertTrue(log_file.exists())
        with open(log_file, 'r') as f:
            log_content = f.read()
            self.assertIn("Core layer integration test message", log_content)
            self.assertIn("Static analysis layer integration test message", log_content)
    
    def test_error_propagation(self):
        """Test error handling and propagation between layers."""
        # Test with invalid configuration
        invalid_config = {
            "static_analysis": {
                "yara_rules_directory": "/non/existent/path",
                "max_threads": -1,  # Invalid value
                "timeout_seconds": "invalid"  # Invalid type
            }
        }
        
        invalid_config_file = Path(self.temp_dir) / "invalid_config.json"
        with open(invalid_config_file, 'w') as f:
            json.dump(invalid_config, f)
        
        config_manager = ConfigManager(str(invalid_config_file))
        self.assertTrue(config_manager.load_config())  # Should load but with issues
        
        # Test that static analysis handles invalid configuration gracefully
        if STATIC_ANALYSIS_AVAILABLE:
            static_config = config_manager.get_section("static_analysis")
            try:
                scanner = YaraScanner(static_config)
                # Should handle gracefully or raise appropriate exception
            except Exception as e:
                # Exception is acceptable for invalid configuration
                self.assertIsInstance(e, (ValueError, FileNotFoundError, TypeError))

class TestAPIIntegration(unittest.TestCase):
    """Test API integration with other layers."""
    
    def setUp(self):
        """Set up API integration test environment."""
        if not API_AVAILABLE:
            self.skipTest("API components not available")
        
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up API integration test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_api_app_creation(self):
        """Test API application creation and basic structure."""
        # Test that FastAPI app is created
        self.assertIsNotNone(app)
        self.assertIsInstance(app, fastapi.FastAPI)
        
        # Test that app has expected attributes
        self.assertTrue(hasattr(app, 'routes'))
        self.assertTrue(hasattr(app, 'middleware'))
    
    def test_api_routes_registration(self):
        """Test that API routes are properly registered."""
        # Get all routes
        routes = [route.path for route in app.routes]
        
        # Test that essential routes exist
        expected_routes = [
            "/",
            "/health",
            "/api/system/status"
        ]
        
        for expected_route in expected_routes:
            # Check if route exists (exact match or pattern match)
            route_exists = any(
                expected_route == route or 
                expected_route in route or
                route.startswith(expected_route.split('{')[0])  # Handle parameterized routes
                for route in routes
            )
            self.assertTrue(route_exists, f"Route {expected_route} not found in {routes}")

class TestDataFlowIntegration(unittest.TestCase):
    """Test data flow between different components."""
    
    def setUp(self):
        """Set up data flow test environment."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test data
        self.test_data = {
            "file_path": str(Path(self.temp_dir) / "test_file.txt"),
            "file_content": "Test content for data flow testing",
            "expected_hash": None
        }
        
        # Create test file
        with open(self.test_data["file_path"], 'w') as f:
            f.write(self.test_data["file_content"])
        
        # Calculate expected hash
        if CORE_AVAILABLE:
            self.test_data["expected_hash"] = FileUtils.get_file_hash(
                self.test_data["file_path"], "sha256"
            )
    
    def tearDown(self):
        """Clean up data flow test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @unittest.skipIf(not CORE_AVAILABLE, "Core components not available")
    def test_file_processing_pipeline(self):
        """Test complete file processing pipeline."""
        # Step 1: File hash calculation (Core layer)
        file_hash = FileUtils.get_file_hash(self.test_data["file_path"], "sha256")
        self.assertIsNotNone(file_hash)
        self.assertEqual(file_hash, self.test_data["expected_hash"])
        
        # Step 2: File size calculation (Core layer)
        file_size = FileUtils.get_file_size(self.test_data["file_path"])
        self.assertGreater(file_size, 0)
        self.assertEqual(file_size, len(self.test_data["file_content"]))
        
        # Step 3: File existence check (Core layer)
        file_exists = FileUtils.file_exists(self.test_data["file_path"])
        self.assertTrue(file_exists)
    
    def test_configuration_data_flow(self):
        """Test configuration data flow through the system."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        # Create configuration
        config_data = {
            "test_section": {
                "test_value": "integration_test",
                "test_number": 42,
                "test_boolean": True
            }
        }
        
        config_file = Path(self.temp_dir) / "flow_config.json"
        with open(config_file, 'w') as f:
            json.dump(config_data, f)
        
        # Test configuration loading and access
        config_manager = ConfigManager(str(config_file))
        self.assertTrue(config_manager.load_config())
        
        # Test data retrieval
        self.assertEqual(config_manager.get("test_section.test_value"), "integration_test")
        self.assertEqual(config_manager.get("test_section.test_number"), 42)
        self.assertTrue(config_manager.get("test_section.test_boolean"))

class TestPerformanceIntegration(unittest.TestCase):
    """Test performance across integrated components."""
    
    def setUp(self):
        """Set up performance integration test environment."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test files of different sizes
        self.test_files = {}
        
        # Small file (1KB)
        small_file = Path(self.temp_dir) / "small.txt"
        with open(small_file, 'w') as f:
            f.write("test content\n" * 100)
        self.test_files["small"] = str(small_file)
        
        # Medium file (10KB)
        medium_file = Path(self.temp_dir) / "medium.txt"
        with open(medium_file, 'w') as f:
            f.write("test content for medium file\n" * 400)
        self.test_files["medium"] = str(medium_file)
    
    def tearDown(self):
        """Clean up performance integration test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @unittest.skipIf(not CORE_AVAILABLE, "Core components not available")
    def test_integrated_file_processing_performance(self):
        """Test performance of integrated file processing."""
        import time
        
        for file_type, file_path in self.test_files.items():
            start_time = time.time()
            
            # Simulate integrated processing
            # Step 1: Hash calculation
            file_hash = FileUtils.get_file_hash(file_path, "sha256")
            
            # Step 2: File size
            file_size = FileUtils.get_file_size(file_path)
            
            # Step 3: File existence
            file_exists = FileUtils.file_exists(file_path)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Verify results
            self.assertIsNotNone(file_hash)
            self.assertGreater(file_size, 0)
            self.assertTrue(file_exists)
            
            # Performance expectations
            if file_type == "small":
                self.assertLess(duration, 0.1)  # Less than 100ms
            elif file_type == "medium":
                self.assertLess(duration, 0.2)  # Less than 200ms
            
            print(f"Integrated processing ({file_type}): {duration:.3f}s")

def run_integration_tests():
    """Run all integration tests."""
    test_classes = [
        TestCoreStaticAnalysisIntegration,
        TestAPIIntegration,
        TestDataFlowIntegration,
        TestPerformanceIntegration
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    print("🔗 Running Layer Integration Tests")
    print("=" * 50)
    
    success = run_integration_tests()
    
    if success:
        print("\n✅ All integration tests passed!")
        print("🚀 Layer integration is working correctly")
    else:
        print("\n❌ Some integration tests failed!")
        print("🔧 Please check the test output for details")
    
    exit(0 if success else 1)
