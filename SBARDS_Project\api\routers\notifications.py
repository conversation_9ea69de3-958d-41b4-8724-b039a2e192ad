"""
SBARDS Notifications API Router
Advanced notification system endpoints
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

# Import services
try:
    from api.services.notification_service import notification_service, NotificationLevel, NotificationChannel, Notification
    from api.services.cache_manager import cache_manager
    NOTIFICATIONS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Notification service not available: {e}")
    NOTIFICATIONS_AVAILABLE = False

# Setup router
router = APIRouter()
logger = logging.getLogger("notifications_api")

# Pydantic models
class NotificationRequest(BaseModel):
    title: str
    message: str
    level: str = "info"
    source: str = "api"
    data: Optional[Dict[str, Any]] = None
    channels: Optional[List[str]] = None

class NotificationRuleRequest(BaseModel):
    rule_id: str
    name: str
    condition: str
    level: str
    channels: List[str]
    enabled: bool = True
    cooldown_minutes: int = 5

class EmailConfigRequest(BaseModel):
    smtp_server: str
    smtp_port: int
    username: str
    password: str

class SMSConfigRequest(BaseModel):
    api_key: str
    api_url: str

# Dependency to check notifications availability
def check_notifications_available():
    if not NOTIFICATIONS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Notification service not available")
    return True

@router.post("/send")
async def send_notification(
    notification_request: NotificationRequest,
    _: bool = Depends(check_notifications_available)
):
    """Send a notification"""
    try:
        # Validate level
        try:
            level = NotificationLevel(notification_request.level)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid notification level: {notification_request.level}")
        
        # Validate and convert channels
        channels = []
        if notification_request.channels:
            for channel_str in notification_request.channels:
                try:
                    channel = NotificationChannel(channel_str)
                    channels.append(channel)
                except ValueError:
                    raise HTTPException(status_code=400, detail=f"Invalid notification channel: {channel_str}")
        else:
            channels = [NotificationChannel.WEBSOCKET]  # Default to WebSocket
        
        # Create notification
        notification = Notification(
            title=notification_request.title,
            message=notification_request.message,
            level=level,
            source=notification_request.source,
            data=notification_request.data
        )
        
        # Send notification
        await notification_service.send_notification(notification, channels)
        
        return {
            "success": True,
            "notification_id": notification.id,
            "title": notification.title,
            "level": notification.level.value,
            "channels": [c.value for c in channels],
            "delivered_channels": [c.value for c in notification.delivered_channels],
            "failed_channels": [c.value for c in notification.failed_channels],
            "timestamp": notification.timestamp.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending notification: {e}")
        raise HTTPException(status_code=500, detail=f"Error sending notification: {str(e)}")

@router.get("/history")
async def get_notification_history(
    limit: int = Query(50, ge=1, le=200, description="Number of notifications to return"),
    level: Optional[str] = Query(None, description="Filter by notification level"),
    source: Optional[str] = Query(None, description="Filter by notification source"),
    _: bool = Depends(check_notifications_available)
):
    """Get notification history"""
    try:
        # Check cache first
        cache_key = f"notification_history_{limit}_{level or 'all'}_{source or 'all'}"
        cached_history = cache_manager.get(cache_key, namespace="notifications")
        
        if cached_history:
            return {
                "notifications": cached_history,
                "total": len(cached_history),
                "limit": limit,
                "filters": {"level": level, "source": source},
                "cached": True,
                "timestamp": datetime.now().isoformat()
            }
        
        # Get history from service
        history = notification_service.get_notification_history(limit)
        
        # Apply filters
        if level:
            history = [n for n in history if n["level"] == level]
        
        if source:
            history = [n for n in history if n["source"] == source]
        
        # Cache for 2 minutes
        cache_manager.set(cache_key, history, ttl=120, namespace="notifications")
        
        return {
            "notifications": history,
            "total": len(history),
            "limit": limit,
            "filters": {"level": level, "source": source},
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting notification history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting notification history: {str(e)}")

@router.get("/rules")
async def get_notification_rules(
    _: bool = Depends(check_notifications_available)
):
    """Get all notification rules"""
    try:
        # Check cache first
        cache_key = "notification_rules_status"
        cached_rules = cache_manager.get(cache_key, namespace="notifications")
        
        if cached_rules:
            return {
                "rules": cached_rules,
                "total": len(cached_rules),
                "cached": True,
                "timestamp": datetime.now().isoformat()
            }
        
        rules = notification_service.get_rules_status()
        
        # Cache for 5 minutes
        cache_manager.set(cache_key, rules, ttl=300, namespace="notifications")
        
        return {
            "rules": rules,
            "total": len(rules),
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting notification rules: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting notification rules: {str(e)}")

@router.post("/rules")
async def add_notification_rule(
    rule_request: NotificationRuleRequest,
    _: bool = Depends(check_notifications_available)
):
    """Add a new notification rule"""
    try:
        # Validate level
        try:
            level = NotificationLevel(rule_request.level)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid notification level: {rule_request.level}")
        
        # Validate channels
        channels = []
        for channel_str in rule_request.channels:
            try:
                channel = NotificationChannel(channel_str)
                channels.append(channel)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid notification channel: {channel_str}")
        
        # Create rule
        from api.services.notification_service import NotificationRule
        rule = NotificationRule(
            rule_id=rule_request.rule_id,
            name=rule_request.name,
            condition=rule_request.condition,
            level=level,
            channels=channels,
            enabled=rule_request.enabled,
            cooldown_minutes=rule_request.cooldown_minutes
        )
        
        # Add rule to service
        notification_service.add_rule(rule)
        
        # Invalidate cache
        cache_manager.delete("notification_rules_status", namespace="notifications")
        
        return {
            "success": True,
            "rule_id": rule.rule_id,
            "name": rule.name,
            "enabled": rule.enabled,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding notification rule: {e}")
        raise HTTPException(status_code=500, detail=f"Error adding notification rule: {str(e)}")

@router.put("/rules/{rule_id}/enable")
async def enable_notification_rule(
    rule_id: str,
    _: bool = Depends(check_notifications_available)
):
    """Enable a notification rule"""
    try:
        notification_service.enable_rule(rule_id)
        
        # Invalidate cache
        cache_manager.delete("notification_rules_status", namespace="notifications")
        
        return {
            "success": True,
            "rule_id": rule_id,
            "action": "enabled",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error enabling notification rule: {e}")
        raise HTTPException(status_code=500, detail=f"Error enabling notification rule: {str(e)}")

@router.put("/rules/{rule_id}/disable")
async def disable_notification_rule(
    rule_id: str,
    _: bool = Depends(check_notifications_available)
):
    """Disable a notification rule"""
    try:
        notification_service.disable_rule(rule_id)
        
        # Invalidate cache
        cache_manager.delete("notification_rules_status", namespace="notifications")
        
        return {
            "success": True,
            "rule_id": rule_id,
            "action": "disabled",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error disabling notification rule: {e}")
        raise HTTPException(status_code=500, detail=f"Error disabling notification rule: {str(e)}")

@router.delete("/rules/{rule_id}")
async def remove_notification_rule(
    rule_id: str,
    _: bool = Depends(check_notifications_available)
):
    """Remove a notification rule"""
    try:
        notification_service.remove_rule(rule_id)
        
        # Invalidate cache
        cache_manager.delete("notification_rules_status", namespace="notifications")
        
        return {
            "success": True,
            "rule_id": rule_id,
            "action": "removed",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error removing notification rule: {e}")
        raise HTTPException(status_code=500, detail=f"Error removing notification rule: {str(e)}")

@router.post("/config/email")
async def configure_email_notifications(
    email_config: EmailConfigRequest,
    _: bool = Depends(check_notifications_available)
):
    """Configure email notification settings"""
    try:
        notification_service.configure_email(
            smtp_server=email_config.smtp_server,
            smtp_port=email_config.smtp_port,
            username=email_config.username,
            password=email_config.password
        )
        
        return {
            "success": True,
            "message": "Email notifications configured",
            "smtp_server": email_config.smtp_server,
            "smtp_port": email_config.smtp_port,
            "username": email_config.username,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error configuring email notifications: {e}")
        raise HTTPException(status_code=500, detail=f"Error configuring email notifications: {str(e)}")

@router.post("/config/sms")
async def configure_sms_notifications(
    sms_config: SMSConfigRequest,
    _: bool = Depends(check_notifications_available)
):
    """Configure SMS notification settings"""
    try:
        notification_service.configure_sms(
            api_key=sms_config.api_key,
            api_url=sms_config.api_url
        )
        
        return {
            "success": True,
            "message": "SMS notifications configured",
            "api_url": sms_config.api_url,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error configuring SMS notifications: {e}")
        raise HTTPException(status_code=500, detail=f"Error configuring SMS notifications: {str(e)}")

@router.post("/test")
async def test_notification_channels(
    channels: List[str] = Query(..., description="Channels to test"),
    _: bool = Depends(check_notifications_available)
):
    """Test notification channels"""
    try:
        # Validate channels
        test_channels = []
        for channel_str in channels:
            try:
                channel = NotificationChannel(channel_str)
                test_channels.append(channel)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid notification channel: {channel_str}")
        
        # Create test notification
        test_notification = Notification(
            title="Test Notification",
            message="This is a test notification from SBARDS",
            level=NotificationLevel.INFO,
            source="test",
            data={"test": True}
        )
        
        # Send test notification
        await notification_service.send_notification(test_notification, test_channels)
        
        return {
            "success": True,
            "message": "Test notification sent",
            "channels_tested": [c.value for c in test_channels],
            "delivered_channels": [c.value for c in test_notification.delivered_channels],
            "failed_channels": [c.value for c in test_notification.failed_channels],
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing notification channels: {e}")
        raise HTTPException(status_code=500, detail=f"Error testing notification channels: {str(e)}")

@router.get("/stats")
async def get_notification_stats(
    _: bool = Depends(check_notifications_available)
):
    """Get notification statistics"""
    try:
        # Get recent history for stats
        history = notification_service.get_notification_history(100)
        
        # Calculate stats
        total_notifications = len(history)
        level_counts = {}
        channel_counts = {}
        source_counts = {}
        
        for notification in history:
            # Count by level
            level = notification["level"]
            level_counts[level] = level_counts.get(level, 0) + 1
            
            # Count by channels
            for channel in notification["delivered_channels"]:
                channel_counts[channel] = channel_counts.get(channel, 0) + 1
            
            # Count by source
            source = notification["source"]
            source_counts[source] = source_counts.get(source, 0) + 1
        
        # Get rules stats
        rules = notification_service.get_rules_status()
        enabled_rules = len([r for r in rules if r["enabled"]])
        disabled_rules = len([r for r in rules if not r["enabled"]])
        
        stats = {
            "total_notifications": total_notifications,
            "level_distribution": level_counts,
            "channel_distribution": channel_counts,
            "source_distribution": source_counts,
            "rules": {
                "total": len(rules),
                "enabled": enabled_rules,
                "disabled": disabled_rules
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting notification stats: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting notification stats: {str(e)}")

# Health check endpoint
@router.get("/health")
async def notifications_health():
    """Notification service health check"""
    try:
        if not NOTIFICATIONS_AVAILABLE:
            return {
                "status": "unavailable",
                "message": "Notification service not available",
                "timestamp": datetime.now().isoformat()
            }
        
        # Test basic functionality
        rules = notification_service.get_rules_status()
        
        return {
            "status": "healthy",
            "message": "Notification service operational",
            "rules_loaded": len(rules),
            "websocket_manager_available": notification_service.websocket_manager is not None,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Notification health check failed: {e}")
        return {
            "status": "unhealthy",
            "message": f"Notification service error: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }
