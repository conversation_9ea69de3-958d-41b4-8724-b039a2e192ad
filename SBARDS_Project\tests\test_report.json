{"summary": {"total_files": 37, "existing_files": 37, "completion_rate": 100.0, "total_size": 462954, "timestamp": "2025-05-25T04:44:53.529955"}, "detailed_reports": {"structure": {"core": {"exists": true, "file_count": 5, "files": ["core\\config.py", "core\\constants.py", "core\\logger.py", "core\\test_core.py", "core\\utils.py"]}, "capture": {"exists": true, "file_count": 7, "files": ["capture\\build_capture.py", "capture\\test_capture_layer.py", "capture\\cpp\\CMakeLists.txt", "capture\\cpp\\file_monitor.cpp", "capture\\cpp\\permission_manager.cpp"]}, "static_analysis": {"exists": true, "file_count": 18, "files": ["static_analysis\\build_static_analysis.py", "static_analysis\\test_static_analysis.py", "static_analysis\\cpp\\CMakeLists.txt", "static_analysis\\cpp\\entropy_checker.cpp", "static_analysis\\cpp\\hash_generator.cpp"]}, "api": {"exists": true, "file_count": 28, "files": ["api\\main.py", "api\\db\\base.py", "api\\db\\models.py", "api\\db\\session.py", "api\\db\\__init__.py"]}, "ui": {"exists": true, "file_count": 3, "files": ["ui\\cli\\cli_app.py", "ui\\dashboard\\dashboard.py", "ui\\dashboard\\templates\\dashboard.html"]}, "tests": {"exists": true, "file_count": 15, "files": ["tests\\README.md", "tests\\test_static_analysis_integration.py", "tests\\integration\\test_capture_integration.py", "tests\\integration\\test_layer_integration.py", "tests\\performance\\test_performance_benchmarks.py"]}, "docs": {"exists": true, "file_count": 6, "files": ["docs\\Function_Preservation_Plan.md", "docs\\Phase1_Migration_Report.md", "docs\\Phase2_Capture_Layer_Report.md", "docs\\Phase3_Static_Analysis_Report.md", "docs\\Phase3_Summary.md"]}, "config": {"exists": true, "file_count": 1, "files": ["config\\config.json"]}, "data": {"exists": true, "file_count": 4, "files": ["data\\sbards.db", "data\\virus_hashes\\local_db\\database.py", "data\\whitelists\\safe_files.json", "data\\whitelists\\trusted_sources.json"]}, "logs": {"exists": true, "file_count": 30, "files": ["logs\\layer_api.log", "logs\\layer_api_db.log", "logs\\layer_api_middleware_cors.log", "logs\\layer_api_middleware_logging.log", "logs\\layer_api_middleware_rate_limit.log"]}}, "core": {"core/config.py": {"exists": true, "size": 17793}, "core/logger.py": {"exists": true, "size": 18176}, "core/constants.py": {"exists": true, "size": 9226}, "core/utils.py": {"exists": true, "size": 27439}, "core/test_core.py": {"exists": true, "size": 4500}, "imports": {"success": true, "error": null}}, "capture": {"capture/cpp/file_monitor.cpp": {"exists": true, "size": 21045}, "capture/cpp/permission_manager.cpp": {"exists": true, "size": 19311}, "capture/cpp/CMakeLists.txt": {"exists": true, "size": 6432}, "capture/python/file_interceptor.py": {"exists": true, "size": 27751}, "capture/python/redis_queue.py": {"exists": true, "size": 25459}, "capture/test_capture_layer.py": {"exists": true, "size": 15659}, "capture/build_capture.py": {"exists": true, "size": 11547}, "imports": {"success": true, "error": null}}, "static_analysis": {"static_analysis/cpp/signature_checker.cpp": {"exists": true, "size": 24530}, "static_analysis/cpp/permission_analyzer.cpp": {"exists": true, "size": 22497}, "static_analysis/cpp/entropy_checker.cpp": {"exists": true, "size": 16873}, "static_analysis/cpp/hash_generator.cpp": {"exists": true, "size": 16899}, "static_analysis/cpp/CMakeLists.txt": {"exists": true, "size": 13035}, "static_analysis/python/yara_scanner.py": {"exists": true, "size": 31480}, "static_analysis/python/virus_total.py": {"exists": true, "size": 18588}, "static_analysis/python/report_generator.py": {"exists": true, "size": 25688}, "static_analysis/yara_rules/index.yar": {"exists": true, "size": 9678}, "static_analysis/test_static_analysis.py": {"exists": true, "size": 21238}, "static_analysis/build_static_analysis.py": {"exists": true, "size": 22240}, "imports": {"success": true, "error": null}}, "dependencies": {"required": {"fastapi": {"available": true, "error": null}, "uvicorn": {"available": true, "error": null}, "pydantic": {"available": true, "error": null}, "sqlalchemy": {"available": true, "error": null}, "redis": {"available": false, "error": "No module named 'redis'"}, "requests": {"available": true, "error": null}, "cryptography": {"available": true, "error": null}, "pathlib": {"available": true, "error": null}}, "optional": {"yara": {"available": true, "error": null}, "pymongo": {"available": false, "error": "No module named 'pymongo'"}, "docker": {"available": false, "error": "No module named 'docker'"}}}, "config": {"config.json": {"exists": true, "size": 4380}, "requirements.txt": {"exists": true, "size": 4684}, "README.md": {"exists": true, "size": 9245}, "run.py": {"exists": true, "size": 17561}}}, "metadata": {"generated_at": "2025-05-25T04:44:53.529977", "python_version": "3.13", "platform": "nt", "working_directory": "G:\\SBARDS\\SBARDS_Project"}}