#!/usr/bin/env python3
"""
Integrated Capture Layer - SBARDS
طبقة الالتقاط المتكاملة - نظام SBARDS

هذا الملف يجمع جميع مكونات طبقة الالتقاط:
1. TrueFileInterceptor (Python) - إدارة المنطقة الآمنة والتنسيق
2. CppInterceptorBridge - التكامل مع مكون C++ للاعتراض الحقيقي
3. التكامل مع طبقة التحليل الثابت
4. إدارة دورة الحياة الكاملة

السيناريو الكامل:
1. C++ يعترض الملف قبل الحفظ
2. Python يستقبل البيانات ويخزنها آمن
3. حساب هاش SHA-256 فوري
4. إرسال للتحليل الثابت
5. قرار بناءً على النتيجة (إرجاع أو حجر صحي)
"""

import os
import sys
import time
import asyncio
from typing import Dict, Any, Optional, Callable
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.logger import get_global_logger
from capture.python.true_file_interceptor import TrueFileInterceptor, FileInterceptionEvent
from capture.python.cpp_integration import create_cpp_bridge

class IntegratedCaptureLayer:
    """
    طبقة الالتقاط المتكاملة
    تجمع جميع مكونات الاعتراض والمعالجة
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_global_logger().get_layer_logger("capture")
        
        # المكونات الرئيسية
        self.true_interceptor = TrueFileInterceptor(config)
        self.cpp_bridge = create_cpp_bridge(
            use_mock=config.get("use_mock_cpp", True)  # استخدام mock افتراضياً للاختبار
        )
        
        # حالة النظام
        self.running = False
        self.static_analysis_callback = None
        
        # إحصائيات متكاملة
        self.integrated_stats = {
            "cpp_intercepts": 0,
            "python_processes": 0,
            "static_analysis_requests": 0,
            "files_restored": 0,
            "files_quarantined": 0,
            "start_time": None
        }
        
        self.logger.info("IntegratedCaptureLayer initialized")
    
    def set_static_analysis_callback(self, callback: Callable[[Dict[str, Any]], Dict[str, Any]]):
        """تعيين callback للتكامل مع طبقة التحليل الثابت"""
        self.static_analysis_callback = callback
        self.true_interceptor.set_static_analysis_callback(self._static_analysis_wrapper)
        self.logger.info("Static analysis callback registered")
    
    def _static_analysis_wrapper(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """wrapper للتكامل مع طبقة التحليل الثابت"""
        try:
            self.integrated_stats["static_analysis_requests"] += 1
            
            if self.static_analysis_callback:
                result = self.static_analysis_callback(file_info)
                self.logger.debug(f"Static analysis result: {result.get('threat_level', 'unknown')}")
                return result
            else:
                # إذا لم يكن هناك تحليل ثابت، اعتبر الملف آمن
                self.logger.warning("No static analysis callback - treating as safe")
                return {"threat_level": "safe", "details": "No static analysis available"}
                
        except Exception as e:
            self.logger.error(f"Error in static analysis wrapper: {e}")
            return {"threat_level": "unknown", "error": str(e)}
    
    def _cpp_callback(self, event_info: Dict[str, Any]) -> bool:
        """callback يتم استدعاؤه من C++ عند اعتراض ملف"""
        try:
            self.integrated_stats["cpp_intercepts"] += 1
            
            self.logger.info(f"File intercepted by C++: {event_info['file_path']}")
            
            # تمرير للمُعترض Python للمعالجة
            event = self.true_interceptor.intercept_file_data(
                file_data=event_info['file_data'],
                original_filename=os.path.basename(event_info['intended_path']),
                source_info={
                    'source': 'cpp_interceptor',
                    'intended_path': event_info['intended_path'],
                    'cpp_file_path': event_info['file_path']
                }
            )
            
            if event:
                self.integrated_stats["python_processes"] += 1
                self.logger.info(f"File passed to Python processing: {event.file_hash}")
                return True
            else:
                self.logger.error("Failed to process file in Python layer")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in C++ callback: {e}")
            return False
    
    def start(self) -> bool:
        """تشغيل طبقة الالتقاط المتكاملة"""
        if self.running:
            return True
        
        self.logger.info("Starting Integrated Capture Layer...")
        self.integrated_stats["start_time"] = time.time()
        
        try:
            # 1. تهيئة جسر C++
            if not self.cpp_bridge.initialize():
                self.logger.error("Failed to initialize C++ bridge")
                return False
            
            # 2. تعيين callback لـ C++
            self.cpp_bridge.set_python_callback(self._cpp_callback)
            
            # 3. تشغيل المُعترض Python
            if not self.true_interceptor.start():
                self.logger.error("Failed to start Python interceptor")
                return False
            
            # 4. تشغيل جسر C++
            if not self.cpp_bridge.start():
                self.logger.error("Failed to start C++ bridge")
                self.true_interceptor.stop()
                return False
            
            self.running = True
            self.logger.info("Integrated Capture Layer started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start Integrated Capture Layer: {e}")
            self.stop()
            return False
    
    def stop(self):
        """إيقاف طبقة الالتقاط المتكاملة"""
        if not self.running:
            return
        
        self.logger.info("Stopping Integrated Capture Layer...")
        
        try:
            # إيقاف المكونات بالترتيب العكسي
            self.cpp_bridge.stop()
            self.true_interceptor.stop()
            
            self.running = False
            self.logger.info("Integrated Capture Layer stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping Integrated Capture Layer: {e}")
    
    def cleanup(self):
        """تنظيف جميع الموارد"""
        try:
            self.stop()
            self.cpp_bridge.cleanup()
            self.true_interceptor.cleanup_old_files()
            self.logger.info("Integrated Capture Layer cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات شاملة"""
        stats = self.integrated_stats.copy()
        
        # إضافة إحصائيات المكونات الفرعية
        python_stats = self.true_interceptor.get_statistics()
        stats.update({
            "python_" + k: v for k, v in python_stats.items()
        })
        
        # حساب الوقت التشغيلي
        if stats["start_time"]:
            stats["runtime_seconds"] = time.time() - stats["start_time"]
        
        return stats
    
    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        return {
            "running": self.running,
            "components": {
                "cpp_bridge": self.cpp_bridge is not None,
                "python_interceptor": self.true_interceptor is not None,
                "static_analysis_callback": self.static_analysis_callback is not None
            },
            "statistics": self.get_comprehensive_statistics()
        }
    
    # دوال للاختبار والتطوير
    def simulate_file_intercept(self, filename: str, content: bytes, intended_path: str) -> bool:
        """محاكاة اعتراض ملف للاختبار"""
        try:
            event_info = {
                'file_path': f'/tmp/simulated_{filename}',
                'intended_path': intended_path,
                'file_data': content,
                'file_size': len(content),
                'source': 'simulation'
            }
            
            return self._cpp_callback(event_info)
            
        except Exception as e:
            self.logger.error(f"Error in simulation: {e}")
            return False

# دالة مساعدة لإنشاء طبقة الالتقاط
def create_integrated_capture_layer(config: Dict[str, Any]) -> IntegratedCaptureLayer:
    """إنشاء طبقة الالتقاط المتكاملة"""
    return IntegratedCaptureLayer(config)

# اختبار شامل
async def test_integrated_capture():
    """اختبار شامل لطبقة الالتقاط المتكاملة"""
    print("🛡️ SBARDS Integrated Capture Layer Test")
    print("=" * 50)
    
    # تكوين الاختبار
    config = {
        "temp_storage_path": "capture/temp_storage",
        "use_mock_cpp": True,  # استخدام mock للاختبار
        "max_file_size_mb": 100
    }
    
    # إنشاء طبقة الالتقاط
    capture_layer = create_integrated_capture_layer(config)
    
    # تعيين callback وهمي للتحليل الثابت
    def mock_static_analysis(file_info: Dict[str, Any]) -> Dict[str, Any]:
        print(f"📊 Mock Static Analysis: {file_info['file_path']}")
        
        # محاكاة تحليل بناءً على اسم الملف
        filename = os.path.basename(file_info.get('original_path', ''))
        
        if 'malware' in filename.lower() or 'virus' in filename.lower():
            return {"threat_level": "malicious", "details": "Simulated malware detection"}
        elif 'suspicious' in filename.lower():
            return {"threat_level": "suspicious", "details": "Simulated suspicious patterns"}
        else:
            return {"threat_level": "safe", "details": "Simulated clean file"}
    
    capture_layer.set_static_analysis_callback(mock_static_analysis)
    
    # تشغيل النظام
    if capture_layer.start():
        print("✅ Integrated Capture Layer started successfully")
        
        # اختبار محاكاة ملفات مختلفة
        test_files = [
            ("clean_document.txt", b"This is a clean document", "/home/<USER>/Downloads/clean_document.txt"),
            ("suspicious_file.exe", b"Suspicious executable content", "/home/<USER>/Downloads/suspicious_file.exe"),
            ("malware_sample.bin", b"Malicious binary content", "/home/<USER>/Downloads/malware_sample.bin")
        ]
        
        print("\n🧪 Testing file interceptions...")
        for filename, content, intended_path in test_files:
            print(f"\n📁 Simulating: {filename}")
            success = capture_layer.simulate_file_intercept(filename, content, intended_path)
            print(f"   Result: {'✅ Success' if success else '❌ Failed'}")
        
        # انتظار المعالجة
        print("\n⏳ Waiting for processing...")
        await asyncio.sleep(3)
        
        # عرض الإحصائيات
        stats = capture_layer.get_comprehensive_statistics()
        print(f"\n📊 Final Statistics:")
        print(f"   C++ Intercepts: {stats['cpp_intercepts']}")
        print(f"   Python Processes: {stats['python_processes']}")
        print(f"   Static Analysis Requests: {stats['static_analysis_requests']}")
        print(f"   Files Restored: {stats.get('python_files_restored', 0)}")
        print(f"   Files Quarantined: {stats.get('python_files_quarantined', 0)}")
        
        # عرض حالة النظام
        status = capture_layer.get_status()
        print(f"\n🔧 System Status:")
        print(f"   Running: {status['running']}")
        print(f"   Components: {status['components']}")
        
        # إيقاف النظام
        capture_layer.cleanup()
        print("\n✅ Test completed successfully")
        
    else:
        print("❌ Failed to start Integrated Capture Layer")

if __name__ == "__main__":
    asyncio.run(test_integrated_capture())
