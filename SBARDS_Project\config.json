{"core": {"project_name": "SBARDS", "version": "2.0.0", "debug_mode": false, "log_level": "info"}, "capture": {"enabled": true, "target_directories": ["samples", "Downloads"], "recursive": true, "max_depth": 5, "exclude_dirs": [".git", "__pycache__", "node_modules"], "exclude_extensions": [".tmp", ".log"], "max_file_size_mb": 100, "temp_storage_path": "capture/temp_storage", "cpp_monitor_enabled": true, "python_interceptor_enabled": true, "redis_queue_enabled": false}, "static_analysis": {"enabled": true, "cpp_analyzers": {"signature_checker": true, "permission_analyzer": true, "entropy_checker": true, "hash_generator": true}, "python_analyzers": {"yara_scanner": true, "virus_total": false, "report_generator": true}, "yara_rules": {"custom_enabled": true, "malware_enabled": true, "ransomware_enabled": true, "permissions_enabled": true, "rule_paths": ["static_analysis/yara_rules/custom/", "static_analysis/yara_rules/malware/", "static_analysis/yara_rules/ransomware/", "static_analysis/yara_rules/permissions/"]}, "hash_algorithms": ["sha256", "sha512", "md5", "ssdeep"], "entropy_threshold": 7.5, "parallel_processing": true, "max_threads": 4}, "dynamic_analysis": {"enabled": false, "sandbox_enabled": false, "honeypot_enabled": false, "ml_analysis_enabled": false, "timeout_seconds": 300}, "response": {"enabled": true, "quarantine_enabled": true, "alert_system_enabled": true, "auto_response": false, "quarantine_path": "quarantine/"}, "external_integration": {"enabled": false, "virus_total": {"enabled": false, "api_key": "", "rate_limit": 4, "timeout_seconds": 30}, "blockchain": {"enabled": false, "network": "hyperledger", "fabric_config": {"network_name": "sbards-network", "channel_name": "sbards-channel", "chaincode_name": "sbards-chaincode", "peer_endpoint": "localhost:7051", "orderer_endpoint": "localhost:7050"}}, "threat_intel": {"enabled": false, "sources": ["virustotal", "alienvault_otx", "misp"], "update_interval_hours": 24, "cache_enabled": true}, "mongodb": {"enabled": false, "host": "localhost", "port": 27017, "database": "sbards_security", "username": "", "password": "", "ssl_enabled": false}}, "memory_protection": {"enabled": false, "disk_encryption": false, "cold_boot_protection": false}, "monitoring": {"enabled": true, "system_monitor": true, "threat_tracker": true, "audit_logger": true, "event_correlator": true, "check_interval_seconds": 1.0, "alert_threshold": 0.7}, "api": {"host": "127.0.0.1", "port": 8000, "enable_cors": true, "allowed_origins": ["*"], "enable_docs": true, "enable_websockets": true, "max_upload_size_mb": 100}, "ui": {"dashboard_enabled": true, "cli_enabled": true, "gui_enabled": false}, "data": {"local_db_path": "data/virus_hashes/local_db/", "backup_enabled": true, "backup_path": "data/backups/", "whitelist_enabled": true}, "security": {"integrity_check": true, "threat_intel": true, "privacy_handler": true, "encryption_enabled": false}, "performance": {"threads": 4, "batch_size": 20, "timeout_seconds": 30, "adaptive_threading": true, "memory_limit_mb": 1024, "cpu_limit_percent": 80}}