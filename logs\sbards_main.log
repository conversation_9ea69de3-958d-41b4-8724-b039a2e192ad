2025-05-26 04:43:32,197 - layer.main - INFO - [run.py:379] - Running api layer only
2025-05-26 04:43:32,198 - layer.main - INFO - [run.py:77] - Initializing layers: ['api']
2025-05-26 04:43:32,198 - layer.main - INFO - [run.py:117] - Initializing api layer...
2025-05-26 04:43:34,985 - analytics_service - INFO - [analytics_service.py:108] - Analytics database initialized successfully
2025-05-26 04:43:35,033 - layer.api - INFO - [main.py:136] - Analytics router included successfully
2025-05-26 04:43:35,074 - notification_service - INFO - [notification_service.py:173] - Added notification rule: High CPU Usage
2025-05-26 04:43:35,075 - notification_service - INFO - [notification_service.py:173] - Added notification rule: High Memory Usage
2025-05-26 04:43:35,075 - notification_service - INFO - [notification_service.py:173] - Added notification rule: Threat Detected
2025-05-26 04:43:35,076 - notification_service - INFO - [notification_service.py:173] - Added notification rule: Capture Layer Down
2025-05-26 04:43:35,076 - notification_service - INFO - [notification_service.py:173] - Added notification rule: System Health Critical
2025-05-26 04:43:35,117 - layer.api - INFO - [main.py:146] - Notifications router included successfully
2025-05-26 04:43:35,126 - layer.main - INFO - [run.py:306] - Initializing API layer with FastAPI...
2025-05-26 04:43:35,126 - layer.main - INFO - [run.py:148] - Initializing True Capture Layer components...
2025-05-26 04:43:35,359 - layer.capture - INFO - [true_file_interceptor.py:240] - TrueFileInterceptor initialized
2025-05-26 04:43:35,360 - layer.capture - INFO - [integrated_capture_layer.py:64] - IntegratedCaptureLayer initialized
2025-05-26 04:43:35,361 - layer.capture - INFO - [true_file_interceptor.py:245] - Static analysis callback registered
2025-05-26 04:43:35,362 - layer.capture - INFO - [integrated_capture_layer.py:70] - Static analysis callback registered
2025-05-26 04:43:35,362 - layer.capture - INFO - [integrated_capture_layer.py:125] - Starting Integrated Capture Layer...
2025-05-26 04:43:35,363 - layer.capture - INFO - [cpp_integration.py:216] - Mock C++ interceptor initialized
2025-05-26 04:43:35,365 - layer.capture - INFO - [cpp_integration.py:221] - Mock Python callback registered
2025-05-26 04:43:35,366 - layer.capture - INFO - [true_file_interceptor.py:322] - Worker loop started
2025-05-26 04:43:35,366 - layer.capture - INFO - [true_file_interceptor.py:260] - TrueFileInterceptor started successfully
2025-05-26 04:43:35,367 - layer.capture - INFO - [cpp_integration.py:225] - Mock C++ interceptor started
2025-05-26 04:43:35,368 - layer.capture - INFO - [integrated_capture_layer.py:149] - Integrated Capture Layer started successfully
2025-05-26 04:43:35,368 - layer.main - INFO - [run.py:181] - True Capture Layer started successfully
2025-05-26 04:43:35,369 - layer.main - INFO - [run.py:231] - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 04:43:35,369 - layer.main - INFO - [run.py:332] - API layer initialized successfully
2025-05-26 04:43:35,370 - layer.main - INFO - [run.py:82] - Successfully initialized api layer
2025-05-26 04:43:35,370 - layer.main - INFO - [run.py:389] - api layer is running...
2025-05-26 04:43:35,371 - layer.main - INFO - [run.py:421] - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 04:43:35,371 - layer.main - INFO - [run.py:422] - ============================================================
2025-05-26 04:43:35,372 - layer.main - INFO - [run.py:423] - Available Endpoints:
2025-05-26 04:43:35,372 - layer.main - INFO - [run.py:427] -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:43:35,372 - layer.main - INFO - [run.py:427] -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 04:43:35,373 - layer.main - INFO - [run.py:427] -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 04:43:35,373 - layer.main - INFO - [run.py:427] -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 04:43:35,374 - layer.main - INFO - [run.py:427] -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 04:43:35,374 - layer.main - INFO - [run.py:427] -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 04:43:35,375 - layer.main - INFO - [run.py:429] - ============================================================
2025-05-26 04:43:35,375 - layer.main - INFO - [run.py:430] - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:43:35,375 - layer.main - INFO - [run.py:431] - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 04:43:35,435 - layer.api - INFO - [main.py:155] - Starting SBARDS API server...
2025-05-26 04:43:35,596 - layer.capture - INFO - [true_file_interceptor.py:240] - TrueFileInterceptor initialized
2025-05-26 04:43:35,597 - layer.capture - INFO - [integrated_capture_layer.py:64] - IntegratedCaptureLayer initialized
2025-05-26 04:43:35,598 - layer.capture - INFO - [true_file_interceptor.py:245] - Static analysis callback registered
2025-05-26 04:43:35,599 - layer.capture - INFO - [integrated_capture_layer.py:70] - Static analysis callback registered
2025-05-26 04:43:35,599 - layer.capture - INFO - [integrated_capture_layer.py:125] - Starting Integrated Capture Layer...
2025-05-26 04:43:35,600 - layer.capture - INFO - [cpp_integration.py:216] - Mock C++ interceptor initialized
2025-05-26 04:43:35,600 - layer.capture - INFO - [cpp_integration.py:221] - Mock Python callback registered
2025-05-26 04:43:35,601 - layer.capture - INFO - [true_file_interceptor.py:322] - Worker loop started
2025-05-26 04:43:35,601 - layer.capture - INFO - [true_file_interceptor.py:260] - TrueFileInterceptor started successfully
2025-05-26 04:43:35,602 - layer.capture - INFO - [cpp_integration.py:225] - Mock C++ interceptor started
2025-05-26 04:43:35,603 - layer.capture - INFO - [integrated_capture_layer.py:149] - Integrated Capture Layer started successfully
2025-05-26 04:43:35,603 - layer.api_capture - INFO - [capture.py:144] - True Capture Layer initialized successfully
2025-05-26 04:43:35,604 - layer.api_capture - WARNING - [capture.py:179] - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 04:43:35,605 - layer.api - INFO - [main.py:161] - Capture layer initialized successfully on startup
2025-05-26 04:43:35,605 - layer.api - INFO - [main.py:729] - Notification service initialized with WebSocket manager
2025-05-26 04:43:36,732 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
2025-05-26 04:43:37,368 - layer.capture - INFO - [integrated_capture_layer.py:95] - File intercepted by C++: /tmp/mock_intercepted_file.txt
2025-05-26 04:43:37,427 - layer.capture - INFO - [true_file_interceptor.py:125] - File stored securely: 20250526_044337_369110_mock_file.txt (hash: 65c92b167a9717fa...)
2025-05-26 04:43:37,428 - layer.capture - INFO - [true_file_interceptor.py:313] - File intercepted: mock_file.txt -> capture\temp_storage\incoming\20250526_044337_369110_mock_file.txt
2025-05-26 04:43:37,428 - layer.capture - INFO - [integrated_capture_layer.py:110] - File passed to Python processing: 65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699
2025-05-26 04:43:37,428 - layer.main - INFO - [run.py:172] - Static analysis request for: capture\temp_storage\incoming\20250526_044337_369110_mock_file.txt
2025-05-26 04:43:37,433 - layer.capture - ERROR - [true_file_interceptor.py:157] - Failed to restore file to /home/<USER>/Downloads/mock_file.txt: [Errno 13] Permission denied: '/home/<USER>/Downloads/mock_file.txt'
2025-05-26 04:43:37,434 - layer.capture - WARNING - [true_file_interceptor.py:402] - File quarantined: capture\temp_storage\incoming\20250526_044337_369110_mock_file.txt - Reason: Failed to restore
2025-05-26 04:43:37,435 - layer.capture - INFO - [true_file_interceptor.py:422] - Quarantine notification: {'type': 'quarantine', 'original_filename': 'mock_file.txt', 'file_hash': '65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699', 'reason': 'Failed to restore', 'quarantine_path': 'capture\\temp_storage\\quarantine\\20250526_044337_369110_mock_file.txt', 'timestamp': '2025-05-26T04:43:37.435267'}
2025-05-26 04:43:37,603 - layer.capture - INFO - [integrated_capture_layer.py:95] - File intercepted by C++: /tmp/mock_intercepted_file.txt
2025-05-26 04:43:37,666 - layer.capture - INFO - [true_file_interceptor.py:125] - File stored securely: 20250526_044337_604075_mock_file.txt (hash: 65c92b167a9717fa...)
2025-05-26 04:43:37,667 - layer.capture - INFO - [true_file_interceptor.py:313] - File intercepted: mock_file.txt -> capture\temp_storage\incoming\20250526_044337_604075_mock_file.txt
2025-05-26 04:43:37,668 - layer.api_capture - INFO - [capture.py:135] - Static analysis request for: capture\temp_storage\incoming\20250526_044337_604075_mock_file.txt
2025-05-26 04:43:37,669 - layer.capture - INFO - [integrated_capture_layer.py:110] - File passed to Python processing: 65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699
2025-05-26 04:43:37,673 - layer.capture - ERROR - [true_file_interceptor.py:157] - Failed to restore file to /home/<USER>/Downloads/mock_file.txt: [Errno 13] Permission denied: '/home/<USER>/Downloads/mock_file.txt'
2025-05-26 04:43:37,675 - layer.capture - WARNING - [true_file_interceptor.py:402] - File quarantined: capture\temp_storage\incoming\20250526_044337_604075_mock_file.txt - Reason: Failed to restore
2025-05-26 04:43:37,676 - layer.capture - INFO - [true_file_interceptor.py:422] - Quarantine notification: {'type': 'quarantine', 'original_filename': 'mock_file.txt', 'file_hash': '65c92b167a9717faf395d0c0b0d648cb2f4a7be618cc9e3add3b08df64b5e699', 'reason': 'Failed to restore', 'quarantine_path': 'capture\\temp_storage\\quarantine\\20250526_044337_604075_mock_file.txt', 'timestamp': '2025-05-26T04:43:37.676325'}
2025-05-26 04:44:37,767 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
2025-05-26 04:45:38,797 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
2025-05-26 04:46:40,855 - notification_service - INFO - [notification_service.py:297] - Notification sent: Threat Detected via ['websocket']
