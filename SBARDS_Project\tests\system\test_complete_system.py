#!/usr/bin/env python3
"""
Complete System Tests - SBARDS Project

This module provides comprehensive end-to-end system tests to ensure
the entire SBARDS system works correctly in the new structure.

System Test Categories:
- End-to-end workflow testing
- System integration testing
- Real-world scenario simulation
- Stress testing
- Regression testing
- Compatibility verification
"""

import os
import sys
import unittest
import tempfile
import time
import json
import threading
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import subprocess

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from core.config import ConfigManager
    from core.utils import FileUtils
    from core.logger import setup_logging, get_global_logger
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False

try:
    from static_analysis.python.yara_scanner import YaraScanner
    STATIC_ANALYSIS_AVAILABLE = True
except ImportError:
    STATIC_ANALYSIS_AVAILABLE = False

class TestCompleteWorkflow(unittest.TestCase):
    """Test complete end-to-end workflows."""
    
    def setUp(self):
        """Set up complete workflow test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "system_config.json"
        
        # Create comprehensive system configuration
        self.system_config = {
            "core": {
                "project_name": "SBARDS_System_Test",
                "version": "2.0.0",
                "debug_mode": True,
                "log_level": "INFO"
            },
            "static_analysis": {
                "yara_rules_directory": str(Path(self.temp_dir) / "yara_rules"),
                "parallel_processing": True,
                "max_threads": 4,
                "enable_caching": True,
                "timeout_seconds": 30
            },
            "scanner": {
                "enabled": True,
                "use_cpp_components": False,  # Use Python for testing
                "performance_mode": "high",
                "parallel_processing": True,
                "max_threads": 4
            },
            "api": {
                "host": "127.0.0.1",
                "port": 8000,
                "enable_cors": True
            },
            "output": {
                "enabled": True,
                "output_directory": str(Path(self.temp_dir) / "output"),
                "log_directory": str(Path(self.temp_dir) / "logs"),
                "format": "json",
                "detailed_reports": True
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(self.system_config, f, indent=2)
        
        # Create directory structure
        self.yara_rules_dir = Path(self.temp_dir) / "yara_rules"
        self.output_dir = Path(self.temp_dir) / "output"
        self.logs_dir = Path(self.temp_dir) / "logs"
        
        for directory in [self.yara_rules_dir, self.output_dir, self.logs_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Create test YARA rules
        self._create_test_yara_rules()
        
        # Create test files for scanning
        self._create_test_files()
    
    def tearDown(self):
        """Clean up complete workflow test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_test_yara_rules(self):
        """Create test YARA rules for system testing."""
        # Malware detection rule
        malware_rule = '''
rule SystemTest_Malware {
    meta:
        description = "System test malware detection rule"
        author = "SBARDS System Test"
        category = "malware"
        severity = "high"
    strings:
        $malware_pattern = "MALWARE_SIGNATURE_TEST"
        $suspicious_api = "CreateRemoteThread"
        $crypto_pattern = "CryptAcquireContext"
    condition:
        any of them
}
'''
        
        # Ransomware detection rule
        ransomware_rule = '''
rule SystemTest_Ransomware {
    meta:
        description = "System test ransomware detection rule"
        author = "SBARDS System Test"
        category = "ransomware"
        severity = "critical"
    strings:
        $ransom_note = "your files have been encrypted"
        $bitcoin_payment = "bitcoin payment"
        $decrypt_tool = "decrypt your files"
    condition:
        any of them
}
'''
        
        # Write rules to files
        with open(self.yara_rules_dir / "malware_test.yar", 'w') as f:
            f.write(malware_rule)
        
        with open(self.yara_rules_dir / "ransomware_test.yar", 'w') as f:
            f.write(ransomware_rule)
    
    def _create_test_files(self):
        """Create test files for scanning."""
        self.test_files = {}
        
        # Clean file
        clean_file = Path(self.temp_dir) / "clean_file.txt"
        with open(clean_file, 'w') as f:
            f.write("This is a clean file with normal content for testing.")
        self.test_files["clean"] = str(clean_file)
        
        # Malware-like file
        malware_file = Path(self.temp_dir) / "malware_file.txt"
        with open(malware_file, 'w') as f:
            f.write("This file contains MALWARE_SIGNATURE_TEST for detection.")
        self.test_files["malware"] = str(malware_file)
        
        # Ransomware-like file
        ransomware_file = Path(self.temp_dir) / "ransomware_file.txt"
        with open(ransomware_file, 'w') as f:
            f.write("Attention! Your files have been encrypted. Send bitcoin payment to decrypt your files.")
        self.test_files["ransomware"] = str(ransomware_file)
        
        # Suspicious file with API calls
        suspicious_file = Path(self.temp_dir) / "suspicious_file.txt"
        with open(suspicious_file, 'w') as f:
            f.write("This file contains CreateRemoteThread and CryptAcquireContext API calls.")
        self.test_files["suspicious"] = str(suspicious_file)
        
        # Large file for performance testing
        large_file = Path(self.temp_dir) / "large_file.txt"
        with open(large_file, 'w') as f:
            f.write("Large file content for performance testing.\n" * 10000)
        self.test_files["large"] = str(large_file)
    
    def test_complete_system_initialization(self):
        """Test complete system initialization."""
        # Test configuration loading
        config_manager = ConfigManager(str(self.config_file))
        self.assertTrue(config_manager.load_config())
        
        # Test logging setup
        log_file = self.logs_dir / "system_test.log"
        logger_manager = setup_logging(
            log_file=str(log_file),
            log_level="INFO"
        )
        self.assertIsNotNone(logger_manager)
        
        # Test logger functionality
        logger = get_global_logger().get_logger("system_test")
        logger.info("System initialization test message")
        
        # Verify log file was created
        self.assertTrue(log_file.exists())
        
        with open(log_file, 'r') as f:
            log_content = f.read()
            self.assertIn("System initialization test message", log_content)
    
    @unittest.skipIf(not STATIC_ANALYSIS_AVAILABLE, "Static analysis not available")
    def test_complete_scanning_workflow(self):
        """Test complete file scanning workflow."""
        # Initialize configuration
        config_manager = ConfigManager(str(self.config_file))
        config_manager.load_config()
        
        # Get static analysis configuration
        static_config = config_manager.get_section("static_analysis")
        
        # Initialize YARA scanner
        scanner = YaraScanner(static_config)
        
        # Test scanning each type of file
        scan_results = {}
        
        for file_type, file_path in self.test_files.items():
            # Scan file
            result = scanner.scan_file(file_path)
            scan_results[file_type] = result
            
            # Verify basic result structure
            self.assertIsNotNone(result)
            self.assertEqual(result.file_path, file_path)
            self.assertGreaterEqual(result.file_size, 0)
            self.assertGreaterEqual(result.scan_time, 0)
            self.assertIsInstance(result.matches, list)
        
        # Verify detection results
        # Clean file should have no or minimal matches
        clean_result = scan_results["clean"]
        self.assertLessEqual(len(clean_result.matches), 1)
        
        # Malware file should be detected
        malware_result = scan_results["malware"]
        if malware_result.matches:
            malware_rules = [match.rule_name for match in malware_result.matches]
            self.assertIn("SystemTest_Malware", malware_rules)
        
        # Ransomware file should be detected
        ransomware_result = scan_results["ransomware"]
        if ransomware_result.matches:
            ransomware_rules = [match.rule_name for match in ransomware_result.matches]
            self.assertIn("SystemTest_Ransomware", ransomware_rules)
        
        # Suspicious file should be detected
        suspicious_result = scan_results["suspicious"]
        if suspicious_result.matches:
            suspicious_rules = [match.rule_name for match in suspicious_result.matches]
            self.assertIn("SystemTest_Malware", suspicious_rules)
    
    def test_concurrent_system_operations(self):
        """Test concurrent system operations."""
        # Initialize system
        config_manager = ConfigManager(str(self.config_file))
        config_manager.load_config()
        
        results = []
        errors = []
        
        def concurrent_operation(file_path):
            """Perform concurrent file operations."""
            try:
                # Hash calculation
                file_hash = FileUtils.get_file_hash(file_path, "sha256")
                
                # File size check
                file_size = FileUtils.get_file_size(file_path)
                
                # File existence check
                file_exists = FileUtils.file_exists(file_path)
                
                results.append({
                    "file_path": file_path,
                    "hash": file_hash,
                    "size": file_size,
                    "exists": file_exists
                })
                
            except Exception as e:
                errors.append(f"Error processing {file_path}: {e}")
        
        # Run concurrent operations
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                executor.submit(concurrent_operation, file_path)
                for file_path in self.test_files.values()
            ]
            
            # Wait for completion
            for future in futures:
                future.result()
        
        # Verify results
        self.assertEqual(len(errors), 0, f"Concurrent operation errors: {errors}")
        self.assertEqual(len(results), len(self.test_files))
        
        # Verify all operations completed successfully
        for result in results:
            self.assertIsNotNone(result["hash"])
            self.assertGreater(result["size"], 0)
            self.assertTrue(result["exists"])
    
    def test_system_performance_under_load(self):
        """Test system performance under load."""
        import time
        
        # Create multiple files for load testing
        load_test_files = []
        for i in range(20):
            test_file = Path(self.temp_dir) / f"load_test_{i}.txt"
            with open(test_file, 'w') as f:
                f.write(f"Load test file {i} content.\n" * 100)
            load_test_files.append(str(test_file))
        
        # Measure performance under load
        start_time = time.time()
        
        # Process all files
        for file_path in load_test_files:
            file_hash = FileUtils.get_file_hash(file_path, "sha256")
            self.assertIsNotNone(file_hash)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Performance expectations
        files_per_second = len(load_test_files) / total_time
        self.assertGreater(files_per_second, 10)  # At least 10 files per second
        
        print(f"Load test performance: {files_per_second:.1f} files/sec")
    
    def test_error_recovery_and_resilience(self):
        """Test system error recovery and resilience."""
        # Test with various error conditions
        error_conditions = [
            "/non/existent/file.txt",  # Non-existent file
            "",  # Empty path
            self.temp_dir,  # Directory instead of file
        ]
        
        for error_condition in error_conditions:
            # System should handle errors gracefully
            try:
                file_exists = FileUtils.file_exists(error_condition)
                self.assertIsInstance(file_exists, bool)
                
                file_hash = FileUtils.get_file_hash(error_condition, "sha256")
                self.assertTrue(file_hash is None or isinstance(file_hash, str))
                
            except Exception as e:
                # Some exceptions are acceptable, but system should not crash
                self.assertIsInstance(e, (FileNotFoundError, PermissionError, OSError))
    
    def test_configuration_hot_reload(self):
        """Test configuration hot reload capability."""
        # Load initial configuration
        config_manager = ConfigManager(str(self.config_file))
        self.assertTrue(config_manager.load_config())
        
        initial_value = config_manager.get("core.project_name")
        self.assertEqual(initial_value, "SBARDS_System_Test")
        
        # Modify configuration file
        modified_config = self.system_config.copy()
        modified_config["core"]["project_name"] = "SBARDS_Modified_Test"
        
        with open(self.config_file, 'w') as f:
            json.dump(modified_config, f, indent=2)
        
        # Reload configuration
        self.assertTrue(config_manager.load_config())
        
        # Verify changes were loaded
        new_value = config_manager.get("core.project_name")
        self.assertEqual(new_value, "SBARDS_Modified_Test")

class TestRegressionPrevention(unittest.TestCase):
    """Test regression prevention for existing functionality."""
    
    def setUp(self):
        """Set up regression test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up regression test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_backward_compatibility(self):
        """Test backward compatibility with old interfaces."""
        # Test that old-style function calls still work
        test_file = Path(self.temp_dir) / "compatibility_test.txt"
        with open(test_file, 'w') as f:
            f.write("Backward compatibility test content")
        
        # Test old-style hash calculation
        hash_result = FileUtils.get_file_hash(str(test_file), "sha256")
        self.assertIsNotNone(hash_result)
        self.assertEqual(len(hash_result), 64)
        
        # Test old-style file operations
        exists_result = FileUtils.file_exists(str(test_file))
        self.assertTrue(exists_result)
        
        size_result = FileUtils.get_file_size(str(test_file))
        self.assertGreater(size_result, 0)
    
    def test_api_consistency(self):
        """Test API consistency and stability."""
        # Test that core APIs return consistent results
        test_file = Path(self.temp_dir) / "api_test.txt"
        test_content = "API consistency test content"
        
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        # Calculate hash multiple times
        hashes = []
        for _ in range(5):
            hash_result = FileUtils.get_file_hash(str(test_file), "sha256")
            hashes.append(hash_result)
        
        # All hashes should be identical
        unique_hashes = set(hashes)
        self.assertEqual(len(unique_hashes), 1)
        
        # File size should be consistent
        sizes = []
        for _ in range(5):
            size_result = FileUtils.get_file_size(str(test_file))
            sizes.append(size_result)
        
        unique_sizes = set(sizes)
        self.assertEqual(len(unique_sizes), 1)
        self.assertEqual(sizes[0], len(test_content))

def run_system_tests():
    """Run all system tests."""
    test_classes = [
        TestCompleteWorkflow,
        TestRegressionPrevention
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    print("🔄 Running Complete System Tests")
    print("=" * 50)
    
    success = run_system_tests()
    
    if success:
        print("\n✅ All system tests passed!")
        print("🎉 Complete system is working correctly")
        print("🚀 System is ready for production use")
    else:
        print("\n❌ Some system tests failed!")
        print("🔧 Please address system issues before deployment")
    
    exit(0 if success else 1)
