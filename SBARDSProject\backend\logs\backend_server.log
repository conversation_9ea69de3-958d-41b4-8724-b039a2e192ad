2025-05-21 21:04:05 | INFO     | SBARDS.Backend.Server | Logging to file: G:\SBARDS\SBARDSProject\backend\logs\backend_server.log
2025-05-21 21:04:05 | INFO     | SBARDS.Backend.Server | SBARDS Backend Server v1.0.0 starting up
2025-05-21 21:04:05 | INFO     | SBARDS.Backend.Server | Python version: 3.13.1
2025-05-21 21:04:05 | INFO     | SBARDS.Backend.Server | Platform: Windows-11-10.0.26100-SP0
2025-05-21 21:04:05 | INFO     | SBARDS.Backend.Server | Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-21 21:04:05 | INFO     | SBARDS.Backend.Server | Starting Uvicorn with configuration: {'app': 'app.main_new:app', 'host': '0.0.0.0', 'port': 8000, 'reload': False, 'workers': 1, 'log_level': 'info'}
2025-05-21 21:04:06 | ERROR    | SBARDS.Backend.Server | Error starting server: No module named 'pydantic_settings'
2025-05-21 21:04:52 | INFO     | SBARDS.Backend.Server | Logging to file: G:\SBARDS\SBARDSProject\backend\logs\backend_server.log
2025-05-21 21:04:52 | INFO     | SBARDS.Backend.Server | SBARDS Backend Server v1.0.0 starting up
2025-05-21 21:04:52 | INFO     | SBARDS.Backend.Server | Python version: 3.13.1
2025-05-21 21:04:52 | INFO     | SBARDS.Backend.Server | Platform: Windows-11-10.0.26100-SP0
2025-05-21 21:04:52 | INFO     | SBARDS.Backend.Server | Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-21 21:04:52 | INFO     | SBARDS.Backend.Server | Starting Uvicorn with configuration: {'app': 'app.main_new:app', 'host': '0.0.0.0', 'port': 8000, 'reload': False, 'workers': 1, 'log_level': 'info'}
2025-05-21 21:04:53 | ERROR    | SBARDS.Backend.Server | Error starting server: No module named 'sqlalchemy'
2025-05-21 21:05:40 | INFO     | SBARDS.Backend.Server | Logging to file: G:\SBARDS\SBARDSProject\backend\logs\backend_server.log
2025-05-21 21:05:40 | INFO     | SBARDS.Backend.Server | SBARDS Backend Server v1.0.0 starting up
2025-05-21 21:05:40 | INFO     | SBARDS.Backend.Server | Python version: 3.13.1
2025-05-21 21:05:40 | INFO     | SBARDS.Backend.Server | Platform: Windows-11-10.0.26100-SP0
2025-05-21 21:05:40 | INFO     | SBARDS.Backend.Server | Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-21 21:05:40 | INFO     | SBARDS.Backend.Server | Starting Uvicorn with configuration: {'app': 'app.main_new:app', 'host': '0.0.0.0', 'port': 8000, 'reload': False, 'workers': 1, 'log_level': 'info'}
2025-05-21 21:05:42 | ERROR    | SBARDS.Backend.Server | Error starting server: No module named 'jwt'
2025-05-21 21:07:45 | INFO     | SBARDS.Backend.Server | Logging to file: G:\SBARDS\SBARDSProject\backend\logs\backend_server.log
2025-05-21 21:07:45 | INFO     | SBARDS.Backend.Server | SBARDS Backend Server v1.0.0 starting up
2025-05-21 21:07:45 | INFO     | SBARDS.Backend.Server | Python version: 3.13.1
2025-05-21 21:07:45 | INFO     | SBARDS.Backend.Server | Platform: Windows-11-10.0.26100-SP0
2025-05-21 21:07:45 | INFO     | SBARDS.Backend.Server | Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-21 21:07:45 | INFO     | SBARDS.Backend.Server | Starting Uvicorn with configuration: {'app': 'app.main_new:app', 'host': '0.0.0.0', 'port': 8000, 'reload': False, 'workers': 1, 'log_level': 'info'}
2025-05-21 21:07:47 | ERROR    | SBARDS.Backend.Server | Error starting server: No module named 'aiohttp'
2025-05-21 21:08:39 | INFO     | SBARDS.Backend.Server | Logging to file: G:\SBARDS\SBARDSProject\backend\logs\backend_server.log
2025-05-21 21:08:39 | INFO     | SBARDS.Backend.Server | SBARDS Backend Server v1.0.0 starting up
2025-05-21 21:08:39 | INFO     | SBARDS.Backend.Server | Python version: 3.13.1
2025-05-21 21:08:39 | INFO     | SBARDS.Backend.Server | Platform: Windows-11-10.0.26100-SP0
2025-05-21 21:08:39 | INFO     | SBARDS.Backend.Server | Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-21 21:08:39 | INFO     | SBARDS.Backend.Server | Starting Uvicorn with configuration: {'app': 'app.main_new:app', 'host': '0.0.0.0', 'port': 8000, 'reload': False, 'workers': 1, 'log_level': 'info'}
2025-05-21 21:08:41 | ERROR    | fastapi | Form data requires "python-multipart" to be installed. 
You can install "python-multipart" with: 

pip install python-multipart

2025-05-21 21:08:41 | ERROR    | SBARDS.Backend.Server | Error starting server: Form data requires "python-multipart" to be installed. 
You can install "python-multipart" with: 

pip install python-multipart

2025-05-21 21:09:10 | INFO     | SBARDS.Backend.Server | Logging to file: G:\SBARDS\SBARDSProject\backend\logs\backend_server.log
2025-05-21 21:09:10 | INFO     | SBARDS.Backend.Server | SBARDS Backend Server v1.0.0 starting up
2025-05-21 21:09:10 | INFO     | SBARDS.Backend.Server | Python version: 3.13.1
2025-05-21 21:09:10 | INFO     | SBARDS.Backend.Server | Platform: Windows-11-10.0.26100-SP0
2025-05-21 21:09:10 | INFO     | SBARDS.Backend.Server | Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-21 21:09:10 | INFO     | SBARDS.Backend.Server | Starting Uvicorn with configuration: {'app': 'app.main_new:app', 'host': '0.0.0.0', 'port': 8000, 'reload': False, 'workers': 1, 'log_level': 'info'}
2025-05-21 21:09:12 | INFO     | SBARDS.Backend | Logging configured with level: INFO
2025-05-21 21:09:29 | INFO     | SBARDS.Backend.Server | Logging to file: G:\SBARDS\SBARDSProject\backend\logs\backend_server.log
2025-05-21 21:09:29 | INFO     | SBARDS.Backend.Server | SBARDS Backend Server v1.0.0 starting up
2025-05-21 21:09:29 | INFO     | SBARDS.Backend.Server | Python version: 3.13.1
2025-05-21 21:09:29 | INFO     | SBARDS.Backend.Server | Platform: Windows-11-10.0.26100-SP0
2025-05-21 21:09:29 | INFO     | SBARDS.Backend.Server | Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-21 21:09:29 | INFO     | SBARDS.Backend.Server | Starting Uvicorn with configuration: {'app': 'app.main_new:app', 'host': '0.0.0.0', 'port': 8000, 'reload': False, 'workers': 1, 'log_level': 'info'}
2025-05-21 21:09:30 | INFO     | SBARDS.Backend | Logging configured with level: INFO
2025-05-24 23:26:32 | INFO     | SBARDS.Backend.Server | Logging to file: G:\SBARDS\SBARDSProject\backend\logs\backend_server.log
2025-05-24 23:26:32 | INFO     | SBARDS.Backend.Server | SBARDS Backend Server v1.0.0 starting up
2025-05-24 23:26:32 | INFO     | SBARDS.Backend.Server | Python version: 3.13.1
2025-05-24 23:26:32 | INFO     | SBARDS.Backend.Server | Platform: Windows-11-10.0.26100-SP0
2025-05-24 23:26:32 | INFO     | SBARDS.Backend.Server | Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-24 23:26:32 | INFO     | SBARDS.Backend.Server | Starting Uvicorn with configuration: {'app': 'app.main_new:app', 'host': '0.0.0.0', 'port': 8000, 'reload': False, 'workers': 1, 'log_level': 'info'}
2025-05-24 23:26:36 | INFO     | SBARDS.Backend | Logging configured with level: INFO
