"""
Report schemas for SBARDS API

This module contains Pydantic schemas for scan reports.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from .files import FileResultCreate, FileResultResponse


class ScanReportBase(BaseModel):
    """Base schema for scan reports."""
    
    scan_id: str = Field(..., description="Unique identifier for the scan")
    scan_path: str = Field(..., description="Path that was scanned")
    files_scanned: int = Field(..., description="Number of files scanned")
    threats_found: int = Field(..., description="Number of threats found")
    report_path: str = Field(..., description="Path to the HTML report file")
    scan_type: Optional[str] = Field("standard", description="Type of scan performed")


class ScanReportCreate(ScanReportBase):
    """Schema for creating scan reports."""
    
    report_content: str = Field(..., description="Content of the HTML report")
    performance_metrics: Optional[Dict[str, Any]] = Field(None, description="Performance metrics")
    file_results: List[FileResultCreate] = Field([], description="List of file results")


class ScanReportResponse(ScanReportBase):
    """Schema for scan report responses."""
    
    id: int = Field(..., description="Database ID")
    timestamp: datetime = Field(..., description="Timestamp when the scan was created")
    performance_metrics: Optional[Dict[str, Any]] = Field(None, description="Performance metrics")
    file_results: List[FileResultResponse] = Field([], description="List of file results")

    class Config:
        """Pydantic config."""
        orm_mode = True


class ScanReportList(BaseModel):
    """Schema for paginated scan report lists."""
    
    reports: List[ScanReportResponse] = Field(..., description="List of scan reports")
    total: int = Field(..., description="Total number of reports")
    skip: int = Field(..., description="Number of reports skipped")
    limit: int = Field(..., description="Maximum number of reports returned")


class ScanReportSummary(BaseModel):
    """Schema for scan report summary."""
    
    id: int = Field(..., description="Database ID")
    scan_id: str = Field(..., description="Unique identifier for the scan")
    timestamp: datetime = Field(..., description="Timestamp when the scan was created")
    scan_path: str = Field(..., description="Path that was scanned")
    files_scanned: int = Field(..., description="Number of files scanned")
    threats_found: int = Field(..., description="Number of threats found")
    scan_type: Optional[str] = Field("standard", description="Type of scan performed")

    class Config:
        """Pydantic config."""
        orm_mode = True
