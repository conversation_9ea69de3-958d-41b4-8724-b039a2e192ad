2025-05-25 22:50:18,687 - layer.api_capture - WARNING - [capture.py:132] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-25 22:50:18,691 - layer.api_capture - INFO - [capture.py:147] - initialize_capture_layer() - Capture layer initialized successfully
2025-05-25 22:53:48,415 - layer.api_capture - WARNING - [capture.py:132] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-25 22:53:48,419 - layer.api_capture - INFO - [capture.py:147] - initialize_capture_layer() - Capture layer initialized successfully
2025-05-25 22:58:37,645 - layer.api_capture - INFO - [capture.py:162] - shutdown_capture_layer() - File interceptor stopped
2025-05-26 02:01:03,756 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:01:03,757 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:01:05,821 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_020105_756529_mock_file.txt
2025-05-26 02:03:42,682 - layer.api_capture - INFO - [capture.py:300] - upload_file() - File uploaded successfully through True Capture Layer: test.txt -> 6905d5624faa1a5f (size: 27, time: 0.145s)
2025-05-26 02:03:42,682 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_020342_536582_test.txt
2025-05-26 02:11:42,872 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:11:42,872 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:11:44,934 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_021144_872645_mock_file.txt
2025-05-26 02:15:43,240 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:15:43,241 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:15:45,300 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_021545_241320_mock_file.txt
2025-05-26 02:20:00,431 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 02:24:46,813 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:24:46,813 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:24:48,869 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_022448_813938_mock_file.txt
2025-05-26 02:48:13,602 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 02:48:13,602 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 02:48:15,658 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_024815_602867_mock_file.txt
2025-05-26 02:48:36,891 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 03:10:38,765 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 03:10:38,766 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 03:10:40,832 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_031040_766508_mock_file.txt
2025-05-26 03:12:53,989 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 03:12:53,991 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 03:12:56,047 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_031255_989892_mock_file.txt
2025-05-26 03:39:34,237 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 03:39:34,238 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 03:39:36,382 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_033936_238811_mock_file.txt
2025-05-26 03:40:05,597 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 04:26:56,402 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 04:26:56,403 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 04:26:58,489 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_042658_404480_mock_file.txt
2025-05-26 04:29:38,394 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 04:29:38,394 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 04:29:40,410 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 04:29:55,751 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 04:30:06,037 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 04:30:06,038 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 04:30:08,102 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_043008_038060_mock_file.txt
2025-05-26 04:36:56,944 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 05:00:29,588 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 05:00:29,589 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 05:00:31,761 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_050031_679425_mock_file.txt
2025-05-26 05:03:53,264 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 05:07:02,402 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 05:07:02,403 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 05:07:04,430 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 05:07:25,284 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 05:07:25,285 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 05:07:27,344 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_050727_285160_mock_file.txt
2025-05-26 05:13:37,048 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 05:58:16,034 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 05:58:16,035 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 05:58:18,053 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 06:05:31,498 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 06:05:31,499 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 06:05:33,752 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_060533_499343_mock_file.txt
2025-05-26 06:28:42,471 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 06:28:42,472 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 06:28:44,545 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_062844_472045_mock_file.txt
2025-05-26 06:30:12,111 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-26 06:33:02,330 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-26 06:33:02,331 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-26 06:33:04,402 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_063304_332899_mock_file.txt
2025-05-27 21:06:47,321 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 22:13:04,518 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:13:04,519 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:13:06,603 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_221306_520479_mock_file.txt
2025-05-27 22:15:30,374 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:15:30,376 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:15:32,457 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_221532_375136_mock_file.txt
2025-05-27 22:16:00,619 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 22:16:35,486 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:16:35,487 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:16:37,537 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_221637_486714_mock_file.txt
2025-05-27 22:21:09,674 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 22:27:49,753 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:27:49,754 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:27:51,850 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_222751_753997_mock_file.txt
2025-05-27 22:29:12,988 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:29:12,989 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:29:15,053 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_222914_988961_mock_file.txt
2025-05-27 22:34:50,470 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 22:36:33,964 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:36:33,965 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:36:36,037 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_223635_964521_mock_file.txt
2025-05-27 22:36:54,215 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 22:38:40,918 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:38:40,919 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:38:42,992 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_223842_919662_mock_file.txt
2025-05-27 22:39:07,157 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 22:43:55,094 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:43:55,095 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:43:57,180 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_224357_095168_mock_file.txt
2025-05-27 22:47:22,723 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 22:51:23,922 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:51:23,923 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:51:26,106 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_225125_933375_mock_file.txt
2025-05-27 22:51:53,319 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 22:54:45,454 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 22:54:45,455 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 22:54:47,532 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_225447_455535_mock_file.txt
2025-05-27 23:01:10,186 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 23:11:58,272 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:11:58,273 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:12:00,371 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_231200_272871_mock_file.txt
2025-05-27 23:12:43,683 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 23:12:56,666 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:12:56,667 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:12:58,729 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_231258_667013_mock_file.txt
2025-05-27 23:13:37,947 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:13:37,948 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:13:40,109 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_231339_949396_mock_file.txt
2025-05-27 23:24:11,348 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 23:31:32,409 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:31:32,410 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:31:34,489 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_233134_410710_mock_file.txt
2025-05-27 23:31:54,657 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 23:34:58,952 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:34:58,953 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:35:01,048 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_233500_953124_mock_file.txt
2025-05-27 23:42:32,782 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:42:32,783 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:42:35,045 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_234234_783502_mock_file.txt
2025-05-27 23:44:09,716 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 23:44:24,036 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:44:24,037 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:44:26,142 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_234426_037788_mock_file.txt
2025-05-27 23:47:50,544 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:47:50,545 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:47:52,848 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_234752_636275_mock_file.txt
2025-05-27 23:49:21,546 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 23:49:30,101 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:49:30,102 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:49:32,356 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_234932_102798_mock_file.txt
2025-05-27 23:50:18,654 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 23:55:04,626 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:55:04,627 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:55:06,748 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_235506_646244_mock_file.txt
2025-05-27 23:56:09,376 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-27 23:57:09,194 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:57:09,195 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:57:11,268 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_235711_194653_mock_file.txt
2025-05-27 23:58:11,722 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-27 23:58:11,723 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-27 23:58:13,817 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250527_235813_726251_mock_file.txt
2025-05-28 00:02:43,876 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:04:52,378 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:04:52,379 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:04:54,476 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_000454_379252_mock_file.txt
2025-05-28 00:05:41,808 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:05:41,809 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:05:42,821 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:06:54,488 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:07:10,552 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:07:10,552 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:07:12,609 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_000712_552639_mock_file.txt
2025-05-28 00:07:44,876 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:13:51,282 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:13:51,284 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:13:53,366 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_001353_282616_mock_file.txt
2025-05-28 00:14:09,493 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:16:57,458 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:16:57,459 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:16:59,545 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_001659_459782_mock_file.txt
2025-05-28 00:17:13,683 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:18:56,542 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:18:56,543 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:18:58,847 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_001858_647997_mock_file.txt
2025-05-28 00:19:59,407 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:20:09,160 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:20:09,161 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:20:11,231 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_002011_160763_mock_file.txt
2025-05-28 00:22:32,394 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:26:03,069 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:26:03,070 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:26:05,125 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_002605_069823_mock_file.txt
2025-05-28 00:27:29,815 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:28:38,160 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:28:38,161 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:28:40,232 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_002840_161266_mock_file.txt
2025-05-28 00:29:01,433 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:30:11,619 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:30:11,620 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:30:13,672 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_003013_619868_mock_file.txt
2025-05-28 00:30:48,943 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 00:31:34,378 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:31:34,379 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:31:36,478 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_003136_379719_mock_file.txt
2025-05-28 00:37:59,424 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:37:59,425 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:38:01,611 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_003801_508803_mock_file.txt
2025-05-28 00:39:14,313 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 00:39:14,313 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 00:39:16,370 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_003916_313633_mock_file.txt
2025-05-28 00:45:10,995 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 01:04:43,825 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 01:04:43,826 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 01:04:46,107 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_010445_892165_mock_file.txt
2025-05-28 01:06:04,769 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 01:16:40,885 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 01:16:40,887 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 01:16:43,085 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_011642_999397_mock_file.txt
2025-05-28 01:17:07,297 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 01:17:31,610 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 01:17:31,611 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 01:17:33,812 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_011733_646200_mock_file.txt
2025-05-28 01:18:29,254 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 01:21:59,711 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 01:21:59,712 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 01:22:01,942 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_012201_746969_mock_file.txt
2025-05-28 01:23:47,760 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
2025-05-28 01:41:11,307 - layer.api_capture - INFO - [capture.py:144] - initialize_capture_layer() - True Capture Layer initialized successfully
2025-05-28 01:41:11,310 - layer.api_capture - WARNING - [capture.py:179] - initialize_capture_layer() - Redis initialization failed: Redis library not available. Install with: pip install redis
2025-05-28 01:41:13,484 - layer.api_capture - INFO - [capture.py:135] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250528_014113_341427_mock_file.txt
2025-05-28 01:42:09,935 - layer.api_capture - INFO - [capture.py:193] - shutdown_capture_layer() - True Capture Layer stopped and cleaned up
