# CMakeLists.txt for SBARDS Capture Layer C++ Components
# High-performance file monitoring and permission analysis

cmake_minimum_required(VERSION 3.16)
project(SBARDS_Capture VERSION 2.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    # Visual Studio
    add_compile_options(/W4 /WX /permissive-)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
else()
    # GCC/Clang
    add_compile_options(-Wall -Wextra -Werror -pedantic)
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG -march=native")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g3 -fsanitize=address")
endif()

# Find required packages
find_package(Threads REQUIRED)

# Platform-specific libraries
if(WIN32)
    set(PLATFORM_LIBS shlwapi advapi32)
else()
    set(PLATFORM_LIBS pthread)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# File Monitor Library
add_library(sbards_file_monitor SHARED
    file_monitor.cpp
)

target_link_libraries(sbards_file_monitor
    ${PLATFORM_LIBS}
    Threads::Threads
)

# Set library properties
set_target_properties(sbards_file_monitor PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
    PUBLIC_HEADER "file_monitor.h"
)

# Permission Manager Library
add_library(sbards_permission_manager SHARED
    permission_manager.cpp
)

target_link_libraries(sbards_permission_manager
    ${PLATFORM_LIBS}
    Threads::Threads
)

set_target_properties(sbards_permission_manager PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
    PUBLIC_HEADER "permission_manager.h"
)

# File Monitor Executable (for testing)
add_executable(file_monitor_test
    file_monitor.cpp
)

target_link_libraries(file_monitor_test
    ${PLATFORM_LIBS}
    Threads::Threads
)

target_compile_definitions(file_monitor_test PRIVATE
    STANDALONE_EXECUTABLE
)

# Permission Manager Executable (for testing)
add_executable(permission_manager_test
    permission_manager.cpp
)

target_link_libraries(permission_manager_test
    ${PLATFORM_LIBS}
    Threads::Threads
)

target_compile_definitions(permission_manager_test PRIVATE
    STANDALONE_EXECUTABLE
)

# Combined Capture Library
add_library(sbards_capture SHARED
    file_monitor.cpp
    permission_manager.cpp
)

target_link_libraries(sbards_capture
    ${PLATFORM_LIBS}
    Threads::Threads
)

set_target_properties(sbards_capture PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
)

# Installation rules
install(TARGETS sbards_file_monitor sbards_permission_manager sbards_capture
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    PUBLIC_HEADER DESTINATION include/sbards
)

install(TARGETS file_monitor_test permission_manager_test
    RUNTIME DESTINATION bin
)

# Create header files for libraries
file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/file_monitor.h
"#ifndef SBARDS_FILE_MONITOR_H
#define SBARDS_FILE_MONITOR_H

#ifdef __cplusplus
extern \"C\" {
#endif

// C interface for Python integration
void* create_file_monitor();
void destroy_file_monitor(void* monitor);
bool start_monitoring(void* monitor);
void stop_monitoring(void* monitor);

#ifdef __cplusplus
}
#endif

#endif // SBARDS_FILE_MONITOR_H
")

file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/permission_manager.h
"#ifndef SBARDS_PERMISSION_MANAGER_H
#define SBARDS_PERMISSION_MANAGER_H

#ifdef __cplusplus
extern \"C\" {
#endif

// C interface for Python integration
void* create_permission_manager();
void destroy_permission_manager(void* manager);
const char* analyze_file_permissions(void* manager, const char* file_path);

#ifdef __cplusplus
}
#endif

#endif // SBARDS_PERMISSION_MANAGER_H
")

# Copy headers to build directory
configure_file(${CMAKE_CURRENT_BINARY_DIR}/file_monitor.h 
               ${CMAKE_CURRENT_SOURCE_DIR}/file_monitor.h COPYONLY)
configure_file(${CMAKE_CURRENT_BINARY_DIR}/permission_manager.h 
               ${CMAKE_CURRENT_SOURCE_DIR}/permission_manager.h COPYONLY)

# Testing
enable_testing()

add_test(NAME file_monitor_basic_test
    COMMAND file_monitor_test
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

add_test(NAME permission_manager_basic_test
    COMMAND permission_manager_test
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Custom targets for development
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --verbose
    DEPENDS file_monitor_test permission_manager_test
)

add_custom_target(benchmark
    COMMAND echo "Running performance benchmarks..."
    COMMAND file_monitor_test
    COMMAND permission_manager_test
    DEPENDS file_monitor_test permission_manager_test
)

# Package configuration
set(CPACK_PACKAGE_NAME "SBARDS-Capture")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "SBARDS Capture Layer - High Performance File Monitoring")
set(CPACK_PACKAGE_VENDOR "SBARDS Project")

if(WIN32)
    set(CPACK_GENERATOR "ZIP;NSIS")
else()
    set(CPACK_GENERATOR "TGZ;DEB")
endif()

include(CPack)

# Print configuration summary
message(STATUS "")
message(STATUS "SBARDS Capture Layer Configuration Summary:")
message(STATUS "==========================================")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "Platform: ${CMAKE_SYSTEM_NAME} ${CMAKE_SYSTEM_VERSION}")
message(STATUS "Architecture: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
message(STATUS "Components to build:")
message(STATUS "  - File Monitor Library: sbards_file_monitor")
message(STATUS "  - Permission Manager Library: sbards_permission_manager")
message(STATUS "  - Combined Capture Library: sbards_capture")
message(STATUS "  - Test Executables: file_monitor_test, permission_manager_test")
message(STATUS "")
message(STATUS "Build commands:")
message(STATUS "  mkdir build && cd build")
message(STATUS "  cmake ..")
message(STATUS "  cmake --build . --config ${CMAKE_BUILD_TYPE}")
message(STATUS "  ctest --verbose")
message(STATUS "")
