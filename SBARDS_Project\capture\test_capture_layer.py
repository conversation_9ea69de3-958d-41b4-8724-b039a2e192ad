#!/usr/bin/env python3
"""
Test Suite for SBARDS Capture Layer

This comprehensive test suite validates all components of the capture layer:
- C++ file monitor integration
- Permission manager functionality
- File interceptor operations
- Redis queue management
- FastAPI integration

Usage:
    python test_capture_layer.py
"""

import os
import sys
import time
import tempfile
import unittest
import threading
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from capture.python.file_interceptor import FileInterceptor, FileInterceptionEvent
from capture.python.redis_queue import RedisQueueManager
from core.logger import get_global_logger

# FastAPIFileHandler - سنقوم بإنشائه
class FastAPIFileHandler:
    def __init__(self, interceptor, temp_storage_path):
        self.interceptor = interceptor
        self.temp_storage_path = Path(temp_storage_path)
        self.temp_storage_path.mkdir(exist_ok=True)

    async def handle_file_upload(self, file_data, filename):
        return {
            "status": "success",
            "original_filename": filename,
            "file_size": len(file_data)
        }

    def _sanitize_filename(self, filename):
        import re
        # Remove dangerous characters
        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        safe_filename = safe_filename.replace('..', '_')
        return safe_filename

class TestFileInterceptor(unittest.TestCase):
    """Test cases for FileInterceptor class."""

    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.config = {
            "target_directories": [self.test_dir],
            "recursive": True,
            "max_depth": 3,
            "exclude_dirs": [".git"],
            "exclude_extensions": [".tmp"],
            "max_file_size_mb": 10,
            "threads": 2
        }
        self.interceptor = FileInterceptor(self.config)

    def tearDown(self):
        """Clean up test environment."""
        if self.interceptor.running:
            self.interceptor.stop()

        # Clean up test directory
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_initialization(self):
        """Test FileInterceptor initialization."""
        self.assertIsNotNone(self.interceptor)
        self.assertEqual(self.interceptor.target_directories, [self.test_dir])
        self.assertEqual(self.interceptor.thread_count, 2)
        self.assertFalse(self.interceptor.running)

    def test_start_stop(self):
        """Test starting and stopping the interceptor."""
        # Test start
        self.assertTrue(self.interceptor.start())
        self.assertTrue(self.interceptor.running)

        # Test double start
        self.assertFalse(self.interceptor.start())

        # Test stop
        self.interceptor.stop()
        self.assertFalse(self.interceptor.running)

    def test_file_interception(self):
        """Test file interception functionality."""
        # Create test file
        test_file = os.path.join(self.test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")

        # Intercept file
        event = self.interceptor.intercept_file(test_file, "created")

        self.assertIsNotNone(event)
        self.assertEqual(event.file_path, test_file)
        self.assertEqual(event.event_type, "created")
        self.assertFalse(event.is_suspicious)

    def test_suspicious_file_detection(self):
        """Test suspicious file detection."""
        # Create suspicious file
        suspicious_file = os.path.join(self.test_dir, "malware.exe")
        with open(suspicious_file, 'w') as f:
            f.write("fake executable")

        event = self.interceptor.intercept_file(suspicious_file, "created")

        self.assertIsNotNone(event)
        self.assertTrue(event.is_suspicious)
        self.assertGreaterEqual(event.priority, 3)

    def test_directory_scanning(self):
        """Test directory scanning functionality."""
        # Create test files
        for i in range(5):
            test_file = os.path.join(self.test_dir, f"file_{i}.txt")
            with open(test_file, 'w') as f:
                f.write(f"content {i}")

        events = self.interceptor.scan_directories()
        self.assertGreaterEqual(len(events), 5)

    def test_file_filtering(self):
        """Test file filtering functionality."""
        # Create excluded file
        excluded_file = os.path.join(self.test_dir, "temp.tmp")
        with open(excluded_file, 'w') as f:
            f.write("temporary content")

        event = self.interceptor.intercept_file(excluded_file, "created")
        self.assertIsNone(event)

    def test_callback_registration(self):
        """Test callback registration and execution."""
        callback_called = False
        received_event = None

        def test_callback(event):
            nonlocal callback_called, received_event
            callback_called = True
            received_event = event

        self.interceptor.add_callback(test_callback)

        # Start interceptor and process a file
        self.interceptor.start()

        test_file = os.path.join(self.test_dir, "callback_test.txt")
        with open(test_file, 'w') as f:
            f.write("callback test")

        event = self.interceptor.intercept_file(test_file, "created")

        # Wait for processing
        time.sleep(0.5)

        self.assertTrue(callback_called)
        self.assertIsNotNone(received_event)

class TestFastAPIFileHandler(unittest.TestCase):
    """Test cases for FastAPIFileHandler class."""

    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.config = {
            "target_directories": [self.test_dir],
            "threads": 1
        }
        self.interceptor = FileInterceptor(self.config)
        self.handler = FastAPIFileHandler(self.interceptor, self.test_dir)

    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_initialization(self):
        """Test FastAPIFileHandler initialization."""
        self.assertIsNotNone(self.handler)
        self.assertEqual(self.handler.interceptor, self.interceptor)
        self.assertTrue(self.handler.temp_storage_path.exists())

    async def test_file_upload(self):
        """Test file upload handling."""
        file_data = b"test file content"
        filename = "test_upload.txt"

        result = await self.handler.handle_file_upload(file_data, filename)

        self.assertEqual(result["status"], "success")
        self.assertEqual(result["original_filename"], filename)
        self.assertEqual(result["file_size"], len(file_data))

    def test_filename_sanitization(self):
        """Test filename sanitization."""
        dangerous_filename = "../../malicious<>file.txt"
        safe_filename = self.handler._sanitize_filename(dangerous_filename)

        self.assertNotIn("..", safe_filename)
        self.assertNotIn("<", safe_filename)
        self.assertNotIn(">", safe_filename)

class TestRedisQueueManager(unittest.TestCase):
    """Test cases for RedisQueueManager class."""

    def setUp(self):
        """Set up test environment."""
        self.config = {
            "redis_host": "localhost",
            "redis_port": 6379,
            "redis_db": 15,  # Use test database
            "queue_prefix": "sbards_test",
            "max_retries": 2
        }

        try:
            self.queue_manager = RedisQueueManager(self.config)
            self.redis_available = self.queue_manager.is_available()
        except Exception:
            self.redis_available = False
            self.queue_manager = None

    def tearDown(self):
        """Clean up test environment."""
        if self.redis_available and self.queue_manager:
            self.queue_manager.clear_queues()
            if self.queue_manager.running:
                self.queue_manager.stop()

    def test_initialization(self):
        """Test RedisQueueManager initialization."""
        if not self.redis_available:
            self.skipTest("Redis not available")

        self.assertIsNotNone(self.queue_manager)
        self.assertTrue(self.queue_manager.is_available())

    def test_message_sending(self):
        """Test message sending functionality."""
        if not self.redis_available:
            self.skipTest("Redis not available")

        test_data = {"test": "data", "number": 123}
        result = self.queue_manager.send_message("test_message", test_data, "normal")

        self.assertTrue(result)
        self.assertEqual(self.queue_manager.stats["messages_sent"], 1)

    def test_queue_sizes(self):
        """Test queue size monitoring."""
        if not self.redis_available:
            self.skipTest("Redis not available")

        # Send test messages
        for i in range(3):
            self.queue_manager.send_message("test", {"id": i}, "normal")

        queue_sizes = self.queue_manager.get_queue_sizes()
        self.assertGreaterEqual(queue_sizes["normal_priority"], 3)

    def test_consumer_registration(self):
        """Test consumer registration and message processing."""
        if not self.redis_available:
            self.skipTest("Redis not available")

        processed_messages = []

        def test_consumer(data):
            processed_messages.append(data)
            return True

        self.queue_manager.register_consumer("test_message", test_consumer)
        self.queue_manager.start(worker_count=1)

        # Send test message
        test_data = {"test": "consumer"}
        self.queue_manager.send_message("test_message", test_data, "normal")

        # Wait for processing
        time.sleep(1)

        self.assertEqual(len(processed_messages), 1)
        self.assertEqual(processed_messages[0], test_data)

class TestCppIntegration(unittest.TestCase):
    """Test cases for C++ component integration."""

    def test_file_monitor_compilation(self):
        """Test if C++ file monitor can be compiled."""
        cpp_file = Path(__file__).parent / "cpp" / "file_monitor.cpp"
        self.assertTrue(cpp_file.exists(), "file_monitor.cpp should exist")

    def test_permission_manager_compilation(self):
        """Test if C++ permission manager can be compiled."""
        cpp_file = Path(__file__).parent / "cpp" / "permission_manager.cpp"
        self.assertTrue(cpp_file.exists(), "permission_manager.cpp should exist")

    @patch('ctypes.CDLL')
    def test_cpp_library_loading(self, mock_cdll):
        """Test C++ library loading simulation."""
        # Mock successful library loading
        mock_lib = Mock()
        mock_cdll.return_value = mock_lib

        # Simulate C++ function calls
        mock_lib.create_file_monitor.return_value = 12345
        mock_lib.start_monitoring.return_value = True

        # Test the mocked functionality
        monitor_ptr = mock_lib.create_file_monitor()
        self.assertEqual(monitor_ptr, 12345)

        result = mock_lib.start_monitoring(monitor_ptr)
        self.assertTrue(result)

class TestIntegrationScenarios(unittest.TestCase):
    """Integration test scenarios for the entire capture layer."""

    def setUp(self):
        """Set up integration test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.config = {
            "target_directories": [self.test_dir],
            "recursive": True,
            "max_depth": 2,
            "threads": 2
        }

    def tearDown(self):
        """Clean up integration test environment."""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_end_to_end_file_processing(self):
        """Test complete file processing pipeline."""
        # Initialize components
        interceptor = FileInterceptor(self.config)

        processed_files = []

        def processing_callback(event):
            processed_files.append(event.file_path)

        interceptor.add_callback(processing_callback)

        # Start interceptor
        self.assertTrue(interceptor.start())

        # Create test files
        test_files = []
        for i in range(3):
            test_file = os.path.join(self.test_dir, f"integration_test_{i}.txt")
            with open(test_file, 'w') as f:
                f.write(f"integration test content {i}")
            test_files.append(test_file)

        # Process files
        for test_file in test_files:
            event = interceptor.intercept_file(test_file, "created")
            self.assertIsNotNone(event)

        # Wait for processing
        time.sleep(1)

        # Verify processing
        self.assertGreaterEqual(len(processed_files), 3)

        # Stop interceptor
        interceptor.stop()

    def test_performance_benchmarking(self):
        """Test performance benchmarking of the capture layer."""
        interceptor = FileInterceptor(self.config)

        # Create multiple test files
        num_files = 50
        test_files = []

        start_time = time.time()

        for i in range(num_files):
            test_file = os.path.join(self.test_dir, f"perf_test_{i}.txt")
            with open(test_file, 'w') as f:
                f.write(f"performance test content {i}")
            test_files.append(test_file)

        creation_time = time.time() - start_time

        # Start interceptor
        interceptor.start()

        # Process files
        processing_start = time.time()
        events = []

        for test_file in test_files:
            event = interceptor.intercept_file(test_file, "created")
            if event:
                events.append(event)

        processing_time = time.time() - processing_start

        # Calculate performance metrics
        files_per_second = len(events) / processing_time if processing_time > 0 else 0

        print(f"\nPerformance Metrics:")
        print(f"  Files created: {num_files} in {creation_time:.3f}s")
        print(f"  Files processed: {len(events)} in {processing_time:.3f}s")
        print(f"  Processing rate: {files_per_second:.2f} files/second")

        # Verify performance
        self.assertGreater(files_per_second, 10, "Processing rate should be > 10 files/second")

        interceptor.stop()

if __name__ == "__main__":
    # Configure logging for tests
    import logging
    logging.basicConfig(level=logging.INFO)

    # Create test loader
    loader = unittest.TestLoader()

    # Create test suite
    test_suite = unittest.TestSuite()

    # Add test cases
    test_suite.addTests(loader.loadTestsFromTestCase(TestFileInterceptor))
    test_suite.addTests(loader.loadTestsFromTestCase(TestFastAPIFileHandler))
    test_suite.addTests(loader.loadTestsFromTestCase(TestRedisQueueManager))
    test_suite.addTests(loader.loadTestsFromTestCase(TestCppIntegration))
    test_suite.addTests(loader.loadTestsFromTestCase(TestIntegrationScenarios))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # Print summary
    print(f"\n{'='*50}")
    print(f"SBARDS Capture Layer Test Results")
    print(f"{'='*50}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")

    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100)
        print(f"Success rate: {success_rate:.1f}%")

    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")

    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
