{"metadata": {"version": "2.0.0", "last_updated": "2025-05-25T00:00:00Z", "description": "Safe files whitelist for SBARDS v2.0", "total_entries": 150, "categories": ["system_files", "trusted_software", "development_tools", "media_files", "documents"]}, "system_files": {"windows": [{"path": "C:\\Windows\\System32\\kernel32.dll", "sha256": "a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890", "description": "Windows Kernel32 Library", "verified": true, "digital_signature": "Microsoft Corporation"}, {"path": "C:\\Windows\\System32\\ntdll.dll", "sha256": "b2c3d4e5f6789012345678901234567890123456789012345678901234567890a1", "description": "NT Layer DLL", "verified": true, "digital_signature": "Microsoft Corporation"}, {"path": "C:\\Windows\\System32\\user32.dll", "sha256": "c3d4e5f6789012345678901234567890123456789012345678901234567890a1b2", "description": "Windows User API", "verified": true, "digital_signature": "Microsoft Corporation"}, {"path": "C:\\Windows\\System32\\advapi32.dll", "sha256": "d4e5f6789012345678901234567890123456789012345678901234567890a1b2c3", "description": "Advanced Windows API", "verified": true, "digital_signature": "Microsoft Corporation"}], "linux": [{"path": "/lib/x86_64-linux-gnu/libc.so.6", "sha256": "e5f6789012345678901234567890123456789012345678901234567890a1b2c3d4", "description": "GNU C Library", "verified": true, "package": "libc6"}, {"path": "/bin/bash", "sha256": "f6789012345678901234567890123456789012345678901234567890a1b2c3d4e5", "description": "Bash Shell", "verified": true, "package": "bash"}, {"path": "/usr/bin/python3", "sha256": "789012345678901234567890123456789012345678901234567890a1b2c3d4e5f6", "description": "Python 3 Interpreter", "verified": true, "package": "python3"}]}, "trusted_software": {"antivirus": [{"name": "Windows Defender", "path": "C:\\Program Files\\Windows Defender\\MsMpEng.exe", "sha256": "89012345678901234567890123456789012345678901234567890a1b2c3d4e5f67", "vendor": "Microsoft Corporation", "verified": true, "category": "security"}, {"name": "Malwarebytes", "path": "C:\\Program Files\\Malwarebytes\\Anti-Malware\\mbam.exe", "sha256": "9012345678901234567890123456789012345678901234567890a1b2c3d4e5f678", "vendor": "Malwarebytes Corporation", "verified": true, "category": "security"}], "browsers": [{"name": "Google Chrome", "path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "sha256": "012345678901234567890123456789012345678901234567890a1b2c3d4e5f6789", "vendor": "Google LLC", "verified": true, "category": "browser"}, {"name": "Mozilla Firefox", "path": "C:\\Program Files\\Mozilla Firefox\\firefox.exe", "sha256": "12345678901234567890123456789012345678901234567890a1b2c3d4e5f67890", "vendor": "Mozilla Corporation", "verified": true, "category": "browser"}], "office_suites": [{"name": "Microsoft Word", "path": "C:\\Program Files\\Microsoft Office\\Office16\\WINWORD.EXE", "sha256": "2345678901234567890123456789012345678901234567890a1b2c3d4e5f678901", "vendor": "Microsoft Corporation", "verified": true, "category": "productivity"}, {"name": "Microsoft Excel", "path": "C:\\Program Files\\Microsoft Office\\Office16\\EXCEL.EXE", "sha256": "345678901234567890123456789012345678901234567890a1b2c3d4e5f6789012", "vendor": "Microsoft Corporation", "verified": true, "category": "productivity"}]}, "development_tools": [{"name": "Visual Studio Code", "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "sha256": "45678901234567890123456789012345678901234567890a1b2c3d4e5f67890123", "vendor": "Microsoft Corporation", "verified": true, "category": "development"}, {"name": "Git", "path": "C:\\Program Files\\Git\\bin\\git.exe", "sha256": "5678901234567890123456789012345678901234567890a1b2c3d4e5f678901234", "vendor": "Git for Windows", "verified": true, "category": "development"}, {"name": "Node.js", "path": "C:\\Program Files\\nodejs\\node.exe", "sha256": "678901234567890123456789012345678901234567890a1b2c3d4e5f6789012345", "vendor": "Node.js Foundation", "verified": true, "category": "development"}], "media_files": {"safe_extensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp", ".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".pdf", ".txt", ".rtf", ".odt", ".docx", ".xlsx", ".pptx"], "safe_mime_types": ["image/jpeg", "image/png", "image/gif", "image/bmp", "audio/mpeg", "audio/wav", "audio/flac", "video/mp4", "video/avi", "video/quicktime", "application/pdf", "text/plain", "text/rtf"]}, "documents": {"safe_patterns": [{"pattern": "*.pdf", "description": "PDF documents", "max_size": 52428800, "scan_content": true}, {"pattern": "*.txt", "description": "Text files", "max_size": 10485760, "scan_content": false}, {"pattern": "*.docx", "description": "Word documents", "max_size": 52428800, "scan_content": true, "check_macros": true}]}, "trusted_sources": [{"domain": "microsoft.com", "description": "Microsoft Corporation", "trust_level": "high", "verified": true}, {"domain": "google.com", "description": "Google LLC", "trust_level": "high", "verified": true}, {"domain": "mozilla.org", "description": "Mozilla Foundation", "trust_level": "high", "verified": true}, {"domain": "github.com", "description": "GitHub Inc.", "trust_level": "medium", "verified": true, "note": "Verify individual repositories"}], "exclusions": {"directories": ["C:\\Windows\\System32\\", "C:\\Windows\\SysWOW64\\", "C:\\Program Files\\Windows Defender\\", "/usr/bin/", "/usr/lib/", "/lib/", "/bin/"], "file_patterns": ["*.dll", "*.so", "*.dyl<PERSON>", "*.sys"], "conditions": [{"type": "digital_signature", "value": "Microsoft Corporation", "action": "whitelist"}, {"type": "file_size", "operator": "less_than", "value": 1024, "action": "skip_scan"}]}, "validation_rules": {"require_digital_signature": true, "max_file_age_days": 365, "min_trust_score": 80, "verify_checksums": true, "check_reputation": true}}