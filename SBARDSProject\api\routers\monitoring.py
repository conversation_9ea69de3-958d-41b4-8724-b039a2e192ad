"""
Monitoring Router for SBARDS API

This module provides the monitoring router for the SBARDS API.
"""

import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel

# Create router
router = APIRouter()

# Global monitor manager instance
_manager = None

def set_manager(manager):
    """Set the global monitor manager instance."""
    global _manager
    _manager = manager

def get_manager():
    """Get the global monitor manager instance."""
    if _manager is None:
        raise HTTPException(status_code=503, detail="Monitor manager not initialized")
    return _manager

# Models
class MonitoringStatus(BaseModel):
    """Monitoring status model."""
    is_running: bool
    start_time: Optional[float] = None
    uptime_seconds: Optional[float] = None
    active_monitors: List[str] = []
    alert_count: int = 0
    process_count: int = 0
    file_operation_count: int = 0
    network_connection_count: int = 0

class Alert(BaseModel):
    """Alert model."""
    id: str
    timestamp: float
    level: str
    source: str
    message: str
    details: Dict[str, Any] = {}

class Process(BaseModel):
    """Process model."""
    pid: int
    name: str
    path: Optional[str] = None
    command_line: Optional[str] = None
    username: Optional[str] = None
    cpu_percent: Optional[float] = None
    memory_percent: Optional[float] = None
    status: Optional[str] = None
    create_time: Optional[float] = None

class FileOperation(BaseModel):
    """File operation model."""
    id: str
    timestamp: float
    operation: str
    path: str
    process_id: Optional[int] = None
    process_name: Optional[str] = None
    details: Dict[str, Any] = {}

class NetworkConnection(BaseModel):
    """Network connection model."""
    id: str
    timestamp: float
    process_id: Optional[int] = None
    process_name: Optional[str] = None
    local_address: str
    local_port: int
    remote_address: str
    remote_port: int
    status: str
    protocol: str

# Endpoints
@router.get("/status", response_model=MonitoringStatus)
async def get_status(
    manager = Depends(get_manager)
):
    """Get monitoring status."""
    try:
        # Get monitoring status
        status = manager.get_status()
        return status
    except Exception as e:
        logging.error(f"Error getting monitoring status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting monitoring status: {str(e)}")

@router.post("/start")
async def start_monitoring(
    background_tasks: BackgroundTasks,
    manager = Depends(get_manager)
):
    """Start monitoring."""
    try:
        # Check if already running
        if manager.is_running():
            return {"status": "already_running"}
        
        # Start monitoring in background
        background_tasks.add_task(manager.start_monitoring)
        
        return {"status": "started"}
    except Exception as e:
        logging.error(f"Error starting monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting monitoring: {str(e)}")

@router.post("/stop")
async def stop_monitoring(
    manager = Depends(get_manager)
):
    """Stop monitoring."""
    try:
        # Check if running
        if not manager.is_running():
            return {"status": "not_running"}
        
        # Stop monitoring
        manager.stop_monitoring()
        
        return {"status": "stopped"}
    except Exception as e:
        logging.error(f"Error stopping monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Error stopping monitoring: {str(e)}")

@router.get("/alerts", response_model=List[Alert])
async def get_alerts(
    limit: int = 20,
    manager = Depends(get_manager)
):
    """Get recent alerts."""
    try:
        # Get alerts
        alerts = manager.get_alerts(limit)
        return alerts
    except Exception as e:
        logging.error(f"Error getting alerts: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting alerts: {str(e)}")

@router.get("/processes", response_model=List[Process])
async def get_processes(
    manager = Depends(get_manager)
):
    """Get current processes."""
    try:
        # Get processes
        processes = manager.get_processes()
        return processes
    except Exception as e:
        logging.error(f"Error getting processes: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting processes: {str(e)}")

@router.get("/file-operations", response_model=List[FileOperation])
async def get_file_operations(
    limit: int = 20,
    manager = Depends(get_manager)
):
    """Get recent file operations."""
    try:
        # Get file operations
        file_operations = manager.get_file_operations(limit)
        return file_operations
    except Exception as e:
        logging.error(f"Error getting file operations: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting file operations: {str(e)}")

@router.get("/network-connections", response_model=List[NetworkConnection])
async def get_network_connections(
    manager = Depends(get_manager)
):
    """Get current network connections."""
    try:
        # Get network connections
        network_connections = manager.get_network_connections()
        return network_connections
    except Exception as e:
        logging.error(f"Error getting network connections: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting network connections: {str(e)}")
