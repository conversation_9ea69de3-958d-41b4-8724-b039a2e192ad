"""
Monitor Manager for SBARDS

This module provides the monitor manager for the monitoring phase of the SBARDS project.
"""

import os
import sys
import time
import uuid
import logging
import threading
import platform
from typing import Dict, List, Any, Optional, Tuple, Set

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import core components
from core.config import ConfigLoader
from core.logging import Logger
from core.utils import get_platform, is_windows, is_linux

class MonitorManager:
    """
    Monitor manager for the monitoring phase.
    
    This class provides mechanisms for:
    1. Managing monitoring components
    2. Coordinating monitoring activities
    3. Processing monitoring events
    4. Generating alerts
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        Initialize monitor manager.
        
        Args:
            config_path (str, optional): Path to configuration file
        """
        # Load configuration
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.get_config()
        self.monitoring_config = self.config.get("monitoring", {})
        
        # Set up logging
        log_dir = self.config.get("output", {}).get("log_directory", "logs")
        log_level = self.config.get("output", {}).get("log_level", "info")
        self.logger = Logger(log_dir, log_level).get_logger("Monitoring")
        
        # Initialize monitoring components
        self.process_monitor = None
        self.filesystem_monitor = None
        self.network_monitor = None
        self.event_correlator = None
        self.alert_manager = None
        self.response_manager = None
        
        # Initialize monitoring state
        self.is_running_flag = False
        self.start_time = None
        self.stop_event = threading.Event()
        self.monitoring_thread = None
        
        # Initialize monitoring metrics
        self.metrics = {
            "alerts": 0,
            "processes": 0,
            "file_operations": 0,
            "network_connections": 0,
            "correlated_events": 0,
            "responses": 0
        }
        
        # Initialize orchestrator reference
        self.orchestrator = None
    
    def _initialize_components(self) -> None:
        """Initialize monitoring components."""
        self.logger.info("Initializing monitoring components")
        
        try:
            # Initialize alert manager first
            from .alert_manager import AlertManager
            self.alert_manager = AlertManager(self.monitoring_config.get("alert", {}))
            
            # Initialize process monitor
            if self.monitoring_config.get("process_monitoring", True):
                from .process_monitor import ProcessMonitor
                self.process_monitor = ProcessMonitor(
                    self.monitoring_config.get("process", {}),
                    self.alert_manager
                )
            
            # Initialize filesystem monitor
            if self.monitoring_config.get("filesystem_monitoring", True):
                from .filesystem_monitor import FilesystemMonitor
                self.filesystem_monitor = FilesystemMonitor(
                    self.monitoring_config.get("filesystem", {}),
                    self.alert_manager
                )
            
            # Initialize network monitor
            if self.monitoring_config.get("network_monitoring", True):
                from .network_monitor import NetworkMonitor
                self.network_monitor = NetworkMonitor(
                    self.monitoring_config.get("network", {}),
                    self.alert_manager
                )
            
            # Initialize event correlator
            from .event_correlator import EventCorrelator
            self.event_correlator = EventCorrelator(
                self.monitoring_config.get("correlation", {}),
                self.alert_manager
            )
            
            # Initialize response manager
            from .response_manager import ResponseManager
            self.response_manager = ResponseManager(
                self.monitoring_config.get("response", {}),
                self.alert_manager
            )
            
            self.logger.info("Monitoring components initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing monitoring components: {e}")
            raise
    
    def start_monitoring(self) -> bool:
        """
        Start monitoring.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        if self.is_running_flag:
            self.logger.warning("Monitoring is already running")
            return True
        
        self.logger.info("Starting monitoring")
        
        try:
            # Initialize components if not already initialized
            if not self.alert_manager:
                self._initialize_components()
            
            # Reset stop event
            self.stop_event.clear()
            
            # Set start time
            self.start_time = time.time()
            
            # Start monitoring components
            if self.process_monitor:
                self.process_monitor.start()
            
            if self.filesystem_monitor:
                self.filesystem_monitor.start()
            
            if self.network_monitor:
                self.network_monitor.start()
            
            if self.event_correlator:
                self.event_correlator.start()
            
            if self.response_manager:
                self.response_manager.start()
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            
            self.is_running_flag = True
            self.logger.info("Monitoring started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting monitoring: {e}")
            self.stop_monitoring()
            return False
    
    def stop_monitoring(self) -> bool:
        """
        Stop monitoring.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        if not self.is_running_flag:
            self.logger.warning("Monitoring is not running")
            return True
        
        self.logger.info("Stopping monitoring")
        
        try:
            # Set stop event
            self.stop_event.set()
            
            # Stop monitoring components
            if self.process_monitor:
                self.process_monitor.stop()
            
            if self.filesystem_monitor:
                self.filesystem_monitor.stop()
            
            if self.network_monitor:
                self.network_monitor.stop()
            
            if self.event_correlator:
                self.event_correlator.stop()
            
            if self.response_manager:
                self.response_manager.stop()
            
            # Wait for monitoring thread to stop
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5.0)
            
            self.is_running_flag = False
            self.logger.info("Monitoring stopped")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping monitoring: {e}")
            return False
    
    def _monitoring_loop(self) -> None:
        """Monitoring loop."""
        check_interval = self.monitoring_config.get("check_interval_seconds", 1.0)
        
        while not self.stop_event.is_set():
            try:
                # Update metrics
                self._update_metrics()
                
                # Wait for next check
                self.stop_event.wait(check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
    
    def _update_metrics(self) -> None:
        """Update monitoring metrics."""
        try:
            # Update alert count
            if self.alert_manager:
                self.metrics["alerts"] = self.alert_manager.get_alert_count()
            
            # Update process count
            if self.process_monitor:
                self.metrics["processes"] = self.process_monitor.get_process_count()
            
            # Update file operation count
            if self.filesystem_monitor:
                self.metrics["file_operations"] = self.filesystem_monitor.get_operation_count()
            
            # Update network connection count
            if self.network_monitor:
                self.metrics["network_connections"] = self.network_monitor.get_connection_count()
            
            # Update correlated event count
            if self.event_correlator:
                self.metrics["correlated_events"] = self.event_correlator.get_event_count()
            
            # Update response count
            if self.response_manager:
                self.metrics["responses"] = self.response_manager.get_response_count()
                
        except Exception as e:
            self.logger.error(f"Error updating metrics: {e}")
    
    def is_running(self) -> bool:
        """
        Check if monitoring is running.
        
        Returns:
            bool: True if running, False otherwise
        """
        return self.is_running_flag
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get monitoring status.
        
        Returns:
            Dict[str, Any]: Monitoring status
        """
        # Calculate uptime
        uptime_seconds = None
        if self.start_time and self.is_running_flag:
            uptime_seconds = time.time() - self.start_time
        
        # Get active monitors
        active_monitors = []
        if self.process_monitor and self.process_monitor.is_running():
            active_monitors.append("process")
        if self.filesystem_monitor and self.filesystem_monitor.is_running():
            active_monitors.append("filesystem")
        if self.network_monitor and self.network_monitor.is_running():
            active_monitors.append("network")
        if self.event_correlator and self.event_correlator.is_running():
            active_monitors.append("correlation")
        if self.response_manager and self.response_manager.is_running():
            active_monitors.append("response")
        
        return {
            "is_running": self.is_running_flag,
            "start_time": self.start_time,
            "uptime_seconds": uptime_seconds,
            "active_monitors": active_monitors,
            "alert_count": self.metrics["alerts"],
            "process_count": self.metrics["processes"],
            "file_operation_count": self.metrics["file_operations"],
            "network_connection_count": self.metrics["network_connections"],
            "correlated_event_count": self.metrics["correlated_events"],
            "response_count": self.metrics["responses"]
        }
    
    def get_alerts(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent alerts.
        
        Args:
            limit (int, optional): Maximum number of alerts to return
            
        Returns:
            List[Dict[str, Any]]: Recent alerts
        """
        if not self.alert_manager:
            return []
        
        return self.alert_manager.get_recent_alerts(limit)
    
    def get_processes(self) -> List[Dict[str, Any]]:
        """
        Get current processes.
        
        Returns:
            List[Dict[str, Any]]: Current processes
        """
        if not self.process_monitor:
            return []
        
        return self.process_monitor.get_current_processes()
    
    def get_file_operations(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent file operations.
        
        Args:
            limit (int, optional): Maximum number of operations to return
            
        Returns:
            List[Dict[str, Any]]: Recent file operations
        """
        if not self.filesystem_monitor:
            return []
        
        return self.filesystem_monitor.get_recent_operations(limit)
    
    def get_network_connections(self) -> List[Dict[str, Any]]:
        """
        Get current network connections.
        
        Returns:
            List[Dict[str, Any]]: Current network connections
        """
        if not self.network_monitor:
            return []
        
        return self.network_monitor.get_current_connections()
    
    def get_recent_alerts(self) -> List[Dict[str, Any]]:
        """
        Get recent alerts for coordination.
        
        Returns:
            List[Dict[str, Any]]: Recent alerts
        """
        if not self.alert_manager:
            return []
        
        return self.alert_manager.get_recent_alerts(10)
    
    def handle_detection(self, detection: Dict[str, Any]) -> None:
        """
        Handle detection from prescanning phase.
        
        Args:
            detection (Dict[str, Any]): Detection data
        """
        self.logger.info(f"Received detection: {detection}")
        
        # Create alert from detection
        if self.alert_manager:
            self.alert_manager.add_alert(
                level="warning",
                source="prescanning",
                message=f"Suspicious file detected: {detection.get('file_path')}",
                details=detection
            )
        
        # Monitor file if filesystem monitor is available
        if self.filesystem_monitor and "file_path" in detection:
            self.filesystem_monitor.monitor_file(detection["file_path"])
    
    def monitor_process(self, process_id: int, priority: int = 5) -> None:
        """
        Monitor a specific process.
        
        Args:
            process_id (int): Process ID
            priority (int, optional): Priority level
        """
        if not self.process_monitor:
            return
        
        self.logger.info(f"Monitoring process {process_id} with priority {priority}")
        self.process_monitor.monitor_process(process_id, priority)
