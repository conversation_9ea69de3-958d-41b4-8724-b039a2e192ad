"""
VirusTotal service for SBARDS API

This module provides integration with VirusTotal API for file scanning.
"""

import json
import asyncio
from typing import Dict, Any, Optional
import requests
import aiohttp

from core.config import get_config
from core.logger import get_global_logger

# Configure logging
logger = get_global_logger().get_layer_logger("api.services.virustotal")


class VirusTotalService:
    """Service for VirusTotal integration."""
    
    def __init__(self):
        """Initialize the VirusTotal service."""
        self.config = get_config()
        self.logger = logger
        self.api_key = self.config.get("virustotal_api_key", "")
        self.api_url = "https://www.virustotal.com/api/v3"
        self.rate_limit_delay = 15  # seconds between requests for free tier
    
    async def check_file_hash(self, file_hash: str) -> Dict[str, Any]:
        """
        Check a file hash against VirusTotal API.
        
        Args:
            file_hash (str): SHA-256 hash of the file.
            
        Returns:
            Dict[str, Any]: VirusTotal API response.
        """
        if not self.api_key:
            self.logger.warning("VirusTotal API key not configured")
            return {"error": "VirusTotal API key not configured"}
        
        try:
            headers = {
                "x-apikey": self.api_key,
                "Accept": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_url}/files/{file_hash}"
                
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        self.logger.info(f"VirusTotal check successful for hash: {file_hash[:16]}...")
                        return result
                    elif response.status == 404:
                        self.logger.info(f"File not found in VirusTotal database: {file_hash[:16]}...")
                        return {"error": "File not found in VirusTotal database"}
                    else:
                        error_text = await response.text()
                        self.logger.error(f"VirusTotal API error: {response.status} - {error_text}")
                        return {"error": f"VirusTotal API error: {response.status}"}
                        
        except Exception as e:
            self.logger.error(f"Error checking VirusTotal: {e}")
            return {"error": str(e)}
    
    async def submit_file(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Submit a file to VirusTotal for analysis.
        
        Args:
            file_content (bytes): File content.
            filename (str): Name of the file.
            
        Returns:
            Dict[str, Any]: VirusTotal API response.
        """
        if not self.api_key:
            self.logger.warning("VirusTotal API key not configured")
            return {"error": "VirusTotal API key not configured"}
        
        # Check file size (VirusTotal limit is 32MB for free tier)
        if len(file_content) > 32 * 1024 * 1024:
            return {"error": "File too large for VirusTotal submission (max 32MB)"}
        
        try:
            headers = {
                "x-apikey": self.api_key
            }
            
            files = {
                "file": (filename, file_content)
            }
            
            # Use requests for file upload (aiohttp file upload is more complex)
            response = requests.post(f"{self.api_url}/files", headers=headers, files=files)
            
            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"File submitted to VirusTotal: {filename}")
                return result
            else:
                self.logger.error(f"VirusTotal submission error: {response.status_code} - {response.text}")
                return {"error": f"VirusTotal submission error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error submitting file to VirusTotal: {e}")
            return {"error": str(e)}
    
    def parse_virustotal_result(self, vt_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse VirusTotal result and extract useful information.
        
        Args:
            vt_result (Dict[str, Any]): Raw VirusTotal result.
            
        Returns:
            Dict[str, Any]: Parsed result with summary.
        """
        try:
            if "error" in vt_result:
                return {
                    "status": "error",
                    "message": vt_result["error"],
                    "threat_detected": False,
                    "detection_count": 0
                }
            
            if "data" not in vt_result or "attributes" not in vt_result["data"]:
                return {
                    "status": "no_data",
                    "message": "No analysis data available",
                    "threat_detected": False,
                    "detection_count": 0
                }
            
            attributes = vt_result["data"]["attributes"]
            
            if "last_analysis_stats" not in attributes:
                return {
                    "status": "no_analysis",
                    "message": "No analysis statistics available",
                    "threat_detected": False,
                    "detection_count": 0
                }
            
            stats = attributes["last_analysis_stats"]
            malicious_count = stats.get("malicious", 0)
            suspicious_count = stats.get("suspicious", 0)
            
            # Determine threat level
            threat_level = "safe"
            if malicious_count >= 10:
                threat_level = "critical"
            elif malicious_count >= 5:
                threat_level = "high"
            elif malicious_count >= 2:
                threat_level = "medium"
            elif malicious_count >= 1 or suspicious_count >= 3:
                threat_level = "low"
            
            return {
                "status": "analyzed",
                "threat_detected": malicious_count > 0,
                "detection_count": malicious_count,
                "suspicious_count": suspicious_count,
                "threat_level": threat_level,
                "total_engines": sum(stats.values()),
                "stats": stats,
                "scan_date": attributes.get("last_analysis_date"),
                "file_type": attributes.get("type_description", "Unknown")
            }
            
        except Exception as e:
            self.logger.error(f"Error parsing VirusTotal result: {e}")
            return {
                "status": "parse_error",
                "message": f"Error parsing result: {str(e)}",
                "threat_detected": False,
                "detection_count": 0
            }
    
    async def check_multiple_hashes(self, file_hashes: list) -> Dict[str, Dict[str, Any]]:
        """
        Check multiple file hashes against VirusTotal.
        
        Args:
            file_hashes (list): List of SHA-256 hashes.
            
        Returns:
            Dict[str, Dict[str, Any]]: Results for each hash.
        """
        results = {}
        
        for i, file_hash in enumerate(file_hashes):
            try:
                result = await self.check_file_hash(file_hash)
                results[file_hash] = result
                
                # Rate limiting - wait between requests
                if i < len(file_hashes) - 1:  # Don't wait after the last request
                    await asyncio.sleep(self.rate_limit_delay)
                    
            except Exception as e:
                self.logger.error(f"Error checking hash {file_hash}: {e}")
                results[file_hash] = {"error": str(e)}
        
        return results
