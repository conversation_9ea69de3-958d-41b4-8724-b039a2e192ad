"""
Advanced Report Generator for SBARDS Static Analysis Layer

This module generates comprehensive security analysis reports combining results from
all static analysis components (C++ and Python) into detailed, actionable reports.

Features:
- Multi-format report generation (JSON, HTML, PDF, XML)
- Risk assessment and scoring
- Executive summaries
- Detailed technical findings
- Remediation recommendations
- Trend analysis and statistics
- Integration with MongoDB for historical data
"""

import os
import sys
import json
import time
import hashlib
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.logger import get_global_logger
from core.utils import FileUtils, SecurityUtils, PerformanceUtils
from core.constants import ThreatLevel, FileStatus

@dataclass
class StaticAnalysisResult:
    """Comprehensive static analysis result structure."""
    file_path: str
    file_hash: str
    file_size: int
    file_type: str
    analysis_timestamp: float
    
    # Signature analysis
    signature_valid: bool = False
    signature_info: Dict[str, Any] = None
    
    # Permission analysis
    permission_score: int = 0
    permission_issues: List[str] = None
    permission_recommendations: List[str] = None
    
    # Entropy analysis
    entropy_score: float = 0.0
    entropy_suspicious: bool = False
    entropy_details: Dict[str, Any] = None
    
    # YARA analysis
    yara_matches: List[Dict[str, Any]] = None
    yara_threat_level: str = "SAFE"
    
    # VirusTotal analysis
    vt_detection_ratio: str = "0/0"
    vt_threat_names: List[str] = None
    vt_scan_date: Optional[str] = None
    
    # Overall assessment
    overall_threat_level: str = "SAFE"
    overall_score: int = 100
    is_malicious: bool = False
    confidence_level: float = 0.0
    
    # Metadata
    analysis_duration: float = 0.0
    analyzer_version: str = "2.0.0"
    
    def __post_init__(self):
        if self.signature_info is None:
            self.signature_info = {}
        if self.permission_issues is None:
            self.permission_issues = []
        if self.permission_recommendations is None:
            self.permission_recommendations = []
        if self.entropy_details is None:
            self.entropy_details = {}
        if self.yara_matches is None:
            self.yara_matches = []
        if self.vt_threat_names is None:
            self.vt_threat_names = []

class StaticAnalysisReportGenerator:
    """
    Advanced report generator for static analysis results.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the report generator.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = get_global_logger().get_layer_logger("static_analysis")
        
        # Report configuration
        self.output_dir = Path(config.get("output_dir", "static_analysis/reports"))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.template_dir = Path(config.get("template_dir", "static_analysis/templates"))
        self.include_charts = config.get("include_charts", True)
        self.include_recommendations = config.get("include_recommendations", True)
        self.include_executive_summary = config.get("include_executive_summary", True)
        
        # Risk thresholds
        self.risk_thresholds = {
            "critical": config.get("critical_threshold", 90),
            "high": config.get("high_threshold", 70),
            "medium": config.get("medium_threshold", 40),
            "low": config.get("low_threshold", 20)
        }
        
        # Statistics
        self.stats = {
            "reports_generated": 0,
            "total_files_analyzed": 0,
            "malicious_files_detected": 0,
            "false_positives": 0,
            "start_time": time.time()
        }
        
        self.logger.info("StaticAnalysisReportGenerator initialized")
    
    def generate_comprehensive_report(self, results: List[StaticAnalysisResult], 
                                    report_format: str = "json") -> Dict[str, Any]:
        """
        Generate a comprehensive analysis report.
        
        Args:
            results (List[StaticAnalysisResult]): Analysis results
            report_format (str): Output format (json, html, pdf, xml)
            
        Returns:
            Dict[str, Any]: Generated report data
        """
        start_time = time.time()
        
        try:
            # Generate report data
            report_data = self._create_report_structure(results)
            
            # Add executive summary
            if self.include_executive_summary:
                report_data["executive_summary"] = self._generate_executive_summary(results)
            
            # Add detailed analysis
            report_data["detailed_analysis"] = self._generate_detailed_analysis(results)
            
            # Add statistics and trends
            report_data["statistics"] = self._generate_statistics(results)
            
            # Add recommendations
            if self.include_recommendations:
                report_data["recommendations"] = self._generate_recommendations(results)
            
            # Save report in requested format
            report_file = self._save_report(report_data, report_format)
            report_data["report_file"] = str(report_file)
            
            # Update statistics
            self.stats["reports_generated"] += 1
            self.stats["total_files_analyzed"] += len(results)
            self.stats["malicious_files_detected"] += sum(1 for r in results if r.is_malicious)
            
            generation_time = time.time() - start_time
            self.logger.info(f"Report generated in {generation_time:.2f}s: {report_file}")
            
            return report_data
            
        except Exception as e:
            self.logger.error(f"Failed to generate report: {e}")
            raise
    
    def generate_single_file_report(self, result: StaticAnalysisResult) -> Dict[str, Any]:
        """
        Generate a report for a single file analysis.
        
        Args:
            result (StaticAnalysisResult): Single file analysis result
            
        Returns:
            Dict[str, Any]: Single file report
        """
        report = {
            "report_type": "single_file",
            "generation_time": datetime.now().isoformat(),
            "file_info": {
                "path": result.file_path,
                "hash": result.file_hash,
                "size": result.file_size,
                "type": result.file_type
            },
            "analysis_results": asdict(result),
            "risk_assessment": self._assess_file_risk(result),
            "recommendations": self._generate_file_recommendations(result)
        }
        
        return report
    
    def _create_report_structure(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Create the basic report structure."""
        return {
            "report_metadata": {
                "report_id": hashlib.md5(f"{time.time()}".encode()).hexdigest()[:8],
                "generation_time": datetime.now().isoformat(),
                "analyzer_version": "SBARDS 2.0.0",
                "total_files": len(results),
                "analysis_duration": sum(r.analysis_duration for r in results)
            },
            "scan_summary": {
                "files_scanned": len(results),
                "malicious_files": sum(1 for r in results if r.is_malicious),
                "suspicious_files": sum(1 for r in results if r.overall_threat_level in ["HIGH", "MEDIUM"]),
                "clean_files": sum(1 for r in results if r.overall_threat_level == "SAFE"),
                "scan_duration": sum(r.analysis_duration for r in results)
            }
        }
    
    def _generate_executive_summary(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Generate executive summary."""
        total_files = len(results)
        malicious_count = sum(1 for r in results if r.is_malicious)
        high_risk_count = sum(1 for r in results if r.overall_threat_level == "HIGH")
        medium_risk_count = sum(1 for r in results if r.overall_threat_level == "MEDIUM")
        
        # Calculate overall security posture
        if malicious_count > 0:
            security_posture = "CRITICAL"
            posture_description = f"{malicious_count} malicious files detected"
        elif high_risk_count > total_files * 0.1:
            security_posture = "HIGH_RISK"
            posture_description = f"{high_risk_count} high-risk files detected"
        elif medium_risk_count > total_files * 0.2:
            security_posture = "MODERATE_RISK"
            posture_description = f"{medium_risk_count} medium-risk files detected"
        else:
            security_posture = "LOW_RISK"
            posture_description = "No significant threats detected"
        
        return {
            "security_posture": security_posture,
            "posture_description": posture_description,
            "key_findings": [
                f"Analyzed {total_files} files",
                f"Detected {malicious_count} malicious files",
                f"Found {high_risk_count} high-risk files",
                f"Identified {medium_risk_count} medium-risk files"
            ],
            "immediate_actions": self._get_immediate_actions(results),
            "risk_distribution": {
                "critical": malicious_count,
                "high": high_risk_count,
                "medium": medium_risk_count,
                "low": total_files - malicious_count - high_risk_count - medium_risk_count
            }
        }
    
    def _generate_detailed_analysis(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Generate detailed analysis section."""
        analysis = {
            "signature_analysis": self._analyze_signatures(results),
            "permission_analysis": self._analyze_permissions(results),
            "entropy_analysis": self._analyze_entropy(results),
            "yara_analysis": self._analyze_yara_results(results),
            "virustotal_analysis": self._analyze_virustotal_results(results)
        }
        
        return analysis
    
    def _generate_statistics(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Generate statistics and metrics."""
        if not results:
            return {}
        
        # File type distribution
        file_types = {}
        for result in results:
            file_type = result.file_type or "unknown"
            file_types[file_type] = file_types.get(file_type, 0) + 1
        
        # Threat level distribution
        threat_levels = {}
        for result in results:
            level = result.overall_threat_level
            threat_levels[level] = threat_levels.get(level, 0) + 1
        
        # Average scores
        avg_overall_score = sum(r.overall_score for r in results) / len(results)
        avg_permission_score = sum(r.permission_score for r in results) / len(results)
        avg_entropy_score = sum(r.entropy_score for r in results) / len(results)
        
        return {
            "file_type_distribution": file_types,
            "threat_level_distribution": threat_levels,
            "average_scores": {
                "overall": round(avg_overall_score, 2),
                "permission": round(avg_permission_score, 2),
                "entropy": round(avg_entropy_score, 2)
            },
            "detection_metrics": {
                "total_yara_matches": sum(len(r.yara_matches) for r in results),
                "files_with_yara_matches": sum(1 for r in results if r.yara_matches),
                "files_with_vt_detections": sum(1 for r in results if r.vt_detection_ratio != "0/0"),
                "high_entropy_files": sum(1 for r in results if r.entropy_suspicious)
            }
        }
    
    def _generate_recommendations(self, results: List[StaticAnalysisResult]) -> List[Dict[str, Any]]:
        """Generate security recommendations."""
        recommendations = []
        
        # Analyze common issues
        malicious_files = [r for r in results if r.is_malicious]
        high_risk_files = [r for r in results if r.overall_threat_level == "HIGH"]
        permission_issues = [r for r in results if r.permission_issues]
        high_entropy_files = [r for r in results if r.entropy_suspicious]
        
        if malicious_files:
            recommendations.append({
                "priority": "CRITICAL",
                "category": "Malware Detection",
                "title": "Immediate Malware Remediation Required",
                "description": f"Found {len(malicious_files)} confirmed malicious files",
                "action": "Quarantine and remove all detected malware immediately",
                "affected_files": [r.file_path for r in malicious_files[:5]]  # Limit to 5 examples
            })
        
        if high_risk_files:
            recommendations.append({
                "priority": "HIGH",
                "category": "Risk Mitigation",
                "title": "High-Risk Files Require Review",
                "description": f"Found {len(high_risk_files)} high-risk files",
                "action": "Review and validate legitimacy of high-risk files",
                "affected_files": [r.file_path for r in high_risk_files[:5]]
            })
        
        if permission_issues:
            recommendations.append({
                "priority": "MEDIUM",
                "category": "Permission Security",
                "title": "Fix File Permission Issues",
                "description": f"Found {len(permission_issues)} files with permission issues",
                "action": "Review and correct file permissions according to security best practices",
                "affected_files": [r.file_path for r in permission_issues[:5]]
            })
        
        if high_entropy_files:
            recommendations.append({
                "priority": "MEDIUM",
                "category": "Encryption Detection",
                "title": "Review High-Entropy Files",
                "description": f"Found {len(high_entropy_files)} files with high entropy (possible encryption/packing)",
                "action": "Analyze high-entropy files for potential obfuscation or encryption",
                "affected_files": [r.file_path for r in high_entropy_files[:5]]
            })
        
        return recommendations
    
    def _assess_file_risk(self, result: StaticAnalysisResult) -> Dict[str, Any]:
        """Assess risk for a single file."""
        risk_factors = []
        
        if result.is_malicious:
            risk_factors.append("Confirmed malware detection")
        
        if result.yara_matches:
            risk_factors.append(f"{len(result.yara_matches)} YARA rule matches")
        
        if result.entropy_suspicious:
            risk_factors.append("High entropy (possible obfuscation)")
        
        if result.permission_issues:
            risk_factors.append("Permission security issues")
        
        if result.vt_detection_ratio != "0/0":
            risk_factors.append(f"VirusTotal detections: {result.vt_detection_ratio}")
        
        return {
            "threat_level": result.overall_threat_level,
            "confidence": result.confidence_level,
            "risk_factors": risk_factors,
            "overall_score": result.overall_score
        }
    
    def _generate_file_recommendations(self, result: StaticAnalysisResult) -> List[str]:
        """Generate recommendations for a single file."""
        recommendations = []
        
        if result.is_malicious:
            recommendations.append("IMMEDIATE: Quarantine this file - confirmed malware")
        
        if result.overall_threat_level == "HIGH":
            recommendations.append("HIGH PRIORITY: Review this file for potential threats")
        
        if result.permission_issues:
            recommendations.extend(result.permission_recommendations)
        
        if result.entropy_suspicious:
            recommendations.append("Analyze for potential obfuscation or packing")
        
        if result.yara_matches:
            recommendations.append("Review YARA rule matches for threat indicators")
        
        return recommendations
    
    def _analyze_signatures(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Analyze signature validation results."""
        valid_signatures = sum(1 for r in results if r.signature_valid)
        invalid_signatures = len(results) - valid_signatures
        
        return {
            "valid_signatures": valid_signatures,
            "invalid_signatures": invalid_signatures,
            "signature_validation_rate": (valid_signatures / len(results)) * 100 if results else 0
        }
    
    def _analyze_permissions(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Analyze permission security results."""
        files_with_issues = sum(1 for r in results if r.permission_issues)
        avg_permission_score = sum(r.permission_score for r in results) / len(results) if results else 0
        
        return {
            "files_with_permission_issues": files_with_issues,
            "average_permission_score": round(avg_permission_score, 2),
            "permission_compliance_rate": ((len(results) - files_with_issues) / len(results)) * 100 if results else 0
        }
    
    def _analyze_entropy(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Analyze entropy analysis results."""
        high_entropy_files = sum(1 for r in results if r.entropy_suspicious)
        avg_entropy = sum(r.entropy_score for r in results) / len(results) if results else 0
        
        return {
            "high_entropy_files": high_entropy_files,
            "average_entropy": round(avg_entropy, 2),
            "entropy_threshold_exceeded": (high_entropy_files / len(results)) * 100 if results else 0
        }
    
    def _analyze_yara_results(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Analyze YARA scanning results."""
        files_with_matches = sum(1 for r in results if r.yara_matches)
        total_matches = sum(len(r.yara_matches) for r in results)
        
        return {
            "files_with_yara_matches": files_with_matches,
            "total_yara_matches": total_matches,
            "yara_detection_rate": (files_with_matches / len(results)) * 100 if results else 0
        }
    
    def _analyze_virustotal_results(self, results: List[StaticAnalysisResult]) -> Dict[str, Any]:
        """Analyze VirusTotal results."""
        files_with_detections = sum(1 for r in results if r.vt_detection_ratio != "0/0")
        
        return {
            "files_with_vt_detections": files_with_detections,
            "vt_detection_rate": (files_with_detections / len(results)) * 100 if results else 0
        }
    
    def _get_immediate_actions(self, results: List[StaticAnalysisResult]) -> List[str]:
        """Get immediate actions required."""
        actions = []
        
        malicious_count = sum(1 for r in results if r.is_malicious)
        if malicious_count > 0:
            actions.append(f"Quarantine {malicious_count} confirmed malicious files")
        
        high_risk_count = sum(1 for r in results if r.overall_threat_level == "HIGH")
        if high_risk_count > 0:
            actions.append(f"Review {high_risk_count} high-risk files")
        
        permission_issues = sum(1 for r in results if r.permission_issues)
        if permission_issues > 0:
            actions.append(f"Fix permission issues on {permission_issues} files")
        
        return actions
    
    def _save_report(self, report_data: Dict[str, Any], format_type: str) -> Path:
        """Save report in specified format."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_id = report_data["report_metadata"]["report_id"]
        
        if format_type.lower() == "json":
            filename = f"static_analysis_report_{timestamp}_{report_id}.json"
            filepath = self.output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        elif format_type.lower() == "html":
            filename = f"static_analysis_report_{timestamp}_{report_id}.html"
            filepath = self.output_dir / filename
            
            html_content = self._generate_html_report(report_data)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
        
        else:
            # Default to JSON
            filename = f"static_analysis_report_{timestamp}_{report_id}.json"
            filepath = self.output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        return filepath
    
    def _generate_html_report(self, report_data: Dict[str, Any]) -> str:
        """Generate HTML report."""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>SBARDS Static Analysis Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #2c3e50; color: white; padding: 20px; }
                .summary { background-color: #ecf0f1; padding: 15px; margin: 10px 0; }
                .critical { color: #e74c3c; font-weight: bold; }
                .high { color: #f39c12; font-weight: bold; }
                .medium { color: #f1c40f; font-weight: bold; }
                .low { color: #27ae60; font-weight: bold; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #34495e; color: white; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>SBARDS Static Analysis Report</h1>
                <p>Generated: {generation_time}</p>
                <p>Report ID: {report_id}</p>
            </div>
            
            <div class="summary">
                <h2>Executive Summary</h2>
                <p><strong>Security Posture:</strong> <span class="{posture_class}">{security_posture}</span></p>
                <p><strong>Files Analyzed:</strong> {total_files}</p>
                <p><strong>Malicious Files:</strong> {malicious_files}</p>
                <p><strong>High-Risk Files:</strong> {high_risk_files}</p>
            </div>
            
            <h2>Detailed Findings</h2>
            <p>Detailed analysis results would be displayed here...</p>
            
            <h2>Recommendations</h2>
            <p>Security recommendations would be listed here...</p>
        </body>
        </html>
        """
        
        # Extract data for template
        metadata = report_data.get("report_metadata", {})
        summary = report_data.get("executive_summary", {})
        scan_summary = report_data.get("scan_summary", {})
        
        posture = summary.get("security_posture", "UNKNOWN").lower()
        posture_class = "critical" if "critical" in posture else "high" if "high" in posture else "medium" if "medium" in posture else "low"
        
        return html_template.format(
            generation_time=metadata.get("generation_time", "Unknown"),
            report_id=metadata.get("report_id", "Unknown"),
            security_posture=summary.get("security_posture", "Unknown"),
            posture_class=posture_class,
            total_files=scan_summary.get("files_scanned", 0),
            malicious_files=scan_summary.get("malicious_files", 0),
            high_risk_files=scan_summary.get("suspicious_files", 0)
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get report generator statistics."""
        runtime = time.time() - self.stats["start_time"]
        stats = self.stats.copy()
        stats["runtime_seconds"] = runtime
        return stats

# Example usage
if __name__ == "__main__":
    # Test configuration
    config = {
        "output_dir": "reports",
        "include_charts": True,
        "include_recommendations": True,
        "include_executive_summary": True
    }
    
    # Create report generator
    generator = StaticAnalysisReportGenerator(config)
    
    # Create sample results
    sample_results = [
        StaticAnalysisResult(
            file_path="test1.exe",
            file_hash="abc123",
            file_size=1024,
            file_type=".exe",
            analysis_timestamp=time.time(),
            signature_valid=False,
            permission_score=60,
            entropy_score=7.8,
            entropy_suspicious=True,
            yara_matches=[{"rule": "malware_rule", "tags": ["trojan"]}],
            overall_threat_level="HIGH",
            overall_score=30,
            is_malicious=True
        )
    ]
    
    # Generate report
    report = generator.generate_comprehensive_report(sample_results, "json")
    print(f"Report generated: {report['report_file']}")
