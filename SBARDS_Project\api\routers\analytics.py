"""
SBARDS Analytics API Router
Advanced analytics endpoints for data analysis and reporting
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

# Import services
try:
    from api.services.analytics_service import analytics_service
    from api.services.cache_manager import cache_manager
    ANALYTICS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Analytics service not available: {e}")
    ANALYTICS_AVAILABLE = False

# Setup router
router = APIRouter()
logger = logging.getLogger("analytics_api")

# Dependency to check analytics availability
def check_analytics_available():
    if not ANALYTICS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Analytics service not available")
    return True

@router.get("/metrics/history")
async def get_metric_history(
    metric_name: str = Query(..., description="Name of the metric"),
    hours: int = Query(24, ge=1, le=168, description="Hours of history to retrieve (1-168)"),
    _: bool = Depends(check_analytics_available)
):
    """Get historical data for a specific metric"""
    try:
        # Check cache first
        cache_key = f"metric_history_{metric_name}_{hours}"
        cached_data = cache_manager.get(cache_key, namespace="analytics")
        
        if cached_data:
            return {
                "metric_name": metric_name,
                "period_hours": hours,
                "data": cached_data,
                "cached": True,
                "timestamp": datetime.now().isoformat()
            }
        
        # Get data from analytics service
        history = analytics_service.get_metric_history(metric_name, hours)
        
        # Convert to API format
        data_points = [
            {
                "timestamp": point.timestamp.isoformat(),
                "value": point.value,
                "metadata": point.metadata
            }
            for point in history
        ]
        
        # Cache for 5 minutes
        cache_manager.set(cache_key, data_points, ttl=300, namespace="analytics")
        
        return {
            "metric_name": metric_name,
            "period_hours": hours,
            "data": data_points,
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting metric history: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving metric history: {str(e)}")

@router.get("/trends/analyze")
async def analyze_trend(
    metric_name: str = Query(..., description="Name of the metric to analyze"),
    hours: int = Query(24, ge=1, le=168, description="Hours of data to analyze"),
    _: bool = Depends(check_analytics_available)
):
    """Analyze trend for a specific metric"""
    try:
        # Check cache first
        cache_key = f"trend_analysis_{metric_name}_{hours}"
        cached_analysis = cache_manager.get(cache_key, namespace="analytics")
        
        if cached_analysis:
            return {
                "metric_name": metric_name,
                "analysis": cached_analysis,
                "cached": True,
                "timestamp": datetime.now().isoformat()
            }
        
        # Perform trend analysis
        trend_analysis = analytics_service.analyze_trend(metric_name, hours)
        
        analysis_data = {
            "metric_name": trend_analysis.metric_name,
            "trend_direction": trend_analysis.trend_direction,
            "trend_strength": round(trend_analysis.trend_strength, 3),
            "prediction": round(trend_analysis.prediction, 2),
            "confidence": round(trend_analysis.confidence, 3),
            "analysis_period": trend_analysis.analysis_period
        }
        
        # Cache for 10 minutes
        cache_manager.set(cache_key, analysis_data, ttl=600, namespace="analytics")
        
        return {
            "metric_name": metric_name,
            "analysis": analysis_data,
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error analyzing trend: {e}")
        raise HTTPException(status_code=500, detail=f"Error analyzing trend: {str(e)}")

@router.get("/reports/system")
async def generate_system_report(
    _: bool = Depends(check_analytics_available)
):
    """Generate comprehensive system report"""
    try:
        # Check cache first (cache for 30 minutes)
        cache_key = "system_report_latest"
        cached_report = cache_manager.get(cache_key, namespace="analytics")
        
        if cached_report:
            return {
                "report": cached_report,
                "cached": True,
                "generated_at": datetime.now().isoformat()
            }
        
        # Generate new report
        report = analytics_service.generate_system_report()
        
        report_data = {
            "report_id": report.report_id,
            "title": report.title,
            "report_type": report.report_type,
            "generated_at": report.generated_at.isoformat(),
            "summary": report.summary,
            "data": report.data
        }
        
        # Cache for 30 minutes
        cache_manager.set(cache_key, report_data, ttl=1800, namespace="analytics")
        
        return {
            "report": report_data,
            "cached": False,
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error generating system report: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating system report: {str(e)}")

@router.get("/reports/list")
async def list_reports(
    report_type: Optional[str] = Query(None, description="Filter by report type"),
    limit: int = Query(10, ge=1, le=50, description="Number of reports to return"),
    _: bool = Depends(check_analytics_available)
):
    """List available reports"""
    try:
        reports = analytics_service.get_reports(report_type, limit)
        
        return {
            "reports": reports,
            "total": len(reports),
            "report_type_filter": report_type,
            "limit": limit,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error listing reports: {e}")
        raise HTTPException(status_code=500, detail=f"Error listing reports: {str(e)}")

@router.get("/reports/{report_id}")
async def get_report_details(
    report_id: str,
    _: bool = Depends(check_analytics_available)
):
    """Get detailed report data"""
    try:
        # Check cache first
        cache_key = f"report_details_{report_id}"
        cached_report = cache_manager.get(cache_key, namespace="analytics")
        
        if cached_report:
            return {
                "report": cached_report,
                "cached": True,
                "timestamp": datetime.now().isoformat()
            }
        
        report = analytics_service.get_report_details(report_id)
        
        if not report:
            raise HTTPException(status_code=404, detail="Report not found")
        
        # Cache for 1 hour
        cache_manager.set(cache_key, report, ttl=3600, namespace="analytics")
        
        return {
            "report": report,
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting report details: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting report details: {str(e)}")

@router.post("/metrics/record")
async def record_metric(
    metric_name: str,
    value: float,
    metadata: Optional[Dict[str, Any]] = None,
    _: bool = Depends(check_analytics_available)
):
    """Record a metric data point"""
    try:
        analytics_service.record_metric(metric_name, value, metadata)
        
        # Invalidate related cache entries
        cache_manager.invalidate_pattern(f"metric_history_{metric_name}", namespace="analytics")
        cache_manager.invalidate_pattern(f"trend_analysis_{metric_name}", namespace="analytics")
        
        return {
            "success": True,
            "metric_name": metric_name,
            "value": value,
            "recorded_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error recording metric: {e}")
        raise HTTPException(status_code=500, detail=f"Error recording metric: {str(e)}")

@router.post("/events/record")
async def record_event(
    event_type: str,
    event_data: Dict[str, Any],
    severity: str = "info",
    source: str = "api",
    _: bool = Depends(check_analytics_available)
):
    """Record an event"""
    try:
        analytics_service.record_event(event_type, event_data, severity, source)
        
        return {
            "success": True,
            "event_type": event_type,
            "severity": severity,
            "source": source,
            "recorded_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error recording event: {e}")
        raise HTTPException(status_code=500, detail=f"Error recording event: {str(e)}")

@router.get("/events/recent")
async def get_recent_events(
    hours: int = Query(24, ge=1, le=168, description="Hours of events to retrieve"),
    severity: Optional[str] = Query(None, description="Filter by severity"),
    _: bool = Depends(check_analytics_available)
):
    """Get recent events"""
    try:
        # Check cache first
        cache_key = f"recent_events_{hours}_{severity or 'all'}"
        cached_events = cache_manager.get(cache_key, namespace="analytics")
        
        if cached_events:
            return {
                "events": cached_events,
                "period_hours": hours,
                "severity_filter": severity,
                "cached": True,
                "timestamp": datetime.now().isoformat()
            }
        
        events = analytics_service.get_recent_events(hours)
        
        # Filter by severity if specified
        if severity:
            events = [e for e in events if e.get("severity") == severity]
        
        # Cache for 5 minutes
        cache_manager.set(cache_key, events, ttl=300, namespace="analytics")
        
        return {
            "events": events,
            "period_hours": hours,
            "severity_filter": severity,
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting recent events: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting recent events: {str(e)}")

@router.get("/cache/stats")
async def get_cache_stats():
    """Get analytics cache statistics"""
    try:
        stats = cache_manager.get_stats()
        
        return {
            "cache_stats": stats,
            "namespace": "analytics",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting cache stats: {str(e)}")

@router.delete("/cache/clear")
async def clear_analytics_cache():
    """Clear analytics cache"""
    try:
        success = cache_manager.clear(namespace="analytics")
        
        return {
            "success": success,
            "message": "Analytics cache cleared",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail=f"Error clearing cache: {str(e)}")

# Health check endpoint
@router.get("/health")
async def analytics_health():
    """Analytics service health check"""
    try:
        if not ANALYTICS_AVAILABLE:
            return {
                "status": "unavailable",
                "message": "Analytics service not available",
                "timestamp": datetime.now().isoformat()
            }
        
        # Test database connection
        test_events = analytics_service.get_recent_events(1)
        
        return {
            "status": "healthy",
            "message": "Analytics service operational",
            "database_accessible": True,
            "cache_accessible": True,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Analytics health check failed: {e}")
        return {
            "status": "unhealthy",
            "message": f"Analytics service error: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }
