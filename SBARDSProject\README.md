# SBARDS - Security Behavior Analysis and Ransomware Detection System

SBARDS is a comprehensive security system designed to detect and prevent ransomware and other malicious activities through multi-layered analysis.

## Features

- **Pre-scanning Phase**: Analyzes files using YARA rules and other detection methods
- **Monitoring Phase**: Real-time behavioral monitoring of processes, filesystem, and network
- **Integration Layer**: Coordinates between phases for comprehensive threat detection
- **FastAPI Backend**: Provides a robust API for interacting with the system
- **Web Dashboard**: Visualizes monitoring data and scan results

## Architecture

SBARDS follows a modular architecture with the following components:

- **Core**: Shared utilities for configuration, logging, and common functions
- **Phases**: Separate modules for pre-scanning, monitoring, and integration
- **API**: FastAPI application for interacting with the system
- **Dashboard**: Web-based dashboard for visualization

# SBARDS Project

**Security-Based Automated Ransomware Detection System**

SBARDS is a comprehensive security scanning project that integrates Python and C++ components to detect potential security threats, with a focus on ransomware detection.

## Project Overview

The SBARDS project consists of the following components:

1. **Scanner Core**: Contains the core scanning functionality
   - C++ components for high-performance scanning
   - Python wrappers and orchestration

2. **Rules**: Contains YARA rules for detecting various types of threats
   - Malware rules
   - Ransomware rules
   - Permission rules
   - Custom rules

3. **Backend API**: Provides a centralized service for:
   - Collecting scan reports
   - Integration with VirusTotal for file hash verification
   - Centralized logging and reporting
   - REST API for UIs, dashboards, and further integration

4. **Dashboard**: A web-based interface for viewing scan results and statistics

## Installation

### Prerequisites

- Python 3.9 or higher
- YARA 4.0 or higher
- Docker (optional, for containerized deployment)

### Standard Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/sbards.git
   cd sbards
   ```

2. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

3. Configure the system:

   ```bash
   # Edit config.json to match your environment
   ```

### Docker Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/sbards.git
   cd sbards
   ```

2. Build and run with Docker Compose:

   ```bash
   docker-compose up -d
- C++ compiler (for building the scanner core)
- pip (Python package manager)
- Docker and Docker Compose (optional, for containerized deployment)

### Installation Steps

1. Clone the repository:
   ```
   git clone https://github.com/SBARDSOrganization/SBARDSProject.git
   cd SBARDSProject
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   cd backend
   pip install -r requirements.txt
   cd ..
   ```

3. Build the C++ components (if needed):
   ```
   cd scanner_core/cpp
   ./build.sh  # or build.bat on Windows
   cd ../..
   ```

## Usage

### Running the System

#### All Components

```bash
python run.py
```

#### Pre-scanning Only

```bash
python run.py --prescanning --target /path/to/scan
```

#### Monitoring Only

```bash
python run.py --monitoring
```

#### API Server Only

```bash
python run.py --api --host 0.0.0.0 --port 8000
```

### API Endpoints

- **GET /api/prescanning/scans**: Get all scans
- **POST /api/prescanning/scan**: Start a new scan
- **GET /api/prescanning/scan/{scan_id}**: Get scan status
- **GET /api/monitoring/status**: Get monitoring status
- **POST /api/monitoring/start**: Start monitoring
- **POST /api/monitoring/stop**: Stop monitoring
- **GET /api/scan/progress/{scan_id}**: Get scan progress
- **WebSocket /api/scan/ws/{scan_id}**: Real-time scan progress updates
### Running the Scanner

```
python run_scanner.py scan [path] [options]
```

Options:
- `--output=PATH`: Specify output directory for scan results
- `--format=FORMAT`: Specify output format (html, json, text)
- `--rules=PATH`: Specify custom rules directory
- `--verbose`: Enable verbose output
- `--quiet`: Suppress all output except errors
- `--no-report`: Don't generate an HTML report
- `--open-report`: Open the HTML report after scanning
- `--send-to-backend`: Send scan results to the backend API
- `--backend-url=URL`: URL of the backend API (default: http://localhost:8000)

### Managing Reports

```
python run_scanner.py report [options]
```

Options:
- `--list`: List all available reports
- `--view=REPORT`: View a specific report
- `--latest`: View the latest report
- `--delete=REPORT`: Delete a specific report
- `--send-to-backend`: Send report to the backend API
- `--backend-url=URL`: URL of the backend API (default: http://localhost:8000)

### Interacting with the Backend

```
python run_scanner.py backend [options]
```

Options:
- `--url=URL`: URL of the backend API (default: http://localhost:8000)
- `--stats`: Get statistics from the backend
- `--list-reports`: List reports from the backend
- `--check-file=PATH`: Check a file against VirusTotal via the backend

### Running the Backend

```
cd backend
python run_backend.py [options]
```

Options:
- `--host=HOST`: Host to bind the server to (default: 0.0.0.0)
- `--port=PORT`: Port to bind the server to (default: 8000)
- `--reload`: Enable auto-reload for development
- `--log-level=LEVEL`: Logging level (default: info)

### Running with Docker

```
cd backend
docker-compose up -d
```

### All-in-One Runner

```
python run_sbards.py start [options]
```

Options:
- `--backend`: Start the backend API server
- `--scan`: Run a scan
- `--path=PATH`: Path to scan (default: current directory)
- `--dashboard`: Open the dashboard in a browser
- `--all`: Start all components

## Architecture

The SBARDS project follows a layered architecture:

1. **Scanner Core Layer**: Handles the low-level scanning functionality
   - C++ components for performance-critical operations
   - Python wrappers for integration with the rest of the system

2. **Orchestration Layer**: Coordinates the scanning process
   - Manages file traversal and scanning
   - Applies rules and detection logic
   - Generates reports

3. **Backend Layer**: Provides centralized services
   - Collects and stores scan reports
   - Integrates with external services like VirusTotal
   - Provides a REST API for integration

4. **Presentation Layer**: Provides user interfaces
   - Command-line interface for direct interaction
   - Web dashboard for visualization and management

## License

This project is licensed under the MIT License - see the LICENSE file for details.
