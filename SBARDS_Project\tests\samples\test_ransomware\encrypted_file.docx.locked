ENCRYPTED FILE SIMULATION

This file simulates an encrypted document with a .locked extension.
It would normally be a binary file with encrypted content, but for testing purposes,
this is just a text file with a .locked extension.

The file contains strings that would trigger ransomware detection:
- AES encryption
- RSA encryption
- CryptEncrypt
- EVP_EncryptInit
- Your files have been encrypted
- Bitcoin payment required

This is a test file only and contains no actual malicious code or encryption.
