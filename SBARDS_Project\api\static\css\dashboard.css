/* SBARDS Advanced Dashboard CSS */
/* Modern, Professional, and Interactive Design */

:root {
    /* Color Palette */
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    
    /* Background Colors */
    --bg-primary: #f8f9fa;
    --bg-secondary: #ffffff;
    --bg-dark: #2c3e50;
    --bg-card: #ffffff;
    
    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-light: #ffffff;
    --text-muted: #95a5a6;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
    --shadow-xl: 0 12px 24px rgba(0,0,0,0.25);
    
    /* Borders */
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-color: #dee2e6;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header Styles */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-light);
    padding: 2rem 0;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.header-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: slideInDown 0.8s ease;
}

.header-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
    animation: slideInUp 0.8s ease;
}

.header-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
    animation: fadeIn 1s ease 0.3s both;
}

.header-stat {
    text-align: center;
    padding: 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: var(--transition-normal);
}

.header-stat:hover {
    transform: translateY(-5px);
    background: rgba(255,255,255,0.2);
}

.header-stat-value {
    font-size: 2rem;
    font-weight: 700;
    display: block;
}

.header-stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

/* Main Container */
.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    animation: fadeInUp 0.8s ease;
}

/* Grid Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-grid-wide {
    grid-column: 1 / -1;
}

/* Card Styles */
.dashboard-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.dashboard-card:hover::before {
    transform: scaleX(1);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--bg-primary);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-icon {
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.card-badge {
    background: var(--success-color);
    color: var(--text-light);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.card-content {
    position: relative;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.status-active {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.status-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.status-error {
    background: rgba(231, 76, 60, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.status-info {
    background: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

/* Metric Cards */
.metric-card {
    text-align: center;
    padding: 2rem 1rem;
}

.metric-value {
    font-size: 3rem;
    font-weight: 700;
    color: var(--secondary-color);
    display: block;
    margin-bottom: 0.5rem;
    animation: countUp 1s ease;
}

.metric-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-change {
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--accent-color);
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.chart-canvas {
    width: 100% !important;
    height: 100% !important;
}

/* Progress Bars */
.progress-container {
    margin: 1rem 0;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-primary);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
    border-radius: 4px;
    transition: width 1s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .header-title {
        font-size: 2rem;
    }
    
    .header-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .metric-value {
        font-size: 2rem;
    }
}
