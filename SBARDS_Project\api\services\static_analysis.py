"""
Static analysis service for SBARDS API

This module provides static analysis capabilities for files.
"""

import os
import time
import math
from typing import Dict, Any, List
from datetime import datetime

from core.logger import get_global_logger
from core.utils import FileUtils

# Configure logging
logger = get_global_logger().get_layer_logger("api.services.static_analysis")


class StaticAnalysisService:
    """Service for static file analysis."""
    
    def __init__(self):
        """Initialize the static analysis service."""
        self.logger = logger
        self.file_utils = FileUtils()
    
    async def analyze_file(self, file_path: str, analysis_types: List[str]) -> Dict[str, Any]:
        """
        Perform comprehensive static analysis on a file.
        
        Args:
            file_path (str): Path to the file to analyze.
            analysis_types (List[str]): Types of analysis to perform.
            
        Returns:
            Dict[str, Any]: Analysis results.
        """
        start_time = time.time()
        
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Initialize results
            results = {
                "file_path": file_path,
                "file_hash": "",
                "file_size": os.path.getsize(file_path),
                "file_type": "unknown",
                "signature_info": {},
                "entropy_analysis": {},
                "hash_results": {},
                "yara_matches": [],
                "threat_assessment": {"level": "safe", "score": 0.0, "reasons": []},
                "processing_time": 0.0
            }
            
            # Perform requested analyses
            if "hash" in analysis_types:
                results.update(await self._analyze_hashes(file_path))
            
            if "signature" in analysis_types:
                results.update(await self._analyze_signature(file_path))
            
            if "entropy" in analysis_types:
                results.update(await self._analyze_entropy(file_path))
            
            if "yara" in analysis_types:
                results.update(await self._analyze_yara(file_path))
            
            # Perform threat assessment
            results["threat_assessment"] = self._assess_threat(results)
            
            # Calculate processing time
            results["processing_time"] = time.time() - start_time
            
            self.logger.info(f"Static analysis completed for {file_path} in {results['processing_time']:.3f}s")
            return results
            
        except Exception as e:
            self.logger.error(f"Error performing static analysis on {file_path}: {e}")
            raise
    
    async def _analyze_hashes(self, file_path: str) -> Dict[str, Any]:
        """Analyze file hashes."""
        try:
            hash_results = {}
            
            # Calculate multiple hash types
            hash_results["sha256"] = self.file_utils.get_file_hash(file_path, "sha256") or ""
            hash_results["sha1"] = self.file_utils.get_file_hash(file_path, "sha1") or ""
            hash_results["md5"] = self.file_utils.get_file_hash(file_path, "md5") or ""
            
            return {
                "file_hash": hash_results["sha256"],
                "hash_results": hash_results
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing hashes: {e}")
            return {"hash_results": {"error": str(e)}}
    
    async def _analyze_signature(self, file_path: str) -> Dict[str, Any]:
        """Analyze file signature and type."""
        try:
            signature_info = {}
            
            # Get file extension
            file_ext = os.path.splitext(file_path)[1].lower()
            signature_info["file_extension"] = file_ext
            
            # Basic file type detection
            file_type = "unknown"
            is_suspicious = False
            
            if file_ext in ['.exe', '.dll', '.sys', '.scr', '.com', '.bat', '.cmd']:
                file_type = "executable"
                is_suspicious = True  # Executables are potentially suspicious
            elif file_ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']:
                file_type = "document"
            elif file_ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
                file_type = "archive"
            elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                file_type = "image"
            elif file_ext in ['.mp3', '.wav', '.mp4', '.avi', '.mkv']:
                file_type = "media"
            elif file_ext in ['.txt', '.log', '.cfg', '.ini']:
                file_type = "text"
            
            # Read file header for magic number detection
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(16)
                    if header:
                        magic_signatures = {
                            b'\x4D\x5A': 'PE Executable',
                            b'\x7F\x45\x4C\x46': 'ELF Executable',
                            b'\x50\x4B\x03\x04': 'ZIP Archive',
                            b'\x25\x50\x44\x46': 'PDF Document',
                            b'\xFF\xD8\xFF': 'JPEG Image'
                        }
                        
                        for magic, description in magic_signatures.items():
                            if header.startswith(magic):
                                signature_info["magic_signature"] = description
                                break
            except Exception as e:
                self.logger.warning(f"Error reading file header: {e}")
            
            signature_info.update({
                "detected_type": file_type,
                "is_suspicious": is_suspicious,
                "analysis_timestamp": datetime.now().isoformat()
            })
            
            return {
                "file_type": file_type,
                "signature_info": signature_info
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing signature: {e}")
            return {"signature_info": {"error": str(e)}}
    
    async def _analyze_entropy(self, file_path: str) -> Dict[str, Any]:
        """Analyze file entropy."""
        try:
            entropy_analysis = {}
            
            with open(file_path, 'rb') as f:
                # Read file in chunks to handle large files
                chunk_size = 8192
                byte_counts = [0] * 256
                total_bytes = 0
                
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    
                    for byte in chunk:
                        byte_counts[byte] += 1
                        total_bytes += 1
            
            if total_bytes == 0:
                return {"entropy_analysis": {"error": "Empty file"}}
            
            # Calculate Shannon entropy
            entropy = 0.0
            for count in byte_counts:
                if count > 0:
                    p = count / total_bytes
                    entropy -= p * math.log2(p)
            
            # Classify entropy
            if entropy < 1.0:
                classification = "Very Low Entropy"
                suspicion_level = "low"
            elif entropy < 3.0:
                classification = "Low Entropy"
                suspicion_level = "low"
            elif entropy < 6.0:
                classification = "Medium Entropy"
                suspicion_level = "medium"
            elif entropy < 7.5:
                classification = "High Entropy"
                suspicion_level = "high"
            else:
                classification = "Very High Entropy"
                suspicion_level = "very_high"
            
            entropy_analysis = {
                "overall_entropy": round(entropy, 4),
                "classification": classification,
                "suspicion_level": suspicion_level,
                "is_suspicious": entropy > 7.0,  # High entropy might indicate encryption/packing
                "total_bytes_analyzed": total_bytes,
                "analysis_method": "Shannon entropy"
            }
            
            return {"entropy_analysis": entropy_analysis}
            
        except Exception as e:
            self.logger.error(f"Error analyzing entropy: {e}")
            return {"entropy_analysis": {"error": str(e)}}
    
    async def _analyze_yara(self, file_path: str) -> Dict[str, Any]:
        """Analyze file with YARA rules."""
        try:
            # TODO: Integrate with actual YARA engine when available
            # For now, return placeholder results
            yara_matches = []
            
            # Simulate some basic pattern matching
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in ['.exe', '.dll']:
                yara_matches.append({
                    "rule": "suspicious_executable",
                    "description": "Potentially suspicious executable file",
                    "severity": "medium",
                    "tags": ["executable", "windows"]
                })
            
            return {"yara_matches": yara_matches}
            
        except Exception as e:
            self.logger.error(f"Error analyzing with YARA: {e}")
            return {"yara_matches": []}
    
    def _assess_threat(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall threat level based on analysis results."""
        try:
            threat_score = 0.0
            reasons = []
            
            # Check entropy
            entropy_analysis = analysis_results.get("entropy_analysis", {})
            if entropy_analysis.get("is_suspicious", False):
                threat_score += 0.3
                reasons.append("High entropy detected (possible encryption/packing)")
            
            # Check file type
            signature_info = analysis_results.get("signature_info", {})
            if signature_info.get("is_suspicious", False):
                threat_score += 0.2
                reasons.append("Potentially suspicious file type")
            
            # Check YARA matches
            yara_matches = analysis_results.get("yara_matches", [])
            if yara_matches:
                threat_score += 0.4
                reasons.append(f"YARA rules matched: {len(yara_matches)}")
            
            # Determine threat level
            if threat_score >= 0.8:
                threat_level = "critical"
            elif threat_score >= 0.6:
                threat_level = "high"
            elif threat_score >= 0.4:
                threat_level = "medium"
            elif threat_score >= 0.2:
                threat_level = "low"
            else:
                threat_level = "safe"
            
            return {
                "level": threat_level,
                "score": round(threat_score, 2),
                "reasons": reasons,
                "recommendation": self._get_recommendation(threat_level)
            }
            
        except Exception as e:
            self.logger.error(f"Error assessing threat: {e}")
            return {
                "level": "unknown",
                "score": 0.0,
                "reasons": [f"Error in threat assessment: {str(e)}"],
                "recommendation": "Manual review required"
            }
    
    def _get_recommendation(self, threat_level: str) -> str:
        """Get recommendation based on threat level."""
        recommendations = {
            "safe": "File appears safe for normal use",
            "low": "Exercise caution, monitor file behavior",
            "medium": "Recommend additional analysis before use",
            "high": "High risk - avoid execution, quarantine recommended",
            "critical": "Critical threat - immediate quarantine required"
        }
        return recommendations.get(threat_level, "Unknown threat level")
