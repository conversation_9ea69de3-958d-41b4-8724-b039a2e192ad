#!/usr/bin/env python3
"""
Encoding Fix Script for SBARDS
إصلاح مشاكل الترميز في PowerShell

هذا الملف يصلح مشاكل الترميز والرموز التعبيرية في PowerShell
"""

import os
import sys
import locale

def fix_console_encoding():
    """إصلاح ترميز وحدة التحكم"""
    try:
        # Set console encoding for Windows
        if sys.platform == "win32":
            # Try to set UTF-8 encoding
            try:
                os.system("chcp 65001 >nul 2>&1")
                print("Console encoding set to UTF-8")
            except:
                print("Could not set console encoding")
        
        # Set locale
        try:
            locale.setlocale(locale.LC_ALL, '')
            print(f"Locale set to: {locale.getlocale()}")
        except:
            print("Could not set locale")
            
        # Set environment variables
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        
        print("Environment variables set for UTF-8")
        
    except Exception as e:
        print(f"Error fixing encoding: {e}")

def test_encoding():
    """اختبار الترميز"""
    print("Testing encoding...")
    print("ASCII: Hello World")
    print("UTF-8: مرحبا بالعالم")
    print("Symbols: ✅ ❌ 🔧 📊")
    print("Encoding test completed")

if __name__ == "__main__":
    print("SBARDS Encoding Fix")
    print("=" * 30)
    
    fix_console_encoding()
    print()
    test_encoding()
    print()
    print("Encoding fix completed")
