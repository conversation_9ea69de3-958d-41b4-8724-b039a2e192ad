"""
Integration Tests for SBARDS Capture Layer

This module provides comprehensive integration tests for the capture layer,
testing the complete workflow from file upload to processing.

Features:
- FastAPI endpoint testing
- C++/Python integration testing
- Redis queue integration testing
- Real-time monitoring testing
- Performance and security testing
"""

import os
import sys
import time
import json
import asyncio
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List

import pytest
import httpx
from fastapi.testclient import TestClient

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.logger import get_global_logger
from core.config import ConfigLoader
from capture.python.file_interceptor import FileInterceptor, FastAPIFileHandler
from capture.python.redis_queue import RedisQueueManager
from api.main import app

# Test configuration
logger = get_global_logger().get_layer_logger("test_capture_integration")

class TestCaptureLayerIntegration:
    """Integration tests for the capture layer."""
    
    @classmethod
    def setup_class(cls):
        """Setup test environment."""
        cls.client = TestClient(app)
        cls.test_dir = tempfile.mkdtemp(prefix="sbards_test_")
        cls.config_loader = ConfigLoader()
        cls.config = cls.config_loader.get_config()
        
        # Create test files
        cls.test_files = cls._create_test_files()
        
        logger.info(f"Test environment setup completed: {cls.test_dir}")
    
    @classmethod
    def teardown_class(cls):
        """Cleanup test environment."""
        try:
            shutil.rmtree(cls.test_dir)
            logger.info("Test environment cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    @classmethod
    def _create_test_files(cls) -> Dict[str, str]:
        """Create test files for upload testing."""
        test_files = {}
        
        # Clean text file
        clean_file = os.path.join(cls.test_dir, "clean_test.txt")
        with open(clean_file, "w") as f:
            f.write("This is a clean test file for SBARDS testing.")
        test_files["clean"] = clean_file
        
        # Suspicious file (contains suspicious keywords)
        suspicious_file = os.path.join(cls.test_dir, "suspicious_test.txt")
        with open(suspicious_file, "w") as f:
            f.write("encrypt decrypt ransom bitcoin payment")
        test_files["suspicious"] = suspicious_file
        
        # Binary file
        binary_file = os.path.join(cls.test_dir, "test_binary.bin")
        with open(binary_file, "wb") as f:
            f.write(b"\x00\x01\x02\x03\x04\x05" * 100)
        test_files["binary"] = binary_file
        
        # Large file
        large_file = os.path.join(cls.test_dir, "large_test.txt")
        with open(large_file, "w") as f:
            f.write("Large file content " * 10000)
        test_files["large"] = large_file
        
        return test_files
    
    def test_api_health_check(self):
        """Test API health check endpoint."""
        response = self.client.get("/api/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert data["version"] == "2.0.0"
        
        logger.info("API health check test passed")
    
    def test_capture_status_endpoint(self):
        """Test capture layer status endpoint."""
        response = self.client.get("/api/capture/status")
        assert response.status_code == 200
        
        data = response.json()
        assert "running" in data
        assert "interceptor_stats" in data
        assert "queue_stats" in data
        assert "upload_stats" in data
        
        logger.info("Capture status endpoint test passed")
    
    def test_upload_clean_file(self):
        """Test uploading a clean file."""
        with open(self.test_files["clean"], "rb") as f:
            files = {"file": ("clean_test.txt", f, "text/plain")}
            data = {"priority": "normal"}
            
            response = self.client.post("/api/capture/upload", files=files, data=data)
        
        assert response.status_code == 200
        
        result = response.json()
        assert result["status"] == "success"
        assert result["original_filename"] == "clean_test.txt"
        assert result["is_suspicious"] == False
        assert "file_id" in result
        assert "timestamp" in result
        
        logger.info(f"Clean file upload test passed: {result['file_id']}")
    
    def test_upload_suspicious_file(self):
        """Test uploading a suspicious file."""
        with open(self.test_files["suspicious"], "rb") as f:
            files = {"file": ("suspicious_test.txt", f, "text/plain")}
            data = {"priority": "high"}
            
            response = self.client.post("/api/capture/upload", files=files, data=data)
        
        assert response.status_code == 200
        
        result = response.json()
        assert result["status"] == "success"
        assert result["original_filename"] == "suspicious_test.txt"
        assert result["is_suspicious"] == True
        assert result["priority"] > 1  # High priority
        assert "file_id" in result
        
        logger.info(f"Suspicious file upload test passed: {result['file_id']}")
    
    def test_upload_binary_file(self):
        """Test uploading a binary file."""
        with open(self.test_files["binary"], "rb") as f:
            files = {"file": ("test_binary.bin", f, "application/octet-stream")}
            data = {"priority": "normal"}
            
            response = self.client.post("/api/capture/upload", files=files, data=data)
        
        assert response.status_code == 200
        
        result = response.json()
        assert result["status"] == "success"
        assert result["original_filename"] == "test_binary.bin"
        assert "file_id" in result
        
        logger.info(f"Binary file upload test passed: {result['file_id']}")
    
    def test_upload_with_metadata(self):
        """Test uploading a file with metadata."""
        metadata = {
            "source": "test_suite",
            "user": "test_user",
            "tags": ["test", "integration"]
        }
        
        with open(self.test_files["clean"], "rb") as f:
            files = {"file": ("clean_with_metadata.txt", f, "text/plain")}
            data = {
                "priority": "normal",
                "metadata": json.dumps(metadata)
            }
            
            response = self.client.post("/api/capture/upload", files=files, data=data)
        
        assert response.status_code == 200
        
        result = response.json()
        assert result["status"] == "success"
        assert "file_id" in result
        
        logger.info(f"File upload with metadata test passed: {result['file_id']}")
    
    def test_upload_empty_file(self):
        """Test uploading an empty file (should fail)."""
        files = {"file": ("empty.txt", b"", "text/plain")}
        data = {"priority": "normal"}
        
        response = self.client.post("/api/capture/upload", files=files, data=data)
        assert response.status_code == 400
        
        logger.info("Empty file upload rejection test passed")
    
    def test_upload_no_filename(self):
        """Test uploading without filename (should fail)."""
        files = {"file": ("", b"test content", "text/plain")}
        data = {"priority": "normal"}
        
        response = self.client.post("/api/capture/upload", files=files, data=data)
        assert response.status_code == 400
        
        logger.info("No filename upload rejection test passed")
    
    def test_upload_statistics(self):
        """Test upload statistics endpoint."""
        # Upload a few files first
        for i in range(3):
            with open(self.test_files["clean"], "rb") as f:
                files = {"file": (f"test_{i}.txt", f, "text/plain")}
                data = {"priority": "normal"}
                self.client.post("/api/capture/upload", files=files, data=data)
        
        # Get statistics
        response = self.client.get("/api/capture/statistics")
        assert response.status_code == 200
        
        stats = response.json()
        assert "total_uploads" in stats
        assert "successful_uploads" in stats
        assert "failed_uploads" in stats
        assert "total_bytes" in stats
        assert stats["total_uploads"] >= 3
        
        logger.info(f"Upload statistics test passed: {stats}")
    
    def test_file_interceptor_integration(self):
        """Test file interceptor integration."""
        # Create a temporary interceptor
        interceptor_config = {
            "target_directories": [self.test_dir],
            "recursive": False,
            "max_depth": 1,
            "exclude_dirs": [],
            "exclude_extensions": [],
            "max_file_size_mb": 10,
            "threads": 2,
            "enable_real_time": True
        }
        
        interceptor = FileInterceptor(interceptor_config)
        
        # Test interceptor initialization
        assert interceptor is not None
        
        # Test starting and stopping
        assert interceptor.start()
        time.sleep(0.5)  # Allow time for initialization
        assert interceptor.running
        
        interceptor.stop()
        assert not interceptor.running
        
        logger.info("File interceptor integration test passed")
    
    def test_redis_queue_integration(self):
        """Test Redis queue integration (if available)."""
        try:
            redis_config = {
                "redis_host": "localhost",
                "redis_port": 6379,
                "redis_db": 0,
                "queue_prefix": "test_sbards",
                "max_retries": 3
            }
            
            redis_queue = RedisQueueManager(redis_config)
            
            if redis_queue.is_available():
                # Test queue operations
                test_data = {"test": "data", "timestamp": time.time()}
                
                # Send test message
                success = redis_queue.send_message("test_queue", test_data)
                assert success
                
                # Test statistics
                stats = redis_queue.get_statistics()
                assert "messages_sent" in stats
                
                redis_queue.stop()
                logger.info("Redis queue integration test passed")
            else:
                logger.warning("Redis not available - skipping Redis integration test")
                
        except Exception as e:
            logger.warning(f"Redis integration test skipped: {e}")
    
    def test_performance_upload(self):
        """Test upload performance."""
        start_time = time.time()
        
        # Upload multiple files
        upload_count = 5
        for i in range(upload_count):
            with open(self.test_files["clean"], "rb") as f:
                files = {"file": (f"perf_test_{i}.txt", f, "text/plain")}
                data = {"priority": "normal"}
                
                response = self.client.post("/api/capture/upload", files=files, data=data)
                assert response.status_code == 200
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / upload_count
        
        # Performance assertion (should be less than 1 second per file)
        assert avg_time < 1.0
        
        logger.info(f"Performance test passed: {upload_count} uploads in {total_time:.3f}s "
                   f"(avg: {avg_time:.3f}s per file)")
    
    def test_concurrent_uploads(self):
        """Test concurrent file uploads."""
        import threading
        
        results = []
        errors = []
        
        def upload_file(file_index):
            try:
                with open(self.test_files["clean"], "rb") as f:
                    files = {"file": (f"concurrent_{file_index}.txt", f, "text/plain")}
                    data = {"priority": "normal"}
                    
                    response = self.client.post("/api/capture/upload", files=files, data=data)
                    results.append(response.status_code)
            except Exception as e:
                errors.append(str(e))
        
        # Create and start threads
        threads = []
        thread_count = 5
        
        for i in range(thread_count):
            thread = threading.Thread(target=upload_file, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify results
        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == thread_count
        assert all(status == 200 for status in results)
        
        logger.info(f"Concurrent upload test passed: {thread_count} concurrent uploads")

# Pytest fixtures and configuration
@pytest.fixture(scope="session")
def test_client():
    """Create test client for the session."""
    return TestClient(app)

@pytest.fixture(scope="session")
def test_config():
    """Load test configuration."""
    config_loader = ConfigLoader()
    return config_loader.get_config()

# Test runner
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
