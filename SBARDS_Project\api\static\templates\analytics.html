<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBARDS - Analytics</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">
    
    <!-- External Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Styles -->
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <link href="/static/css/themes.css" rel="stylesheet">
    <link href="/static/css/responsive.css" rel="stylesheet">
    
    <!-- Custom Scripts -->
    <script src="/static/js/navigation.js"></script>
    <script src="/static/js/interactive-components.js"></script>
</head>
<body>
    <!-- Navigation will be injected here -->
    
    <!-- Analytics Dashboard -->
    <div class="dashboard-container">
        <!-- Header -->
        <div class="dashboard-header">
            <div class="header-content">
                <h1 class="header-title">
                    <i class="fas fa-chart-line"></i>
                    Analytics Dashboard
                </h1>
                <p class="header-subtitle">Data analysis and system insights</p>
            </div>
            
            <div class="header-stats">
                <div class="header-stat">
                    <div class="header-stat-value" id="totalMetrics">0</div>
                    <div class="header-stat-label">Total Metrics</div>
                </div>
                <div class="header-stat">
                    <div class="header-stat-value" id="activeReports">0</div>
                    <div class="header-stat-label">Active Reports</div>
                </div>
                <div class="header-stat">
                    <div class="header-stat-value" id="dataPoints">0</div>
                    <div class="header-stat-label">Data Points</div>
                </div>
            </div>
        </div>
        
        <!-- Analytics Grid -->
        <div class="dashboard-grid">
            <!-- Metrics Overview -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        System Metrics
                    </h3>
                    <div class="card-actions">
                        <button class="btn btn-sm" onclick="refreshMetrics()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <canvas id="metricsChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Trend Analysis -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-trending-up"></i>
                        Trend Analysis
                    </h3>
                    <select id="trendMetricSelect" class="form-select">
                        <option value="cpu_usage">CPU Usage</option>
                        <option value="memory_usage">Memory Usage</option>
                        <option value="files_processed">Files Processed</option>
                        <option value="threats_detected">Threats Detected</option>
                    </select>
                </div>
                <div class="card-content">
                    <div id="trendAnalysisContent">
                        <div class="trend-item">
                            <div class="trend-label">Direction</div>
                            <div class="trend-value" id="trendDirection">-</div>
                        </div>
                        <div class="trend-item">
                            <div class="trend-label">Strength</div>
                            <div class="trend-value" id="trendStrength">-</div>
                        </div>
                        <div class="trend-item">
                            <div class="trend-label">Prediction</div>
                            <div class="trend-value" id="trendPrediction">-</div>
                        </div>
                        <div class="trend-item">
                            <div class="trend-label">Confidence</div>
                            <div class="trend-value" id="trendConfidence">-</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Events -->
            <div class="dashboard-card dashboard-grid-wide">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i>
                        Recent Events
                    </h3>
                    <div class="card-actions">
                        <select id="eventSeverityFilter" class="form-select">
                            <option value="">All Severities</option>
                            <option value="info">Info</option>
                            <option value="warning">Warning</option>
                            <option value="error">Error</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                </div>
                <div class="card-content">
                    <div class="table-container">
                        <table class="data-table" id="eventsTable">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Type</th>
                                    <th>Severity</th>
                                    <th>Source</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody id="eventsTableBody">
                                <tr>
                                    <td colspan="5" class="text-center">Loading events...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- System Reports -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-alt"></i>
                        System Reports
                    </h3>
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-plus"></i>
                        Generate Report
                    </button>
                </div>
                <div class="card-content">
                    <div id="reportsContainer">
                        <div class="loading-state">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading reports...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cache Statistics -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database"></i>
                        Cache Performance
                    </h3>
                </div>
                <div class="card-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">Hit Rate</div>
                            <div class="stat-value" id="cacheHitRate">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Total Requests</div>
                            <div class="stat-value" id="cacheTotalRequests">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Memory Cache</div>
                            <div class="stat-value" id="cacheMemorySize">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">LRU Cache</div>
                            <div class="stat-value" id="cacheLRUSize">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Analytics JavaScript -->
    <script>
        class AnalyticsDashboard {
            constructor() {
                this.charts = {};
                this.updateInterval = null;
                this.init();
            }
            
            async init() {
                console.log('📊 Analytics Dashboard initializing...');
                await this.loadInitialData();
                this.setupCharts();
                this.setupEventListeners();
                this.startAutoUpdate();
            }
            
            async loadInitialData() {
                try {
                    // Load metrics history
                    await this.loadMetricsHistory();
                    
                    // Load trend analysis
                    await this.loadTrendAnalysis('cpu_usage');
                    
                    // Load recent events
                    await this.loadRecentEvents();
                    
                    // Load reports
                    await this.loadReports();
                    
                    // Load cache stats
                    await this.loadCacheStats();
                    
                } catch (error) {
                    console.error('Error loading initial data:', error);
                }
            }
            
            async loadMetricsHistory() {
                try {
                    const response = await fetch('/api/analytics/metrics/history?metric_name=cpu_usage&hours=24');
                    const data = await response.json();
                    
                    document.getElementById('totalMetrics').textContent = data.data.length;
                    
                    return data;
                } catch (error) {
                    console.error('Error loading metrics:', error);
                    return null;
                }
            }
            
            async loadTrendAnalysis(metric) {
                try {
                    const response = await fetch(`/api/analytics/trends/analyze?metric_name=${metric}&hours=24`);
                    const data = await response.json();
                    
                    if (data.analysis) {
                        document.getElementById('trendDirection').textContent = data.analysis.trend_direction;
                        document.getElementById('trendStrength').textContent = (data.analysis.trend_strength * 100).toFixed(1) + '%';
                        document.getElementById('trendPrediction').textContent = data.analysis.prediction.toFixed(2);
                        document.getElementById('trendConfidence').textContent = (data.analysis.confidence * 100).toFixed(1) + '%';
                    }
                    
                } catch (error) {
                    console.error('Error loading trend analysis:', error);
                }
            }
            
            async loadRecentEvents() {
                try {
                    const response = await fetch('/api/analytics/events/recent?hours=24');
                    const data = await response.json();
                    
                    const tbody = document.getElementById('eventsTableBody');
                    
                    if (data.events && data.events.length > 0) {
                        tbody.innerHTML = data.events.map(event => `
                            <tr>
                                <td>${new Date(event.timestamp).toLocaleString()}</td>
                                <td>${event.event_type}</td>
                                <td><span class="badge badge-${event.severity}">${event.severity}</span></td>
                                <td>${event.source}</td>
                                <td>${JSON.stringify(event.event_data).substring(0, 50)}...</td>
                            </tr>
                        `).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="5" class="text-center">No events found</td></tr>';
                    }
                    
                } catch (error) {
                    console.error('Error loading events:', error);
                    document.getElementById('eventsTableBody').innerHTML = 
                        '<tr><td colspan="5" class="text-center text-error">Error loading events</td></tr>';
                }
            }
            
            async loadReports() {
                try {
                    const response = await fetch('/api/analytics/reports/list?limit=5');
                    const data = await response.json();
                    
                    const container = document.getElementById('reportsContainer');
                    
                    if (data.reports && data.reports.length > 0) {
                        container.innerHTML = data.reports.map(report => `
                            <div class="report-item">
                                <div class="report-title">${report.title}</div>
                                <div class="report-meta">
                                    <span class="report-type">${report.report_type}</span>
                                    <span class="report-date">${new Date(report.generated_at).toLocaleDateString()}</span>
                                </div>
                                <div class="report-summary">${report.summary}</div>
                            </div>
                        `).join('');
                        
                        document.getElementById('activeReports').textContent = data.reports.length;
                    } else {
                        container.innerHTML = '<div class="empty-state">No reports available</div>';
                    }
                    
                } catch (error) {
                    console.error('Error loading reports:', error);
                    document.getElementById('reportsContainer').innerHTML = 
                        '<div class="error-state">Error loading reports</div>';
                }
            }
            
            async loadCacheStats() {
                try {
                    const response = await fetch('/api/analytics/cache/stats');
                    const data = await response.json();
                    
                    if (data.cache_stats) {
                        const stats = data.cache_stats;
                        document.getElementById('cacheHitRate').textContent = stats.hit_rate + '%';
                        document.getElementById('cacheTotalRequests').textContent = stats.total_requests;
                        document.getElementById('cacheMemorySize').textContent = stats.memory_cache_size;
                        document.getElementById('cacheLRUSize').textContent = stats.lru_cache_size;
                    }
                    
                } catch (error) {
                    console.error('Error loading cache stats:', error);
                }
            }
            
            setupCharts() {
                // Setup metrics chart
                const ctx = document.getElementById('metricsChart').getContext('2d');
                this.charts.metrics = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'CPU Usage',
                            data: [],
                            borderColor: 'rgb(75, 192, 192)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            }
            
            setupEventListeners() {
                // Trend metric selector
                document.getElementById('trendMetricSelect').addEventListener('change', (e) => {
                    this.loadTrendAnalysis(e.target.value);
                });
                
                // Event severity filter
                document.getElementById('eventSeverityFilter').addEventListener('change', (e) => {
                    this.filterEvents(e.target.value);
                });
            }
            
            async filterEvents(severity) {
                try {
                    const url = severity ? 
                        `/api/analytics/events/recent?hours=24&severity=${severity}` :
                        '/api/analytics/events/recent?hours=24';
                    
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    const tbody = document.getElementById('eventsTableBody');
                    
                    if (data.events && data.events.length > 0) {
                        tbody.innerHTML = data.events.map(event => `
                            <tr>
                                <td>${new Date(event.timestamp).toLocaleString()}</td>
                                <td>${event.event_type}</td>
                                <td><span class="badge badge-${event.severity}">${event.severity}</span></td>
                                <td>${event.source}</td>
                                <td>${JSON.stringify(event.event_data).substring(0, 50)}...</td>
                            </tr>
                        `).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="5" class="text-center">No events found</td></tr>';
                    }
                    
                } catch (error) {
                    console.error('Error filtering events:', error);
                }
            }
            
            startAutoUpdate() {
                this.updateInterval = setInterval(() => {
                    this.loadRecentEvents();
                    this.loadCacheStats();
                }, 30000); // Update every 30 seconds
            }
        }
        
        // Global functions
        async function refreshMetrics() {
            if (window.analyticsDashboard) {
                await window.analyticsDashboard.loadMetricsHistory();
            }
        }
        
        async function generateReport() {
            try {
                const response = await fetch('/api/analytics/reports/system');
                const data = await response.json();
                
                if (data.report) {
                    alert('Report generated successfully!');
                    if (window.analyticsDashboard) {
                        await window.analyticsDashboard.loadReports();
                    }
                }
            } catch (error) {
                console.error('Error generating report:', error);
                alert('Error generating report');
            }
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.analyticsDashboard = new AnalyticsDashboard();
        });
    </script>
    
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--theme-bg-secondary);
            border-radius: 8px;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: var(--theme-text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--theme-accent-primary);
        }
        
        .trend-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--theme-border-color);
        }
        
        .trend-item:last-child {
            border-bottom: none;
        }
        
        .report-item {
            padding: 1rem;
            border: 1px solid var(--theme-border-color);
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .report-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .report-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: var(--theme-text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .report-summary {
            font-size: 0.9rem;
            color: var(--theme-text-secondary);
        }
        
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .badge-info { background: var(--theme-info-alpha); color: var(--theme-info); }
        .badge-warning { background: var(--theme-warning-alpha); color: var(--theme-warning); }
        .badge-error { background: var(--theme-error-alpha); color: var(--theme-error); }
        .badge-critical { background: var(--theme-error-alpha); color: var(--theme-error); }
        
        .loading-state, .empty-state, .error-state {
            text-align: center;
            padding: 2rem;
            color: var(--theme-text-secondary);
        }
        
        .form-select {
            padding: 0.5rem;
            border: 1px solid var(--theme-border-color);
            border-radius: 6px;
            background: var(--theme-bg-card);
            color: var(--theme-text-primary);
        }
    </style>
</body>
</html>
