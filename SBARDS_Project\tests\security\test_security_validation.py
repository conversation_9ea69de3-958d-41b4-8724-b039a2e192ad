#!/usr/bin/env python3
"""
Security Validation Tests - SBARDS Project

This module provides comprehensive security tests to ensure the new SBARDS
structure maintains high security standards and has no vulnerabilities.

Security Test Categories:
- Input validation and sanitization
- Path traversal prevention
- File access security
- Configuration security
- API security (if available)
- Memory safety
"""

import os
import sys
import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from core.config import ConfigManager
    from core.utils import FileUtils
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False

class TestInputValidation(unittest.TestCase):
    """Test input validation and sanitization."""
    
    def setUp(self):
        """Set up input validation test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
        self.test_file = Path(self.temp_dir) / "test_file.txt"
        
        with open(self.test_file, 'w') as f:
            f.write("Test content for security validation")
    
    def tearDown(self):
        """Clean up input validation test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_file_path_validation(self):
        """Test file path validation and sanitization."""
        # Test valid file paths
        valid_paths = [
            str(self.test_file),
            str(self.test_file.absolute()),
            str(self.test_file.resolve())
        ]
        
        for valid_path in valid_paths:
            # Should work with valid paths
            result = FileUtils.file_exists(valid_path)
            self.assertTrue(result)
            
            hash_result = FileUtils.get_file_hash(valid_path, "sha256")
            self.assertIsNotNone(hash_result)
    
    def test_malicious_file_paths(self):
        """Test handling of malicious file paths."""
        # Test path traversal attempts
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/shadow",
            "C:\\Windows\\System32\\config\\SAM",
            "file:///etc/passwd",
            "\\\\server\\share\\file.txt",
            "con.txt",  # Windows reserved name
            "aux.txt",  # Windows reserved name
            "nul.txt",  # Windows reserved name
            "",  # Empty path
            " ",  # Whitespace only
            "\x00",  # Null byte
            "very_long_path_" + "x" * 1000,  # Very long path
        ]
        
        for malicious_path in malicious_paths:
            # Should handle malicious paths gracefully
            result = FileUtils.file_exists(malicious_path)
            self.assertFalse(result)  # Should return False, not crash
            
            hash_result = FileUtils.get_file_hash(malicious_path, "sha256")
            self.assertIsNone(hash_result)  # Should return None, not crash
    
    def test_invalid_hash_algorithms(self):
        """Test handling of invalid hash algorithms."""
        invalid_algorithms = [
            "invalid_algorithm",
            "",
            " ",
            "sha256; rm -rf /",  # Command injection attempt
            "md5\x00",  # Null byte
            "SHA256",  # Wrong case (should be handled gracefully)
            123,  # Wrong type
            None,  # None value
            ["sha256"],  # Wrong type (list)
            {"algorithm": "sha256"}  # Wrong type (dict)
        ]
        
        for invalid_algo in invalid_algorithms:
            try:
                hash_result = FileUtils.get_file_hash(str(self.test_file), invalid_algo)
                # Should return None for invalid algorithms
                self.assertIsNone(hash_result)
            except (TypeError, ValueError):
                # Acceptable to raise these exceptions for invalid types
                pass
            except Exception as e:
                # Should not raise unexpected exceptions
                self.fail(f"Unexpected exception for algorithm {invalid_algo}: {e}")

class TestPathTraversalPrevention(unittest.TestCase):
    """Test path traversal attack prevention."""
    
    def setUp(self):
        """Set up path traversal test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a nested directory structure
        self.safe_dir = Path(self.temp_dir) / "safe"
        self.safe_dir.mkdir()
        
        self.safe_file = self.safe_dir / "safe_file.txt"
        with open(self.safe_file, 'w') as f:
            f.write("Safe content")
        
        # Create a file outside the safe directory
        self.unsafe_file = Path(self.temp_dir) / "unsafe_file.txt"
        with open(self.unsafe_file, 'w') as f:
            f.write("Unsafe content")
    
    def tearDown(self):
        """Clean up path traversal test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_directory_traversal_attempts(self):
        """Test various directory traversal attempts."""
        # Change to safe directory
        original_cwd = os.getcwd()
        try:
            os.chdir(self.safe_dir)
            
            # Attempt to access files outside the safe directory
            traversal_attempts = [
                "../unsafe_file.txt",
                "..\\unsafe_file.txt",
                "./../unsafe_file.txt",
                "..\\..\\unsafe_file.txt",
                "../" * 10 + "etc/passwd",
                "..\\" * 10 + "windows\\system32\\config\\sam"
            ]
            
            for attempt in traversal_attempts:
                # File operations should handle these safely
                exists_result = FileUtils.file_exists(attempt)
                # The result depends on the actual file system, but should not crash
                self.assertIsInstance(exists_result, bool)
                
                hash_result = FileUtils.get_file_hash(attempt, "sha256")
                # Should either return a hash or None, but not crash
                self.assertTrue(hash_result is None or isinstance(hash_result, str))
        
        finally:
            os.chdir(original_cwd)

class TestFileAccessSecurity(unittest.TestCase):
    """Test file access security measures."""
    
    def setUp(self):
        """Set up file access security test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up file access security test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_large_file_handling(self):
        """Test handling of extremely large files."""
        # Test with a simulated large file (don't actually create it)
        fake_large_file = "/dev/zero"  # Unix systems
        if os.name == 'nt':
            fake_large_file = "nul"  # Windows
        
        # Should handle large files gracefully without consuming excessive memory
        start_time = time.time()
        hash_result = FileUtils.get_file_hash(fake_large_file, "sha256")
        end_time = time.time()
        
        # Should either complete quickly or return None (if file doesn't exist)
        duration = end_time - start_time
        self.assertLess(duration, 5.0)  # Should not hang
    
    def test_special_files_handling(self):
        """Test handling of special files and devices."""
        special_files = []
        
        if os.name != 'nt':  # Unix-like systems
            special_files.extend([
                "/dev/null",
                "/dev/zero",
                "/dev/random",
                "/proc/self/mem",
                "/sys/kernel/debug"
            ])
        else:  # Windows
            special_files.extend([
                "nul",
                "con",
                "aux",
                "prn",
                "com1",
                "lpt1"
            ])
        
        for special_file in special_files:
            # Should handle special files safely
            try:
                exists_result = FileUtils.file_exists(special_file)
                self.assertIsInstance(exists_result, bool)
                
                # Hash calculation should either work or fail gracefully
                hash_result = FileUtils.get_file_hash(special_file, "sha256")
                self.assertTrue(hash_result is None or isinstance(hash_result, str))
                
            except (PermissionError, OSError):
                # These exceptions are acceptable for special files
                pass
    
    def test_concurrent_file_access(self):
        """Test concurrent file access security."""
        import threading
        import time
        
        # Create a test file
        test_file = Path(self.temp_dir) / "concurrent_test.txt"
        with open(test_file, 'w') as f:
            f.write("Concurrent access test content")
        
        results = []
        errors = []
        
        def access_file():
            """Access file concurrently."""
            try:
                hash_result = FileUtils.get_file_hash(str(test_file), "sha256")
                results.append(hash_result)
            except Exception as e:
                errors.append(e)
        
        # Start multiple threads accessing the same file
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=access_file)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        self.assertEqual(len(errors), 0, f"Concurrent access errors: {errors}")
        self.assertEqual(len(results), 10)
        
        # All results should be the same (same file, same hash)
        unique_results = set(results)
        self.assertEqual(len(unique_results), 1, "Inconsistent hash results")

class TestConfigurationSecurity(unittest.TestCase):
    """Test configuration security measures."""
    
    def setUp(self):
        """Set up configuration security test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up configuration security test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_malicious_configuration_content(self):
        """Test handling of malicious configuration content."""
        malicious_configs = [
            # JSON injection attempts
            '{"key": "value", "malicious": "$(rm -rf /)"}',
            '{"key": "value\\"; rm -rf /; echo \\""}',
            '{"key": "value", "script": "<script>alert(1)</script>"}',
            
            # Extremely large configuration
            '{"key": "' + 'x' * 1000000 + '"}',
            
            # Deeply nested configuration
            '{"a": {"b": {"c": {"d": {"e": {"f": "value"}}}}}}',
            
            # Invalid JSON
            '{"key": "value"',  # Missing closing brace
            'not json at all',
            '',  # Empty file
            '\x00\x01\x02',  # Binary content
        ]
        
        for i, malicious_content in enumerate(malicious_configs):
            config_file = Path(self.temp_dir) / f"malicious_config_{i}.json"
            
            try:
                with open(config_file, 'w', encoding='utf-8', errors='ignore') as f:
                    f.write(malicious_content)
            except:
                # Skip if we can't even write the malicious content
                continue
            
            # Test configuration loading
            config_manager = ConfigManager(str(config_file))
            
            # Should handle malicious content gracefully
            try:
                load_result = config_manager.load_config()
                # Should either succeed or fail gracefully
                self.assertIsInstance(load_result, bool)
                
                if load_result:
                    # If loading succeeded, accessing values should be safe
                    value = config_manager.get("key", "default")
                    self.assertIsInstance(value, (str, int, float, bool, type(None)))
                    
            except (json.JSONDecodeError, UnicodeDecodeError, MemoryError):
                # These exceptions are acceptable for malicious content
                pass
            except Exception as e:
                # Should not raise unexpected exceptions
                self.fail(f"Unexpected exception for malicious config {i}: {e}")
    
    def test_configuration_file_permissions(self):
        """Test configuration file permission handling."""
        config_file = Path(self.temp_dir) / "permission_test.json"
        
        # Create a valid configuration
        config_data = {"test": "value"}
        with open(config_file, 'w') as f:
            json.dump(config_data, f)
        
        # Test with different file permissions (Unix only)
        if os.name != 'nt':
            # Make file unreadable
            os.chmod(config_file, 0o000)
            
            config_manager = ConfigManager(str(config_file))
            load_result = config_manager.load_config()
            
            # Should handle permission errors gracefully
            self.assertFalse(load_result)
            
            # Restore permissions for cleanup
            os.chmod(config_file, 0o644)

class TestMemorySafety(unittest.TestCase):
    """Test memory safety measures."""
    
    def setUp(self):
        """Set up memory safety test environment."""
        if not CORE_AVAILABLE:
            self.skipTest("Core components not available")
        
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up memory safety test environment."""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_memory_exhaustion_prevention(self):
        """Test prevention of memory exhaustion attacks."""
        import psutil
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Create a moderately large file
        large_file = Path(self.temp_dir) / "memory_test.txt"
        with open(large_file, 'w') as f:
            f.write("test content\n" * 100000)  # ~1.3MB file
        
        # Process the file multiple times
        for _ in range(10):
            hash_result = FileUtils.get_file_hash(str(large_file), "sha256")
            self.assertIsNotNone(hash_result)
        
        # Check memory usage after processing
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB)
        memory_increase_mb = memory_increase / 1024 / 1024
        self.assertLess(memory_increase_mb, 100, 
                       f"Excessive memory usage: {memory_increase_mb:.1f} MB")
    
    def test_string_handling_safety(self):
        """Test safe string handling."""
        # Test with various string inputs
        test_strings = [
            "",  # Empty string
            " ",  # Whitespace
            "\x00",  # Null byte
            "\n\r\t",  # Control characters
            "normal string",  # Normal string
            "unicode: 你好世界 🌍",  # Unicode
            "very long string: " + "x" * 10000,  # Long string
        ]
        
        for test_string in test_strings:
            # Create a file with the test string
            test_file = Path(self.temp_dir) / "string_test.txt"
            
            try:
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write(test_string)
                
                # Process the file
                hash_result = FileUtils.get_file_hash(str(test_file), "sha256")
                
                # Should handle all string types safely
                self.assertTrue(hash_result is None or isinstance(hash_result, str))
                
                if hash_result:
                    self.assertEqual(len(hash_result), 64)  # SHA-256 length
                
            except UnicodeEncodeError:
                # Acceptable for some unicode strings
                pass

def run_security_tests():
    """Run all security tests."""
    test_classes = [
        TestInputValidation,
        TestPathTraversalPrevention,
        TestFileAccessSecurity,
        TestConfigurationSecurity,
        TestMemorySafety
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    print("🔒 Running Security Validation Tests")
    print("=" * 50)
    
    success = run_security_tests()
    
    if success:
        print("\n✅ All security tests passed!")
        print("🛡️ System security is validated")
    else:
        print("\n❌ Some security tests failed!")
        print("🔧 Please address security issues before deployment")
    
    exit(0 if success else 1)
