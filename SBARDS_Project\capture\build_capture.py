#!/usr/bin/env python3
"""
Build Script for SBARDS Capture Layer C++ Components

This script automates the compilation and testing of C++ components
for the SBARDS capture layer, providing cross-platform build support.

Usage:
    python build_capture.py [options]

Options:
    --clean         Clean build directory before building
    --debug         Build in debug mode
    --test          Run tests after building
    --install       Install libraries after building
    --benchmark     Run performance benchmarks
"""

import os
import sys
import subprocess
import shutil
import argparse
import platform
from pathlib import Path

class CaptureLayerBuilder:
    """Builder for SBARDS Capture Layer C++ components."""
    
    def __init__(self):
        """Initialize the builder."""
        self.script_dir = Path(__file__).parent
        self.cpp_dir = self.script_dir / "cpp"
        self.build_dir = self.cpp_dir / "build"
        self.install_dir = self.script_dir / "lib"
        
        # Platform detection
        self.is_windows = platform.system() == "Windows"
        self.is_linux = platform.system() == "Linux"
        self.is_macos = platform.system() == "Darwin"
        
        # Build configuration
        self.build_type = "Release"
        self.generator = self._detect_generator()
        
    def _detect_generator(self):
        """Detect appropriate CMake generator."""
        if self.is_windows:
            # Try to find Visual Studio
            if shutil.which("cl"):
                return "Visual Studio 16 2019"
            elif shutil.which("gcc"):
                return "MinGW Makefiles"
            else:
                return "NMake Makefiles"
        else:
            # Unix-like systems
            if shutil.which("ninja"):
                return "Ninja"
            else:
                return "Unix Makefiles"
    
    def check_dependencies(self):
        """Check if required build tools are available."""
        print("🔍 Checking build dependencies...")
        
        # Check CMake
        if not shutil.which("cmake"):
            print("❌ CMake not found. Please install CMake 3.16 or later.")
            return False
        
        # Check compiler
        if self.is_windows:
            if not (shutil.which("cl") or shutil.which("gcc") or shutil.which("clang")):
                print("❌ No C++ compiler found. Please install Visual Studio or MinGW.")
                return False
        else:
            if not (shutil.which("gcc") or shutil.which("clang")):
                print("❌ No C++ compiler found. Please install GCC or Clang.")
                return False
        
        print("✅ All dependencies found")
        return True
    
    def clean_build(self):
        """Clean the build directory."""
        print("🧹 Cleaning build directory...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"✅ Cleaned {self.build_dir}")
        
        if self.install_dir.exists():
            shutil.rmtree(self.install_dir)
            print(f"✅ Cleaned {self.install_dir}")
    
    def configure(self):
        """Configure the build with CMake."""
        print("⚙️ Configuring build...")
        
        # Create build directory
        self.build_dir.mkdir(parents=True, exist_ok=True)
        
        # CMake configure command
        cmd = [
            "cmake",
            "-S", str(self.cpp_dir),
            "-B", str(self.build_dir),
            f"-DCMAKE_BUILD_TYPE={self.build_type}",
            f"-DCMAKE_INSTALL_PREFIX={self.install_dir}"
        ]
        
        if self.generator:
            cmd.extend(["-G", self.generator])
        
        # Run CMake configure
        result = subprocess.run(cmd, cwd=self.cpp_dir)
        
        if result.returncode != 0:
            print("❌ CMake configuration failed")
            return False
        
        print("✅ Configuration successful")
        return True
    
    def build(self):
        """Build the C++ components."""
        print("🔨 Building C++ components...")
        
        # CMake build command
        cmd = [
            "cmake",
            "--build", str(self.build_dir),
            "--config", self.build_type,
            "--parallel"
        ]
        
        # Run build
        result = subprocess.run(cmd, cwd=self.cpp_dir)
        
        if result.returncode != 0:
            print("❌ Build failed")
            return False
        
        print("✅ Build successful")
        return True
    
    def test(self):
        """Run tests."""
        print("🧪 Running tests...")
        
        # CTest command
        cmd = [
            "ctest",
            "--build-config", self.build_type,
            "--verbose"
        ]
        
        # Run tests
        result = subprocess.run(cmd, cwd=self.build_dir)
        
        if result.returncode != 0:
            print("❌ Tests failed")
            return False
        
        print("✅ All tests passed")
        return True
    
    def install(self):
        """Install the built libraries."""
        print("📦 Installing libraries...")
        
        # CMake install command
        cmd = [
            "cmake",
            "--install", str(self.build_dir),
            "--config", self.build_type
        ]
        
        # Run install
        result = subprocess.run(cmd, cwd=self.cpp_dir)
        
        if result.returncode != 0:
            print("❌ Installation failed")
            return False
        
        print(f"✅ Libraries installed to {self.install_dir}")
        return True
    
    def benchmark(self):
        """Run performance benchmarks."""
        print("📊 Running performance benchmarks...")
        
        # Find benchmark executables
        if self.is_windows:
            file_monitor_exe = self.build_dir / self.build_type / "file_monitor_test.exe"
            permission_exe = self.build_dir / self.build_type / "permission_manager_test.exe"
        else:
            file_monitor_exe = self.build_dir / "file_monitor_test"
            permission_exe = self.build_dir / "permission_manager_test"
        
        # Run benchmarks
        if file_monitor_exe.exists():
            print("🏃 Running file monitor benchmark...")
            subprocess.run([str(file_monitor_exe)], cwd=self.cpp_dir)
        
        if permission_exe.exists():
            print("🔐 Running permission manager benchmark...")
            subprocess.run([str(permission_exe)], cwd=self.cpp_dir)
        
        print("✅ Benchmarks completed")
    
    def create_python_bindings(self):
        """Create Python bindings for the C++ libraries."""
        print("🐍 Creating Python bindings...")
        
        binding_code = '''"""
Python bindings for SBARDS Capture Layer C++ components.
"""

import ctypes
import os
from pathlib import Path

# Find the shared library
lib_dir = Path(__file__).parent / "lib"
if os.name == "nt":
    lib_name = "sbards_capture.dll"
else:
    lib_name = "libsbards_capture.so"

lib_path = lib_dir / lib_name

if lib_path.exists():
    # Load the library
    _lib = ctypes.CDLL(str(lib_path))
    
    # File Monitor functions
    _lib.create_file_monitor.restype = ctypes.c_void_p
    _lib.destroy_file_monitor.argtypes = [ctypes.c_void_p]
    _lib.start_monitoring.argtypes = [ctypes.c_void_p]
    _lib.start_monitoring.restype = ctypes.c_bool
    _lib.stop_monitoring.argtypes = [ctypes.c_void_p]
    
    # Permission Manager functions
    _lib.create_permission_manager.restype = ctypes.c_void_p
    _lib.destroy_permission_manager.argtypes = [ctypes.c_void_p]
    _lib.analyze_file_permissions.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
    _lib.analyze_file_permissions.restype = ctypes.c_char_p
    
    class FileMonitor:
        """Python wrapper for C++ FileMonitor."""
        
        def __init__(self):
            self._handle = _lib.create_file_monitor()
        
        def __del__(self):
            if hasattr(self, '_handle'):
                _lib.destroy_file_monitor(self._handle)
        
        def start(self):
            return _lib.start_monitoring(self._handle)
        
        def stop(self):
            _lib.stop_monitoring(self._handle)
    
    class PermissionManager:
        """Python wrapper for C++ PermissionManager."""
        
        def __init__(self):
            self._handle = _lib.create_permission_manager()
        
        def __del__(self):
            if hasattr(self, '_handle'):
                _lib.destroy_permission_manager(self._handle)
        
        def analyze_file(self, file_path):
            result = _lib.analyze_file_permissions(self._handle, file_path.encode())
            return result.decode() if result else None

else:
    print(f"Warning: C++ library not found at {lib_path}")
    
    # Provide mock implementations
    class FileMonitor:
        def start(self): return False
        def stop(self): pass
    
    class PermissionManager:
        def analyze_file(self, file_path): return None
'''
        
        # Write bindings file
        bindings_file = self.script_dir / "cpp_bindings.py"
        with open(bindings_file, 'w') as f:
            f.write(binding_code)
        
        print(f"✅ Python bindings created: {bindings_file}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Build SBARDS Capture Layer C++ Components")
    parser.add_argument("--clean", action="store_true", help="Clean build directory")
    parser.add_argument("--debug", action="store_true", help="Build in debug mode")
    parser.add_argument("--test", action="store_true", help="Run tests after building")
    parser.add_argument("--install", action="store_true", help="Install libraries")
    parser.add_argument("--benchmark", action="store_true", help="Run benchmarks")
    parser.add_argument("--bindings", action="store_true", help="Create Python bindings")
    
    args = parser.parse_args()
    
    # Create builder
    builder = CaptureLayerBuilder()
    
    # Set build type
    if args.debug:
        builder.build_type = "Debug"
    
    print("🚀 SBARDS Capture Layer Builder")
    print("=" * 40)
    print(f"Platform: {platform.system()} {platform.machine()}")
    print(f"Build type: {builder.build_type}")
    print(f"Generator: {builder.generator}")
    print()
    
    # Check dependencies
    if not builder.check_dependencies():
        sys.exit(1)
    
    # Clean if requested
    if args.clean:
        builder.clean_build()
    
    # Configure
    if not builder.configure():
        sys.exit(1)
    
    # Build
    if not builder.build():
        sys.exit(1)
    
    # Test if requested
    if args.test:
        if not builder.test():
            sys.exit(1)
    
    # Install if requested
    if args.install:
        if not builder.install():
            sys.exit(1)
    
    # Benchmark if requested
    if args.benchmark:
        builder.benchmark()
    
    # Create Python bindings if requested
    if args.bindings:
        builder.create_python_bindings()
    
    print()
    print("🎉 Build completed successfully!")
    print()
    print("Next steps:")
    print("  1. Run tests: python build_capture.py --test")
    print("  2. Install libraries: python build_capture.py --install")
    print("  3. Create Python bindings: python build_capture.py --bindings")
    print("  4. Run benchmarks: python build_capture.py --benchmark")

if __name__ == "__main__":
    main()
