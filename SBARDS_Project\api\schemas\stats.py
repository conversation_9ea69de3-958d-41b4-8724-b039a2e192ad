"""
Statistics schemas for SBARDS API

This module contains Pydantic schemas for statistics and metrics.
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class ThreatTypeStats(BaseModel):
    """Schema for threat type statistics."""
    
    type: str = Field(..., description="Type of threat")
    count: int = Field(..., description="Number of occurrences")


class ScanTypeStats(BaseModel):
    """Schema for scan type statistics."""
    
    type: str = Field(..., description="Type of scan")
    count: int = Field(..., description="Number of scans")


class SystemMetrics(BaseModel):
    """Schema for system metrics."""
    
    cpu_usage: Optional[float] = Field(None, description="CPU usage percentage")
    memory_usage: Optional[float] = Field(None, description="Memory usage percentage")
    disk_usage: Optional[float] = Field(None, description="Disk usage percentage")
    active_scans: Optional[int] = Field(None, description="Number of active scans")


class PerformanceMetrics(BaseModel):
    """Schema for performance metrics."""
    
    average_scan_time: Optional[float] = Field(None, description="Average scan time in seconds")
    files_per_second: Optional[float] = Field(None, description="Files processed per second")
    threat_detection_rate: Optional[float] = Field(None, description="Threat detection rate percentage")


class StatsResponse(BaseModel):
    """Schema for statistics responses."""
    
    total_scans: int = Field(..., description="Total number of scans performed")
    total_files_scanned: int = Field(..., description="Total number of files scanned")
    total_threats: int = Field(..., description="Total number of threats found")
    recent_scans: int = Field(..., description="Number of scans in the last 24 hours")
    recent_threats: int = Field(..., description="Number of threats found in the last 24 hours")
    top_threat_types: List[ThreatTypeStats] = Field(..., description="Top threat types")
    scan_type_distribution: List[ScanTypeStats] = Field(..., description="Distribution of scan types")
    system_status: str = Field(..., description="Overall system status")
    api_version: str = Field(..., description="API version")
    system_metrics: Optional[SystemMetrics] = Field(None, description="System performance metrics")
    performance_metrics: Optional[PerformanceMetrics] = Field(None, description="Performance metrics")


class HealthCheckResponse(BaseModel):
    """Schema for health check responses."""
    
    status: str = Field(..., description="Health status")
    timestamp: str = Field(..., description="Timestamp of health check")
    version: str = Field(..., description="API version")
    database_status: str = Field(..., description="Database connection status")
    services_status: dict = Field(..., description="Status of various services")
    uptime: Optional[float] = Field(None, description="System uptime in seconds")
