#!/usr/bin/env python3
"""
اختبار شامل لنظام المراقبة المتطور - SBARDS
Comprehensive Monitoring System Test - SBARDS

يختبر هذا الملف:
- مراقبة المتصفحات
- مراقبة تطبيقات التواصل الاجتماعي
- مراقبة التخزين السحابي
- مراقبة البريد الإلكتروني
- مراقبة الأجهزة الخارجية (USB)
- اعتراض الملفات من جميع المصادر
"""

import os
import sys
import time
import yaml
import tempfile
import shutil
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from capture.python.file_interceptor import FileInterceptor
from core.logger import get_global_logger

def load_config():
    """تحميل ملف التكوين"""
    config_path = "config.yaml"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    else:
        # Default configuration
        return {
            "capture": {
                "recursive": True,
                "max_depth": 3,
                "max_file_size_mb": 50,
                "threads": 2,
                "monitor_browsers": True,
                "monitor_social_media": True,
                "monitor_cloud_storage": True,
                "monitor_email": True,
                "monitor_usb": True,
                "target_directories": ["samples"],
                "exclude_dirs": [".git", "__pycache__"],
                "exclude_extensions": [".log", ".cache"]
            }
        }

def create_test_environment():
    """إنشاء بيئة اختبار مؤقتة"""
    print("🔧 إنشاء بيئة اختبار...")
    
    # Create temporary test directories
    test_base = tempfile.mkdtemp(prefix="sbards_test_")
    
    # Simulate browser download directories
    browser_dirs = [
        "Chrome/Downloads",
        "Firefox/Downloads", 
        "Edge/Downloads",
        "Opera/Downloads"
    ]
    
    # Simulate social media directories
    social_dirs = [
        "WhatsApp/Downloads",
        "Telegram/Downloads",
        "Discord/Downloads",
        "Skype/Downloads"
    ]
    
    # Simulate cloud storage directories
    cloud_dirs = [
        "OneDrive/Documents",
        "GoogleDrive/Downloads",
        "Dropbox/Shared",
        "iCloud/Downloads"
    ]
    
    # Simulate email directories
    email_dirs = [
        "Outlook/Attachments",
        "Thunderbird/Downloads"
    ]
    
    # Simulate USB directories
    usb_dirs = [
        "USB_Drive_E/Files",
        "USB_Drive_F/Documents"
    ]
    
    all_dirs = browser_dirs + social_dirs + cloud_dirs + email_dirs + usb_dirs
    
    created_dirs = []
    for dir_path in all_dirs:
        full_path = os.path.join(test_base, dir_path)
        os.makedirs(full_path, exist_ok=True)
        created_dirs.append(full_path)
    
    print(f"✅ تم إنشاء {len(created_dirs)} مجلد اختبار في: {test_base}")
    return test_base, created_dirs

def create_test_files(test_dirs):
    """إنشاء ملفات اختبار في المجلدات"""
    print("📄 إنشاء ملفات اختبار...")
    
    test_files = []
    
    # File types to test
    file_types = [
        # Normal files
        ("document.txt", "This is a normal document", False),
        ("photo.jpg", b"\xff\xd8\xff\xe0\x00\x10JFIF", False),
        ("video.mp4", b"\x00\x00\x00\x20ftypmp4", False),
        
        # Suspicious files
        ("suspicious.exe", b"MZ\x90\x00\x03\x00\x00\x00", True),
        ("ransom_note.txt", "Your files have been encrypted! Pay bitcoin to decrypt!", True),
        ("malware.bat", "@echo off\nencrypt all files", True),
        ("trojan.scr", b"MZ\x90\x00\x03\x00\x00\x00", True),
        
        # Archive files
        ("archive.zip", b"PK\x03\x04\x14\x00\x00\x00", False),
        ("suspicious.rar", b"Rar!\x1a\x07\x00encrypt", True),
        
        # Script files
        ("script.js", "function encrypt() { /* malicious code */ }", True),
        ("powershell.ps1", "Invoke-Expression 'malware'", True)
    ]
    
    for i, test_dir in enumerate(test_dirs[:8]):  # Test in first 8 directories
        for filename, content, is_suspicious in file_types:
            file_path = os.path.join(test_dir, f"{i}_{filename}")
            
            try:
                if isinstance(content, str):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                else:
                    with open(file_path, 'wb') as f:
                        f.write(content)
                
                test_files.append({
                    'path': file_path,
                    'name': filename,
                    'directory': os.path.basename(test_dir),
                    'is_suspicious': is_suspicious
                })
                
            except Exception as e:
                print(f"⚠️ خطأ في إنشاء ملف {file_path}: {e}")
    
    print(f"✅ تم إنشاء {len(test_files)} ملف اختبار")
    return test_files

def test_file_interceptor(config, test_dirs):
    """اختبار مُعترض الملفات"""
    print("\n🔍 اختبار مُعترض الملفات...")
    
    # Update config to include test directories
    capture_config = config.get("capture", {})
    capture_config["target_directories"] = test_dirs[:5]  # Test first 5 directories
    
    # Initialize file interceptor
    interceptor = FileInterceptor(capture_config)
    
    # Test statistics
    stats = {
        "total_files": 0,
        "intercepted_files": 0,
        "suspicious_files": 0,
        "browser_files": 0,
        "social_media_files": 0,
        "cloud_storage_files": 0,
        "email_files": 0,
        "usb_files": 0
    }
    
    # Scan directories
    print("📂 فحص المجلدات...")
    events = interceptor.scan_directories()
    
    for event in events:
        stats["total_files"] += 1
        
        if event:
            stats["intercepted_files"] += 1
            
            if event.is_suspicious:
                stats["suspicious_files"] += 1
            
            # Categorize by source
            dir_name = os.path.basename(os.path.dirname(event.file_path)).lower()
            if any(browser in dir_name for browser in ["chrome", "firefox", "edge", "opera"]):
                stats["browser_files"] += 1
            elif any(social in dir_name for social in ["whatsapp", "telegram", "discord", "skype"]):
                stats["social_media_files"] += 1
            elif any(cloud in dir_name for cloud in ["onedrive", "googledrive", "dropbox", "icloud"]):
                stats["cloud_storage_files"] += 1
            elif any(email in dir_name for email in ["outlook", "thunderbird"]):
                stats["email_files"] += 1
            elif "usb" in dir_name:
                stats["usb_files"] += 1
    
    return stats, events

def test_real_time_monitoring(interceptor, test_base):
    """اختبار المراقبة في الوقت الفعلي"""
    print("\n⏱️ اختبار المراقبة في الوقت الفعلي...")
    
    # Start interceptor
    if not interceptor.start():
        print("❌ فشل في تشغيل المُعترض")
        return False
    
    # Create new files during monitoring
    new_files = []
    test_dir = os.path.join(test_base, "Chrome", "Downloads")
    
    for i in range(3):
        filename = f"realtime_test_{i}.txt"
        filepath = os.path.join(test_dir, filename)
        
        with open(filepath, 'w') as f:
            f.write(f"Real-time test file {i}")
        
        new_files.append(filepath)
        time.sleep(0.5)  # Small delay between files
    
    # Wait for processing
    time.sleep(2)
    
    # Stop interceptor
    interceptor.stop()
    
    print(f"✅ تم إنشاء {len(new_files)} ملف أثناء المراقبة")
    return True

def run_comprehensive_test():
    """تشغيل الاختبار الشامل"""
    print("🛡️ SBARDS Comprehensive Monitoring Test")
    print("=" * 70)
    print("اختبار شامل لنظام المراقبة المتطور")
    print("=" * 70)
    
    try:
        # Load configuration
        config = load_config()
        print("✅ تم تحميل التكوين")
        
        # Create test environment
        test_base, test_dirs = create_test_environment()
        
        # Create test files
        test_files = create_test_files(test_dirs)
        
        # Test file interceptor
        stats, events = test_file_interceptor(config, test_dirs)
        
        # Test real-time monitoring
        interceptor = FileInterceptor(config.get("capture", {}))
        realtime_success = test_real_time_monitoring(interceptor, test_base)
        
        # Display results
        print("\n" + "=" * 70)
        print("📊 نتائج الاختبار الشامل:")
        print("=" * 70)
        
        print(f"📁 إجمالي الملفات المُنشأة: {len(test_files)}")
        print(f"🔍 إجمالي الملفات المُكتشفة: {stats['total_files']}")
        print(f"📥 الملفات المُعترضة: {stats['intercepted_files']}")
        print(f"🚨 الملفات المشبوهة: {stats['suspicious_files']}")
        
        print(f"\n📊 التصنيف حسب المصدر:")
        print(f"   🌐 ملفات المتصفحات: {stats['browser_files']}")
        print(f"   💬 ملفات التواصل الاجتماعي: {stats['social_media_files']}")
        print(f"   ☁️ ملفات التخزين السحابي: {stats['cloud_storage_files']}")
        print(f"   📧 ملفات البريد الإلكتروني: {stats['email_files']}")
        print(f"   💾 ملفات USB: {stats['usb_files']}")
        
        print(f"\n⏱️ المراقبة في الوقت الفعلي: {'✅ نجحت' if realtime_success else '❌ فشلت'}")
        
        # Calculate success rate
        if len(test_files) > 0:
            detection_rate = (stats['intercepted_files'] / len(test_files)) * 100
            print(f"\n📈 معدل الكشف: {detection_rate:.1f}%")
        
        # Test monitoring capabilities
        print(f"\n🔧 قدرات المراقبة:")
        capture_config = config.get("capture", {})
        print(f"   🌐 مراقبة المتصفحات: {'✅' if capture_config.get('monitor_browsers', False) else '❌'}")
        print(f"   💬 مراقبة التواصل الاجتماعي: {'✅' if capture_config.get('monitor_social_media', False) else '❌'}")
        print(f"   ☁️ مراقبة التخزين السحابي: {'✅' if capture_config.get('monitor_cloud_storage', False) else '❌'}")
        print(f"   📧 مراقبة البريد الإلكتروني: {'✅' if capture_config.get('monitor_email', False) else '❌'}")
        print(f"   💾 مراقبة USB: {'✅' if capture_config.get('monitor_usb', False) else '❌'}")
        
        # Overall assessment
        print("\n" + "=" * 70)
        if stats['intercepted_files'] >= len(test_files) * 0.8:
            print("🎉 الاختبار نجح! النظام يعمل بكفاءة عالية")
            print("✅ النظام قادر على اعتراض الملفات من جميع المصادر")
        elif stats['intercepted_files'] >= len(test_files) * 0.5:
            print("✅ الاختبار نجح جزئياً! النظام يعمل بشكل جيد")
            print("⚠️ يمكن تحسين معدل الكشف")
        else:
            print("⚠️ الاختبار يحتاج تحسين! معدل الكشف منخفض")
            print("🔧 يُنصح بمراجعة إعدادات المراقبة")
        
        print("=" * 70)
        
        # Cleanup
        print(f"\n🧹 تنظيف ملفات الاختبار...")
        shutil.rmtree(test_base)
        print("✅ تم تنظيف ملفات الاختبار")
        
        return stats
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    run_comprehensive_test()
