"""
Utility Functions for SBARDS

This module provides utility functions for the SBARDS project.
"""

import os
import platform
import sys
import hashlib
import json
import time
from typing import Dict, List, Any, Optional, Tuple, Union

def get_platform() -> str:
    """
    Get the current platform.
    
    Returns:
        str: Platform name (Windows, Linux, Darwin, etc.)
    """
    return platform.system()

def is_windows() -> bool:
    """
    Check if the current platform is Windows.
    
    Returns:
        bool: True if Windows, False otherwise
    """
    return platform.system() == "Windows"

def is_linux() -> bool:
    """
    Check if the current platform is Linux.
    
    Returns:
        bool: True if Linux, False otherwise
    """
    return platform.system() == "Linux"

def is_macos() -> bool:
    """
    Check if the current platform is macOS.
    
    Returns:
        bool: True if macOS, False otherwise
    """
    return platform.system() == "Darwin"

def get_file_hash(file_path: str, algorithm: str = "sha256") -> Optional[str]:
    """
    Calculate the hash of a file.
    
    Args:
        file_path (str): Path to the file
        algorithm (str): Hash algorithm (md5, sha1, sha256, sha512)
        
    Returns:
        Optional[str]: File hash or None if file not found
    """
    if not os.path.isfile(file_path):
        return None
    
    try:
        if algorithm == "md5":
            hasher = hashlib.md5()
        elif algorithm == "sha1":
            hasher = hashlib.sha1()
        elif algorithm == "sha512":
            hasher = hashlib.sha512()
        else:
            hasher = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            # Read and update hash in chunks of 4K
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        
        return hasher.hexdigest()
    except Exception as e:
        print(f"Error calculating hash for {file_path}: {e}")
        return None

def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get information about a file.
    
    Args:
        file_path (str): Path to the file
        
    Returns:
        Dict[str, Any]: File information
    """
    if not os.path.exists(file_path):
        return {"error": "File not found"}
    
    try:
        stat_info = os.stat(file_path)
        file_info = {
            "path": file_path,
            "size": stat_info.st_size,
            "created": stat_info.st_ctime,
            "modified": stat_info.st_mtime,
            "accessed": stat_info.st_atime,
            "is_file": os.path.isfile(file_path),
            "is_dir": os.path.isdir(file_path),
            "extension": os.path.splitext(file_path)[1].lower() if os.path.isfile(file_path) else "",
        }
        
        # Add hash for files
        if os.path.isfile(file_path) and stat_info.st_size < 100 * 1024 * 1024:  # 100 MB limit
            file_info["sha256"] = get_file_hash(file_path, "sha256")
        
        return file_info
    except Exception as e:
        return {"error": str(e)}

def save_json(data: Any, file_path: str) -> bool:
    """
    Save data to a JSON file.
    
    Args:
        data (Any): Data to save
        file_path (str): Path to the file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4)
        return True
    except Exception as e:
        print(f"Error saving JSON to {file_path}: {e}")
        return False

def load_json(file_path: str) -> Optional[Any]:
    """
    Load data from a JSON file.
    
    Args:
        file_path (str): Path to the file
        
    Returns:
        Optional[Any]: Loaded data or None if file not found or invalid
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading JSON from {file_path}: {e}")
        return None

def format_time(seconds: float) -> str:
    """
    Format time in seconds to a human-readable string.
    
    Args:
        seconds (float): Time in seconds
        
    Returns:
        str: Formatted time string
    """
    if seconds < 60:
        return f"{seconds:.2f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.2f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.2f} hours"

def get_memory_usage() -> Dict[str, float]:
    """
    Get current memory usage.
    
    Returns:
        Dict[str, float]: Memory usage information
    """
    try:
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            "rss": memory_info.rss / (1024 * 1024),  # RSS in MB
            "vms": memory_info.vms / (1024 * 1024),  # VMS in MB
            "percent": process.memory_percent()
        }
    except ImportError:
        return {"error": "psutil not installed"}
    except Exception as e:
        return {"error": str(e)}
