"""
Constants for SBARDS New Architecture

This module contains all constants used across the SBARDS project layers.
"""

import os
from enum import Enum
from typing import Dict, List

# Project Information
PROJECT_NAME = "SBARDS"
PROJECT_VERSION = "2.0.0"
PROJECT_DESCRIPTION = "Security Behavior Analysis and Ransomware Detection System"

# File Extensions
EXECUTABLE_EXTENSIONS = ['.exe', '.dll', '.sys', '.bat', '.cmd', '.ps1', '.msi', '.scr']
DOCUMENT_EXTENSIONS = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf']
IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.svg']
VIDEO_EXTENSIONS = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm']
AUDIO_EXTENSIONS = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma']
ARCHIVE_EXTENSIONS = ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz']
SCRIPT_EXTENSIONS = ['.py', '.js', '.php', '.pl', '.rb', '.sh', '.vbs']

# Dangerous File Extensions (High Priority Scanning)
DANGEROUS_EXTENSIONS = EXECUTABLE_EXTENSIONS + ['.jar', '.app', '.deb', '.rpm']

# File Size Limits (in bytes)
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100 MB
MAX_SCAN_SIZE = 50 * 1024 * 1024   # 50 MB
MIN_FILE_SIZE = 1                   # 1 byte

# Hash Algorithms
class HashAlgorithm(Enum):
    SHA256 = "sha256"
    SHA512 = "sha512"
    MD5 = "md5"
    SHA1 = "sha1"
    SSDEEP = "ssdeep"

SUPPORTED_HASH_ALGORITHMS = [algo.value for algo in HashAlgorithm]

# YARA Rule Categories
class YaraCategory(Enum):
    CUSTOM = "custom"
    MALWARE = "malware"
    RANSOMWARE = "ransomware"
    PERMISSIONS = "permissions"
    NETWORK = "network"
    CRYPTO = "crypto"

YARA_CATEGORIES = [category.value for category in YaraCategory]

# Threat Levels
class ThreatLevel(Enum):
    SAFE = 0
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

THREAT_LEVEL_NAMES = {
    ThreatLevel.SAFE: "Safe",
    ThreatLevel.LOW: "Low Risk",
    ThreatLevel.MEDIUM: "Medium Risk",
    ThreatLevel.HIGH: "High Risk",
    ThreatLevel.CRITICAL: "Critical Threat"
}

# Entropy Thresholds
ENTROPY_THRESHOLD_LOW = 6.0
ENTROPY_THRESHOLD_MEDIUM = 7.0
ENTROPY_THRESHOLD_HIGH = 7.5
ENTROPY_THRESHOLD_CRITICAL = 8.0

# Performance Constants
DEFAULT_THREAD_COUNT = 4
MAX_THREAD_COUNT = 16
DEFAULT_BATCH_SIZE = 20
MAX_BATCH_SIZE = 100
DEFAULT_TIMEOUT = 30  # seconds
MAX_TIMEOUT = 300     # seconds

# Memory Limits
DEFAULT_MEMORY_LIMIT_MB = 1024
MAX_MEMORY_LIMIT_MB = 8192
MIN_MEMORY_LIMIT_MB = 256

# API Constants
DEFAULT_API_HOST = "127.0.0.1"
DEFAULT_API_PORT = 8000
MAX_UPLOAD_SIZE_MB = 100
API_TIMEOUT_SECONDS = 60

# Database Constants
DB_CONNECTION_TIMEOUT = 30
DB_QUERY_TIMEOUT = 10
MAX_DB_CONNECTIONS = 10

# Monitoring Constants
DEFAULT_MONITOR_INTERVAL = 1.0  # seconds
MIN_MONITOR_INTERVAL = 0.1
MAX_MONITOR_INTERVAL = 60.0
ALERT_THRESHOLD_DEFAULT = 0.7

# Layer Status
class LayerStatus(Enum):
    DISABLED = "disabled"
    ENABLED = "enabled"
    ERROR = "error"
    INITIALIZING = "initializing"

# File Processing Status
class FileStatus(Enum):
    PENDING = "pending"
    SCANNING = "scanning"
    CLEAN = "clean"
    INFECTED = "infected"
    QUARANTINED = "quarantined"
    ERROR = "error"

# Scan Results
class ScanResult(Enum):
    CLEAN = "clean"
    MALWARE = "malware"
    RANSOMWARE = "ransomware"
    SUSPICIOUS = "suspicious"
    ERROR = "error"

# Directory Paths (relative to project root)
CAPTURE_TEMP_DIR = "capture/temp_storage"
QUARANTINE_DIR = "quarantine"
LOGS_DIR = "logs"
OUTPUT_DIR = "output"
BACKUP_DIR = "data/backups"
RULES_DIR = "static_analysis/yara_rules"

# Log Levels
LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

# Platform Specific Constants
WINDOWS_SYSTEM = "Windows"
LINUX_SYSTEM = "Linux"
MACOS_SYSTEM = "Darwin"

# C++ Integration Constants
CPP_LIBRARY_EXTENSIONS = {
    WINDOWS_SYSTEM: ".dll",
    LINUX_SYSTEM: ".so",
    MACOS_SYSTEM: ".dylib"
}

CPP_EXECUTABLE_EXTENSIONS = {
    WINDOWS_SYSTEM: ".exe",
    LINUX_SYSTEM: "",
    MACOS_SYSTEM: ""
}

# Redis Configuration (if enabled)
REDIS_DEFAULT_HOST = "localhost"
REDIS_DEFAULT_PORT = 6379
REDIS_DEFAULT_DB = 0

# External Integration Constants
VIRUSTOTAL_API_URL = "https://www.virustotal.com/vtapi/v2/"
VIRUSTOTAL_RATE_LIMIT = 4  # requests per minute for free API

# Blockchain Constants (Enhanced for SBARDS Documentation)
BLOCKCHAIN_NETWORKS = ["hyperledger", "ethereum", "bitcoin"]
HYPERLEDGER_FABRIC_CONFIG = {
    "network_name": "sbards-network",
    "channel_name": "sbards-channel",
    "chaincode_name": "sbards-chaincode",
    "org_name": "SBARDSOrg",
    "peer_endpoint": "localhost:7051",
    "orderer_endpoint": "localhost:7050"
}

# Smart Contract Constants
SMART_CONTRACT_FUNCTIONS = {
    "store_hash": "storeFileHash",
    "verify_hash": "verifyFileHash",
    "get_hash_info": "getHashInfo",
    "update_threat_status": "updateThreatStatus"
}

# Security Constants (Enhanced)
ENCRYPTION_ALGORITHMS = ["AES-256", "ChaCha20", "RSA-2048", "RSA-4096"]
SECURE_DELETE_PASSES = 3
MEMORY_ENCRYPTION_KEYS = {
    "aes_256_key_size": 32,
    "chacha20_key_size": 32,
    "nonce_size": 12
}

# Cold Boot Protection Constants
COLD_BOOT_PROTECTION = {
    "key_scrambling_interval": 30,  # seconds
    "memory_wipe_patterns": [0x00, 0xFF, 0xAA, 0x55],
    "secure_memory_regions": ["heap", "stack", "data_segment"]
}

# Error Codes
class ErrorCode(Enum):
    SUCCESS = 0
    FILE_NOT_FOUND = 1
    PERMISSION_DENIED = 2
    INVALID_FILE_FORMAT = 3
    SCAN_TIMEOUT = 4
    MEMORY_ERROR = 5
    NETWORK_ERROR = 6
    CONFIG_ERROR = 7
    UNKNOWN_ERROR = 99

# Regular Expressions for Pattern Matching
SUSPICIOUS_PATTERNS = [
    r'encrypt',
    r'decrypt',
    r'ransom',
    r'bitcoin',
    r'cryptocurrency',
    r'payload',
    r'backdoor',
    r'keylogger'
]

# File Magic Numbers (for file type detection)
MAGIC_NUMBERS = {
    'PE': b'\x4D\x5A',  # Windows PE
    'ELF': b'\x7F\x45\x4C\x46',  # Linux ELF
    'PDF': b'\x25\x50\x44\x46',  # PDF
    'ZIP': b'\x50\x4B\x03\x04',  # ZIP
    'RAR': b'\x52\x61\x72\x21',  # RAR
    'JPEG': b'\xFF\xD8\xFF',  # JPEG
    'PNG': b'\x89\x50\x4E\x47',  # PNG
}

# Default Permissions (octal)
DEFAULT_FILE_PERMISSIONS = 0o644
DEFAULT_DIR_PERMISSIONS = 0o755
EXECUTABLE_PERMISSIONS = 0o755

# Quarantine Reasons
QUARANTINE_REASONS = [
    "YARA rule match",
    "High entropy detected",
    "Suspicious permissions",
    "Malware signature found",
    "Ransomware behavior detected",
    "Network communication detected",
    "Manual quarantine"
]

# Report Templates
REPORT_TEMPLATES = {
    "html": "scan_report_template.html",
    "json": "scan_results.json",
    "csv": "scan_results.csv",
    "xml": "scan_results.xml"
}

# Notification Types
class NotificationType(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

# Integration Endpoints
INTEGRATION_ENDPOINTS = {
    "virustotal": "/file/report",
    "blockchain": "/hash/store",
    "threat_intel": "/indicators/check"
}

# Cache Settings
CACHE_TTL_SECONDS = 3600  # 1 hour
MAX_CACHE_SIZE_MB = 100

# Backup Settings
BACKUP_RETENTION_DAYS = 30
MAX_BACKUP_SIZE_GB = 10

# Testing Constants
TEST_FILE_EXTENSIONS = ['.test', '.sample', '.dummy']
TEST_DIRECTORIES = ['tests/samples', 'tests/unit', 'tests/integration']

# Version Compatibility
MIN_PYTHON_VERSION = (3, 8)
MIN_CPP_STANDARD = "C++17"

# Feature Flags
FEATURE_FLAGS = {
    "dynamic_analysis": False,
    "blockchain_integration": False,
    "ml_analysis": False,
    "advanced_monitoring": True,
    "external_apis": False,
    "memory_protection": False,
    "api_hooking": False,
    "sandbox_analysis": False
}

# Dynamic Analysis Constants
SANDBOX_CONFIG = {
    "docker_enabled": True,
    "cuckoo_enabled": False,
    "timeout_seconds": 300,
    "max_memory_mb": 2048,
    "network_isolation": True,
    "api_hooking_enabled": True
}

# API Hooking Constants
API_HOOK_CATEGORIES = {
    "file_operations": ["CreateFile", "WriteFile", "ReadFile", "DeleteFile"],
    "registry_operations": ["RegOpenKey", "RegSetValue", "RegDeleteKey"],
    "network_operations": ["socket", "connect", "send", "recv"],
    "process_operations": ["CreateProcess", "TerminateProcess", "OpenProcess"],
    "memory_operations": ["VirtualAlloc", "VirtualFree", "WriteProcessMemory"]
}

# Machine Learning Constants
ML_MODEL_CONFIG = {
    "lstm_sequence_length": 100,
    "cnn_input_shape": (224, 224, 3),
    "random_forest_estimators": 100,
    "isolation_forest_contamination": 0.1,
    "feature_extraction_methods": ["static", "dynamic", "behavioral"]
}

# Threat Intelligence Constants
THREAT_INTEL_SOURCES = {
    "virustotal": "https://www.virustotal.com/vtapi/v2/",
    "alienvault_otx": "https://otx.alienvault.com/api/v1/",
    "misp": "https://misp-project.org/",
    "ibm_xforce": "https://api.xforce.ibmcloud.com/"
}

# MongoDB Configuration (for Security Logging)
MONGODB_CONFIG = {
    "host": "localhost",
    "port": 27017,
    "database": "sbards_security",
    "collections": {
        "security_events": "security_events",
        "threat_intelligence": "threat_intelligence",
        "file_analysis": "file_analysis",
        "system_logs": "system_logs"
    },
    "indexes": ["timestamp", "file_hash", "threat_level", "event_type"]
}
