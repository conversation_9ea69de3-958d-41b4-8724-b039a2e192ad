{"metadata": {"version": "2.0.0", "last_updated": "2025-05-25T00:00:00Z", "description": "Trusted sources whitelist for SBARDS v2.0", "total_sources": 100, "categories": ["software_vendors", "security_vendors", "operating_systems", "development_platforms", "cloud_services"]}, "software_vendors": {"tier_1": [{"name": "Microsoft Corporation", "domains": ["microsoft.com", "windows.com", "office.com", "azure.com"], "trust_score": 95, "verification_methods": ["digital_signature", "certificate_authority"], "certificate_thumbprints": ["3B7E0FC4CC662C6C75C4F1F8BC4F4F4F4F4F4F4F", "4C8F1FD5DD773D7D86D5F5F5F5F5F5F5F5F5F5F5"], "last_verified": "2025-05-25T00:00:00Z", "notes": "Primary OS and software vendor"}, {"name": "Google LLC", "domains": ["google.com", "chrome.com", "android.com", "gstatic.com"], "trust_score": 92, "verification_methods": ["digital_signature", "certificate_authority"], "certificate_thumbprints": ["5D9F2FE6EE884E8E97E6F6F6F6F6F6F6F6F6F6F6", "6EA03F7FFF995F9FA8F7F7F7F7F7F7F7F7F7F7F7"], "last_verified": "2025-05-25T00:00:00Z", "notes": "Browser and mobile OS vendor"}, {"name": "Apple Inc.", "domains": ["apple.com", "icloud.com", "itunes.com"], "trust_score": 94, "verification_methods": ["digital_signature", "certificate_authority"], "certificate_thumbprints": ["7FB14F80GG006G0GB9G8F8F8F8F8F8F8F8F8F8F8", "8GC25G91HH117H1HCA9G9G9G9G9G9G9G9G9G9G9G"], "last_verified": "2025-05-25T00:00:00Z", "notes": "macOS and iOS vendor"}], "tier_2": [{"name": "Mozilla Corporation", "domains": ["mozilla.org", "firefox.com", "thunderbird.net"], "trust_score": 88, "verification_methods": ["digital_signature", "open_source"], "certificate_thumbprints": ["9HD36HA2II228I2IDH0HAHAHAHAHAHAHAHAHAHA", "AIEA7IB3JJ339J3JEI1IBIBIBIBIBIBIBIBIBIBIB"], "last_verified": "2025-05-25T00:00:00Z", "notes": "Open source browser vendor"}, {"name": "Adobe Inc.", "domains": ["adobe.com", "acrobat.com", "creative.adobe.com"], "trust_score": 85, "verification_methods": ["digital_signature", "certificate_authority"], "certificate_thumbprints": ["BJFB8JC4KK44AK4KFJ2JCJCJCJCJCJCJCJCJCJCJ", "CKGC9KD5LL55BL5LGK3KDKDKDKDKDKDKDKDKDKDK"], "last_verified": "2025-05-25T00:00:00Z", "notes": "Creative software vendor"}]}, "security_vendors": [{"name": "Symantec Corporation", "domains": ["symantec.com", "norton.com", "verisign.com"], "trust_score": 90, "specialization": "antivirus", "verification_methods": ["digital_signature", "security_certification"], "last_verified": "2025-05-25T00:00:00Z"}, {"name": "McAfee LLC", "domains": ["mcafee.com"], "trust_score": 87, "specialization": "antivirus", "verification_methods": ["digital_signature", "security_certification"], "last_verified": "2025-05-25T00:00:00Z"}, {"name": "Kaspersky Lab", "domains": ["kaspersky.com", "kaspersky.ru"], "trust_score": 85, "specialization": "antivirus", "verification_methods": ["digital_signature", "security_certification"], "last_verified": "2025-05-25T00:00:00Z", "notes": "Subject to regional restrictions"}, {"name": "Malwarebytes Corporation", "domains": ["malwarebytes.com"], "trust_score": 89, "specialization": "anti-malware", "verification_methods": ["digital_signature", "security_certification"], "last_verified": "2025-05-25T00:00:00Z"}], "operating_systems": [{"name": "Microsoft Windows", "domains": ["microsoft.com", "windows.com"], "trust_score": 95, "os_family": "windows", "supported_versions": ["Windows 10", "Windows 11", "Windows Server 2019", "Windows Server 2022"], "verification_methods": ["digital_signature", "windows_update"], "last_verified": "2025-05-25T00:00:00Z"}, {"name": "Ubuntu Linux", "domains": ["ubuntu.com", "canonical.com"], "trust_score": 92, "os_family": "linux", "supported_versions": ["20.04 LTS", "22.04 LTS", "24.04 LTS"], "verification_methods": ["package_signature", "repository_verification"], "last_verified": "2025-05-25T00:00:00Z"}, {"name": "Red Hat Enterprise Linux", "domains": ["redhat.com"], "trust_score": 94, "os_family": "linux", "supported_versions": ["RHEL 8", "RHEL 9"], "verification_methods": ["package_signature", "repository_verification"], "last_verified": "2025-05-25T00:00:00Z"}], "development_platforms": [{"name": "GitHub Inc.", "domains": ["github.com", "githubusercontent.com"], "trust_score": 82, "platform_type": "code_repository", "verification_methods": ["repository_verification", "community_trust"], "notes": "Verify individual repositories and maintainers", "last_verified": "2025-05-25T00:00:00Z"}, {"name": "GitLab Inc.", "domains": ["gitlab.com"], "trust_score": 80, "platform_type": "code_repository", "verification_methods": ["repository_verification", "community_trust"], "notes": "Verify individual repositories and maintainers", "last_verified": "2025-05-25T00:00:00Z"}, {"name": "Python Software Foundation", "domains": ["python.org", "pypi.org"], "trust_score": 88, "platform_type": "package_repository", "verification_methods": ["package_signature", "maintainer_verification"], "last_verified": "2025-05-25T00:00:00Z"}, {"name": "Node.js Foundation", "domains": ["nodejs.org", "npmjs.com"], "trust_score": 85, "platform_type": "package_repository", "verification_methods": ["package_signature", "maintainer_verification"], "last_verified": "2025-05-25T00:00:00Z"}], "cloud_services": [{"name": "Amazon Web Services", "domains": ["aws.amazon.com", "amazonaws.com"], "trust_score": 93, "service_type": "cloud_infrastructure", "verification_methods": ["ssl_certificate", "service_verification"], "last_verified": "2025-05-25T00:00:00Z"}, {"name": "Microsoft Azure", "domains": ["azure.microsoft.com", "azure.com"], "trust_score": 94, "service_type": "cloud_infrastructure", "verification_methods": ["ssl_certificate", "service_verification"], "last_verified": "2025-05-25T00:00:00Z"}, {"name": "Google Cloud Platform", "domains": ["cloud.google.com", "googleapis.com"], "trust_score": 91, "service_type": "cloud_infrastructure", "verification_methods": ["ssl_certificate", "service_verification"], "last_verified": "2025-05-25T00:00:00Z"}], "certificate_authorities": [{"name": "DigiCert Inc.", "trust_score": 95, "ca_type": "commercial", "root_certificates": ["DigiCert Global Root CA", "DigiCert High Assurance EV Root CA"]}, {"name": "Let's Encrypt", "trust_score": 90, "ca_type": "free", "root_certificates": ["ISRG Root X1", "ISRG Root X2"]}, {"name": "VeriSign/Symantec", "trust_score": 88, "ca_type": "commercial", "root_certificates": ["VeriSign Class 3 Public Primary Certification Authority", "Symantec Class 3 SHA256 Code Signing CA"]}], "validation_rules": {"minimum_trust_score": 75, "require_https": true, "verify_certificates": true, "check_revocation": true, "max_certificate_age_days": 365, "require_extended_validation": false, "allow_self_signed": false, "check_domain_reputation": true}, "blacklisted_sources": [{"domain": "malicious-site.com", "reason": "Known malware distribution", "added_date": "2025-01-01T00:00:00Z", "severity": "high"}, {"domain": "suspicious-downloads.net", "reason": "Suspicious download patterns", "added_date": "2025-02-15T00:00:00Z", "severity": "medium"}], "update_schedule": {"automatic_updates": true, "update_frequency": "daily", "update_sources": ["vendor_feeds", "security_advisories", "community_reports"], "last_update": "2025-05-25T00:00:00Z", "next_update": "2025-05-26T00:00:00Z"}}