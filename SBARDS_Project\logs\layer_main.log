2025-05-25 20:10:13,627 - layer.main - INFO - [run.py:259] - run_single_layer() - Running capture layer only
2025-05-25 20:10:13,629 - layer.main - INFO - [run.py:76] - _initialize_layers() - Initializing layers: ['capture']
2025-05-25 20:10:13,630 - layer.main - INFO - [run.py:116] - _initialize_layer() - Initializing capture layer...
2025-05-25 20:10:13,631 - layer.main - INFO - [run.py:146] - _initialize_capture_layer() - Capture layer initialized (placeholder)
2025-05-25 20:10:13,632 - layer.main - INFO - [run.py:81] - _initialize_layers() - Successfully initialized capture layer
2025-05-25 20:10:13,633 - layer.main - INFO - [run.py:266] - run_single_layer() - capture layer is running...
2025-05-25 20:10:30,916 - layer.main - INFO - [run.py:63] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-25 22:03:20,068 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 22:03:20,074 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 22:03:50,096 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-25 22:03:50,154 - layer.main - INFO - [run.py:485] - _shutdown_single_layer() - Shutting down capture layer...
2025-05-25 22:03:55,247 - layer.main - INFO - [run.py:493] - _shutdown_single_layer() - File interceptor stopped
2025-05-25 22:07:21,181 - layer.main - INFO - [run.py:375] - run_single_layer() - Running api layer only
2025-05-25 22:07:21,182 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 22:07:21,183 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-25 22:07:23,414 - layer.main - ERROR - [run.py:334] - _initialize_api_layer() - Failed to initialize API layer: (sqlite3.DatabaseError) database disk image is malformed
[SQL: PRAGMA main.table_info("scan_reports")]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-05-25 22:07:23,415 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-25 22:07:23,416 - layer.main - ERROR - [run.py:405] - run_single_layer() - Layer api not properly initialized: failed
2025-05-25 22:34:32,529 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 22:34:32,534 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 22:35:16,230 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-25 22:35:17,560 - layer.main - INFO - [run.py:485] - _shutdown_single_layer() - Shutting down capture layer...
2025-05-25 22:35:22,689 - layer.main - INFO - [run.py:493] - _shutdown_single_layer() - File interceptor stopped
2025-05-25 22:39:17,197 - layer.main - INFO - [run.py:375] - run_single_layer() - Running capture layer only
2025-05-25 22:39:17,197 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['capture']
2025-05-25 22:39:17,198 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing capture layer...
2025-05-25 22:39:17,209 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 22:39:17,217 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 22:39:17,232 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized capture layer
2025-05-25 22:40:19,012 - layer.main - INFO - [run.py:375] - run_single_layer() - Running api layer only
2025-05-25 22:40:19,013 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 22:40:19,014 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-25 22:40:21,429 - layer.main - ERROR - [run.py:334] - _initialize_api_layer() - Failed to initialize API layer: (sqlite3.DatabaseError) database disk image is malformed
[SQL: PRAGMA main.table_info("scan_reports")]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-05-25 22:40:21,429 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-25 22:40:21,430 - layer.main - ERROR - [run.py:405] - run_single_layer() - Layer api not properly initialized: failed
2025-05-25 22:41:52,796 - layer.main - INFO - [run.py:375] - run_single_layer() - Running api layer only
2025-05-25 22:41:52,797 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 22:41:52,797 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-25 22:41:55,189 - layer.main - INFO - [run.py:307] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-25 22:41:55,190 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 22:41:55,196 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 22:41:55,208 - layer.main - ERROR - [run.py:334] - _initialize_api_layer() - Failed to initialize API layer: no running event loop
2025-05-25 22:41:55,213 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-25 22:41:55,214 - layer.main - ERROR - [run.py:405] - run_single_layer() - Layer api not properly initialized: failed
2025-05-25 22:46:16,000 - layer.main - INFO - [run.py:375] - run_single_layer() - Running api layer only
2025-05-25 22:46:16,001 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 22:46:16,002 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-25 22:46:18,497 - layer.main - INFO - [run.py:307] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-25 22:46:18,498 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 22:46:18,504 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 22:46:18,517 - layer.main - ERROR - [run.py:334] - _initialize_api_layer() - Failed to initialize API layer: no running event loop
2025-05-25 22:46:18,520 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-25 22:46:18,521 - layer.main - ERROR - [run.py:405] - run_single_layer() - Layer api not properly initialized: failed
2025-05-25 22:49:38,997 - layer.main - INFO - [run.py:375] - run_single_layer() - Running api layer only
2025-05-25 22:49:38,998 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 22:49:38,999 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-25 22:49:41,414 - layer.main - INFO - [run.py:307] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-25 22:49:41,415 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 22:49:41,421 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 22:49:41,433 - layer.main - ERROR - [run.py:334] - _initialize_api_layer() - Failed to initialize API layer: no running event loop
2025-05-25 22:49:41,437 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-25 22:49:41,438 - layer.main - ERROR - [run.py:405] - run_single_layer() - Layer api not properly initialized: failed
2025-05-25 23:21:41,093 - layer.main - INFO - [run.py:378] - run_single_layer() - Running api layer only
2025-05-25 23:21:41,094 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 23:21:41,095 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-25 23:21:42,171 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-25 23:21:42,172 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 23:21:42,177 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 23:21:42,196 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-25 23:21:42,213 - layer.main - INFO - [run.py:421] - _run_api_layer() - ============================================================
2025-05-25 23:21:42,352 - layer.main - INFO - [run.py:426] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-25 23:21:42,353 - layer.main - INFO - [run.py:426] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-25 23:21:42,355 - layer.main - INFO - [run.py:426] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-25 23:21:42,356 - layer.main - INFO - [run.py:426] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-25 23:21:42,357 - layer.main - INFO - [run.py:426] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-25 23:21:42,357 - layer.main - INFO - [run.py:428] - _run_api_layer() - ============================================================
2025-05-25 23:26:55,265 - layer.main - INFO - [run.py:378] - run_single_layer() - Running api layer only
2025-05-25 23:26:55,266 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 23:26:55,266 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-25 23:26:56,115 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-25 23:26:56,116 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 23:26:56,122 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 23:26:56,142 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-25 23:26:56,160 - layer.main - INFO - [run.py:421] - _run_api_layer() - ============================================================
2025-05-25 23:26:56,167 - layer.main - INFO - [run.py:426] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-25 23:26:56,168 - layer.main - INFO - [run.py:426] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-25 23:26:56,169 - layer.main - INFO - [run.py:426] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-25 23:26:56,169 - layer.main - INFO - [run.py:426] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-25 23:26:56,170 - layer.main - INFO - [run.py:426] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-25 23:26:56,171 - layer.main - INFO - [run.py:428] - _run_api_layer() - ============================================================
2025-05-25 23:26:56,244 - layer.main - INFO - [run.py:496] - _shutdown_single_layer() - Shutting down api layer...
2025-05-25 23:27:24,400 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-25 23:27:24,402 - layer.main - INFO - [run.py:496] - _shutdown_single_layer() - Shutting down api layer...
2025-05-25 23:27:30,153 - layer.main - INFO - [run.py:378] - run_single_layer() - Running api layer only
2025-05-25 23:27:30,154 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-25 23:27:30,154 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-25 23:27:30,984 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-25 23:27:30,985 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-25 23:27:30,991 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-25 23:27:31,012 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-25 23:27:31,029 - layer.main - INFO - [run.py:421] - _run_api_layer() - ============================================================
2025-05-25 23:27:31,036 - layer.main - INFO - [run.py:426] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-25 23:27:31,036 - layer.main - INFO - [run.py:426] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-25 23:27:31,037 - layer.main - INFO - [run.py:426] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-25 23:27:31,037 - layer.main - INFO - [run.py:426] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-25 23:27:31,038 - layer.main - INFO - [run.py:426] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-25 23:27:31,038 - layer.main - INFO - [run.py:428] - _run_api_layer() - ============================================================
2025-05-26 00:05:33,413 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 00:05:33,416 - layer.main - INFO - [run.py:496] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 00:05:36,637 - layer.main - INFO - [run.py:378] - run_single_layer() - Running api layer only
2025-05-26 00:05:36,638 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 00:05:36,638 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 00:05:37,514 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 00:05:37,514 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-26 00:05:37,529 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-26 00:05:37,552 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 00:05:37,569 - layer.main - INFO - [run.py:421] - _run_api_layer() - ============================================================
2025-05-26 00:05:37,576 - layer.main - INFO - [run.py:426] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 00:05:37,577 - layer.main - INFO - [run.py:426] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 00:05:37,578 - layer.main - INFO - [run.py:426] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 00:05:37,578 - layer.main - INFO - [run.py:426] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 00:05:37,579 - layer.main - INFO - [run.py:426] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 00:05:37,579 - layer.main - INFO - [run.py:428] - _run_api_layer() - ============================================================
2025-05-26 00:06:06,123 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 00:06:06,130 - layer.main - INFO - [run.py:496] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 00:14:40,880 - layer.main - INFO - [run.py:389] - run_single_layer() - Running api layer only
2025-05-26 00:14:40,881 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 00:14:40,882 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 00:14:41,912 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 00:14:41,912 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-26 00:14:41,926 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-26 00:14:41,945 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 00:14:41,961 - layer.main - INFO - [run.py:432] - _run_api_layer() - ================================================================================
2025-05-26 00:14:42,068 - layer.main - INFO - [run.py:440] - _run_api_layer() - ================================================================================
2025-05-26 00:14:42,087 - layer.main - INFO - [run.py:446] - _run_api_layer() -      Chrome, Firefox, Edge...
2025-05-26 00:14:42,107 - layer.main - INFO - [run.py:446] - _run_api_layer() -      WhatsApp, Telegram, Discord...
2025-05-26 00:14:42,142 - layer.main - INFO - [run.py:446] - _run_api_layer() -      OneDrive, Google Drive, Dropbox...
2025-05-26 00:14:42,172 - layer.main - INFO - [run.py:446] - _run_api_layer() -      Outlook, Thunderbird, Windows Mail...
2025-05-26 00:14:42,224 - layer.main - INFO - [run.py:446] - _run_api_layer() -      USB, External HDD, SD Cards...
2025-05-26 00:14:42,225 - layer.main - INFO - [run.py:448] - _run_api_layer() - ================================================================================
2025-05-26 00:17:50,811 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 00:17:50,815 - layer.main - INFO - [run.py:518] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 00:24:25,446 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 00:24:25,446 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 00:24:25,447 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 00:24:26,379 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 00:24:26,379 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing capture layer components...
2025-05-26 00:24:26,392 - layer.main - INFO - [run.py:218] - _initialize_capture_layer() - File interceptor started successfully
2025-05-26 00:24:26,412 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 00:24:26,426 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 00:24:26,435 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 00:24:26,435 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 00:24:26,436 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 00:24:26,436 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 00:24:26,437 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 00:24:26,437 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 00:24:26,437 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:16:14,036 - layer.main - INFO - [run.py:379] - run_single_layer() - Running capture layer only
2025-05-26 01:16:14,036 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['capture']
2025-05-26 01:16:14,037 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing capture layer...
2025-05-26 01:16:14,104 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:16:14,407 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:16:14,424 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized capture layer
2025-05-26 01:16:16,484 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_011616_408257_mock_file.txt
2025-05-26 01:16:53,474 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 01:16:54,740 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down capture layer...
2025-05-26 01:16:54,761 - layer.main - INFO - [run.py:515] - _shutdown_single_layer() - True Capture Layer stopped and cleaned up
2025-05-26 01:17:31,685 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 01:17:31,686 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 01:17:31,686 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 01:17:32,872 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 01:17:32,874 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:17:33,127 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:17:33,147 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 01:17:33,161 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 01:17:33,169 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:17:33,169 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 01:17:33,170 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 01:17:33,171 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 01:17:33,171 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 01:17:33,172 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 01:17:33,173 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:17:35,183 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_011735_128292_mock_file.txt
2025-05-26 01:21:21,767 - layer.main - INFO - [run.py:379] - run_single_layer() - Running capture layer only
2025-05-26 01:21:21,768 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['capture']
2025-05-26 01:21:21,769 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing capture layer...
2025-05-26 01:21:21,797 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:21:22,098 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:21:22,111 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized capture layer
2025-05-26 01:21:24,177 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_012124_100028_mock_file.txt
2025-05-26 01:22:49,471 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 01:22:52,422 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down capture layer...
2025-05-26 01:22:52,844 - layer.main - INFO - [run.py:515] - _shutdown_single_layer() - True Capture Layer stopped and cleaned up
2025-05-26 01:24:51,390 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 01:24:51,391 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 01:24:51,392 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 01:24:52,460 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 01:24:52,461 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:24:52,675 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:24:52,696 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 01:24:52,711 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 01:24:52,720 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:24:52,720 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 01:24:52,721 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 01:24:52,722 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 01:24:52,722 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 01:24:52,723 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 01:24:52,723 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:24:54,779 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_012454_676359_mock_file.txt
2025-05-26 01:25:07,408 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 01:25:07,410 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 01:26:28,759 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 01:26:28,760 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 01:26:28,760 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 01:26:29,761 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 01:26:29,762 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:26:30,014 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:26:30,048 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 01:26:30,071 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 01:26:30,083 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:26:30,084 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 01:26:30,085 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 01:26:30,086 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 01:26:30,086 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 01:26:30,087 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 01:26:30,088 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:26:32,095 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_012632_015150_mock_file.txt
2025-05-26 01:34:54,155 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 01:34:54,184 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 01:35:28,847 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 01:35:28,848 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 01:35:28,848 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 01:35:29,905 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 01:35:29,906 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:35:30,133 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:35:30,133 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - True Capture Layer initialized successfully with enhanced functionality
2025-05-26 01:35:30,134 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 01:35:30,134 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 01:35:30,135 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 01:35:30,135 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 01:35:30,136 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 01:35:30,136 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 01:35:30,137 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:35:30,137 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 01:35:30,138 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 01:35:30,138 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 01:35:30,139 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 01:35:30,140 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 01:35:30,140 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:35:30,141 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:35:30,141 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 01:35:32,205 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_013532_134287_mock_file.txt
2025-05-26 01:36:55,230 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 01:36:55,234 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 01:36:55,235 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 01:37:18,283 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 01:37:18,284 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 01:37:18,284 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 01:37:19,322 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 01:37:19,323 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:37:19,542 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:37:19,542 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - True Capture Layer initialized successfully with enhanced functionality
2025-05-26 01:37:19,543 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 01:37:19,543 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 01:37:19,544 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 01:37:19,544 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 01:37:19,545 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 01:37:19,545 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 01:37:19,546 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:37:19,546 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 01:37:19,547 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 01:37:19,547 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 01:37:19,548 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 01:37:19,548 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 01:37:19,549 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:37:19,549 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:37:19,549 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 01:37:21,612 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_013721_544039_mock_file.txt
2025-05-26 01:39:47,072 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 01:39:47,075 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 01:39:47,075 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 01:45:17,912 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 01:45:17,913 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 01:45:17,913 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 01:45:18,910 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 01:45:18,911 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:45:19,184 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:45:19,184 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - True Capture Layer initialized successfully with enhanced functionality
2025-05-26 01:45:19,185 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 01:45:19,187 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 01:45:19,188 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 01:45:19,188 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 01:45:19,189 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 01:45:19,191 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 01:45:19,192 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:45:19,192 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 01:45:19,193 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 01:45:19,193 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 01:45:19,194 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 01:45:19,194 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 01:45:19,195 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:45:19,196 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:45:19,196 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 01:45:21,296 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_014521_185522_mock_file.txt
2025-05-26 01:46:21,952 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 01:46:21,954 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 01:46:21,955 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 01:47:35,355 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 01:47:35,356 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 01:47:35,357 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 01:47:36,824 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 01:47:36,825 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:47:37,103 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:47:37,104 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - True Capture Layer initialized successfully with enhanced functionality
2025-05-26 01:47:37,105 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 01:47:37,105 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 01:47:37,106 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 01:47:37,107 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 01:47:37,107 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 01:47:37,108 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 01:47:37,109 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:47:37,110 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 01:47:37,111 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 01:47:37,112 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 01:47:37,112 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 01:47:37,113 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 01:47:37,113 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:47:37,114 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:47:37,114 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 01:47:39,208 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_014739_108312_mock_file.txt
2025-05-26 01:48:23,945 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 01:48:23,949 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 01:48:23,951 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 01:55:35,678 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 01:55:35,679 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 01:55:35,679 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 01:55:36,764 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 01:55:36,765 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 01:55:37,124 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 01:55:37,125 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - True Capture Layer initialized successfully with enhanced functionality
2025-05-26 01:55:37,125 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 01:55:37,126 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 01:55:37,126 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 01:55:37,127 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 01:55:37,127 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 01:55:37,128 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 01:55:37,128 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:55:37,129 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 01:55:37,129 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 01:55:37,130 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 01:55:37,130 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 01:55:37,130 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 01:55:37,131 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 01:55:37,132 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 01:55:37,132 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 01:55:39,232 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_015539_127212_mock_file.txt
2025-05-26 02:01:02,395 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 02:01:02,395 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 02:01:02,396 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 02:01:03,308 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 02:01:03,308 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 02:01:03,521 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 02:01:03,521 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - True Capture Layer initialized successfully with enhanced functionality
2025-05-26 02:01:03,522 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 02:01:03,522 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 02:01:03,523 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 02:01:03,524 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 02:01:03,524 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 02:01:03,525 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 02:01:03,526 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:01:03,526 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 02:01:03,527 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 02:01:03,527 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 02:01:03,528 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 02:01:03,528 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 02:01:03,529 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 02:01:03,529 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:01:03,530 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 02:01:05,607 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_020105_522393_mock_file.txt
2025-05-26 02:11:40,821 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 02:11:40,822 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 02:11:40,822 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 02:11:41,897 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 02:11:41,898 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 02:11:42,167 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 02:11:42,169 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 02:11:42,171 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 02:11:42,172 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 02:11:42,173 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 02:11:42,174 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 02:11:42,177 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 02:11:42,180 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 02:11:42,180 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:11:42,181 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 02:11:42,181 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 02:11:42,182 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 02:11:42,183 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 02:11:42,248 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 02:11:42,382 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 02:11:42,401 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:11:42,414 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 02:11:44,264 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_021144_167688_mock_file.txt
2025-05-26 02:15:41,388 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 02:15:41,390 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 02:15:41,391 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 02:15:42,654 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 02:15:42,655 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 02:15:42,994 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 02:15:42,995 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 02:15:42,995 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 02:15:42,996 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 02:15:42,996 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 02:15:42,997 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 02:15:42,997 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 02:15:42,998 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 02:15:42,998 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:15:42,998 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 02:15:42,999 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 02:15:43,000 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 02:15:43,000 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 02:15:43,001 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 02:15:43,001 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 02:15:43,002 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:15:43,002 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 02:15:45,068 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_021544_995170_mock_file.txt
2025-05-26 02:20:00,441 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 02:20:00,444 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 02:20:00,445 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 02:24:45,562 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 02:24:45,563 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 02:24:45,564 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 02:24:46,400 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 02:24:46,401 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 02:24:46,590 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 02:24:46,590 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 02:24:46,591 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 02:24:46,591 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 02:24:46,592 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 02:24:46,592 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 02:24:46,592 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 02:24:46,593 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 02:24:46,593 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:24:46,593 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 02:24:46,594 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 02:24:46,594 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 02:24:46,595 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 02:24:46,595 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 02:24:46,596 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 02:24:46,596 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:24:46,596 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 02:24:48,652 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_022448_591071_mock_file.txt
2025-05-26 02:48:12,301 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 02:48:12,302 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 02:48:12,302 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 02:48:13,163 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 02:48:13,164 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 02:48:13,367 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 02:48:13,368 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 02:48:13,368 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 02:48:13,369 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 02:48:13,371 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 02:48:13,372 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 02:48:13,372 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 02:48:13,373 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 02:48:13,374 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:48:13,375 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 02:48:13,376 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 02:48:13,377 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 02:48:13,377 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 02:48:13,378 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 02:48:13,379 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 02:48:13,379 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 02:48:13,380 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 02:48:15,446 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_024815_368441_mock_file.txt
2025-05-26 02:48:36,918 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 02:48:36,922 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 02:48:36,926 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 03:02:39,251 - layer.main - INFO - [run.py:379] - run_single_layer() - Running static_analysis layer only
2025-05-26 03:02:39,252 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['static_analysis']
2025-05-26 03:02:39,252 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing static_analysis layer...
2025-05-26 03:02:39,253 - layer.main - INFO - [run.py:245] - _initialize_static_analysis_layer() - Static analysis layer initialized (placeholder)
2025-05-26 03:02:39,253 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized static_analysis layer
2025-05-26 03:02:39,254 - layer.main - INFO - [run.py:389] - run_single_layer() - static_analysis layer is running...
2025-05-26 03:02:52,487 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 03:02:53,261 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down static_analysis layer...
2025-05-26 03:02:53,262 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - static_analysis layer shutdown completed
2025-05-26 03:10:37,359 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 03:10:37,360 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 03:10:37,360 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 03:10:38,214 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 03:10:38,215 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 03:10:38,412 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 03:10:38,412 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 03:10:38,413 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 03:10:38,413 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 03:10:38,413 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 03:10:38,414 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 03:10:38,414 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 03:10:38,415 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 03:10:38,415 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 03:10:38,416 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 03:10:38,416 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 03:10:38,417 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 03:10:38,417 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 03:10:38,418 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 03:10:38,418 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 03:10:38,418 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 03:10:38,419 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 03:10:40,477 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_031040_412449_mock_file.txt
2025-05-26 03:12:52,698 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 03:12:52,698 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 03:12:52,699 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 03:12:53,542 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 03:12:53,542 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 03:12:53,737 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 03:12:53,737 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 03:12:53,738 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 03:12:53,738 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 03:12:53,739 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 03:12:53,739 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 03:12:53,740 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 03:12:53,740 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 03:12:53,741 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 03:12:53,741 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 03:12:53,742 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 03:12:53,742 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 03:12:53,743 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 03:12:53,743 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 03:12:53,744 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 03:12:53,744 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 03:12:53,745 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 03:12:55,803 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_031255_737361_mock_file.txt
2025-05-26 03:39:33,025 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 03:39:33,026 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 03:39:33,026 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 03:39:33,818 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 03:39:33,818 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 03:39:34,021 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 03:39:34,022 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 03:39:34,022 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 03:39:34,023 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 03:39:34,023 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 03:39:34,024 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 03:39:34,024 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 03:39:34,025 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 03:39:34,026 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 03:39:34,026 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 03:39:34,027 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 03:39:34,027 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 03:39:34,027 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 03:39:34,028 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 03:39:34,028 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 03:39:34,029 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 03:39:34,029 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 03:39:36,092 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_033936_022271_mock_file.txt
2025-05-26 03:40:05,599 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 03:40:05,601 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 03:40:05,602 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 04:26:53,180 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 04:26:53,181 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 04:26:53,181 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 04:26:55,951 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 04:26:55,951 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 04:26:56,140 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 04:26:56,140 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 04:26:56,141 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 04:26:56,141 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 04:26:56,142 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 04:26:56,143 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 04:26:56,144 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 04:26:56,144 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 04:26:56,145 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:26:56,145 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 04:26:56,146 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 04:26:56,147 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 04:26:56,148 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 04:26:56,148 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 04:26:56,148 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 04:26:56,149 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:26:56,149 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 04:26:58,209 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_042658_140678_mock_file.txt
2025-05-26 04:29:35,404 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 04:29:35,405 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 04:29:35,406 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 04:29:37,976 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 04:29:37,977 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 04:29:38,166 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 04:29:38,167 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 04:29:38,167 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 04:29:38,168 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 04:29:38,168 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 04:29:38,169 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 04:29:38,169 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 04:29:38,169 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 04:29:38,170 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:29:38,170 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 04:29:38,171 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 04:29:38,172 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 04:29:38,172 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 04:29:38,173 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 04:29:38,174 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 04:29:38,174 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:29:38,175 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 04:29:40,233 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_042940_167050_mock_file.txt
2025-05-26 04:29:40,413 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 04:29:40,414 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 04:29:55,753 - layer.main - INFO - [run.py:64] - _signal_handler() - Received signal 2, shutting down gracefully...
2025-05-26 04:29:55,755 - layer.main - INFO - [run.py:507] - _shutdown_single_layer() - Shutting down api layer...
2025-05-26 04:29:55,756 - layer.main - INFO - [run.py:526] - _shutdown_single_layer() - api layer shutdown completed
2025-05-26 04:30:03,163 - layer.main - INFO - [run.py:379] - run_single_layer() - Running api layer only
2025-05-26 04:30:03,163 - layer.main - INFO - [run.py:77] - _initialize_layers() - Initializing layers: ['api']
2025-05-26 04:30:03,164 - layer.main - INFO - [run.py:117] - _initialize_layer() - Initializing api layer...
2025-05-26 04:30:05,627 - layer.main - INFO - [run.py:306] - _initialize_api_layer() - Initializing API layer with FastAPI...
2025-05-26 04:30:05,628 - layer.main - INFO - [run.py:148] - _initialize_capture_layer() - Initializing True Capture Layer components...
2025-05-26 04:30:05,814 - layer.main - INFO - [run.py:181] - _initialize_capture_layer() - True Capture Layer started successfully
2025-05-26 04:30:05,814 - layer.main - INFO - [run.py:231] - _initialize_capture_layer() - SUCCESS: True Capture Layer initialized successfully with enhanced functionality
2025-05-26 04:30:05,815 - layer.main - INFO - [run.py:332] - _initialize_api_layer() - API layer initialized successfully
2025-05-26 04:30:05,815 - layer.main - INFO - [run.py:82] - _initialize_layers() - Successfully initialized api layer
2025-05-26 04:30:05,816 - layer.main - INFO - [run.py:389] - run_single_layer() - api layer is running...
2025-05-26 04:30:05,816 - layer.main - INFO - [run.py:421] - _run_api_layer() - Starting FastAPI server on 127.0.0.1:8000
2025-05-26 04:30:05,817 - layer.main - INFO - [run.py:422] - _run_api_layer() - ============================================================
2025-05-26 04:30:05,817 - layer.main - INFO - [run.py:423] - _run_api_layer() - Available Endpoints:
2025-05-26 04:30:05,817 - layer.main - INFO - [run.py:427] - _run_api_layer() -   dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:30:05,818 - layer.main - INFO - [run.py:427] - _run_api_layer() -   health: http://127.0.0.1:8000/api/health/page
2025-05-26 04:30:05,818 - layer.main - INFO - [run.py:427] - _run_api_layer() -   capture_status: http://127.0.0.1:8000/api/capture/status/page
2025-05-26 04:30:05,819 - layer.main - INFO - [run.py:427] - _run_api_layer() -   monitoring_info: http://127.0.0.1:8000/api/capture/monitoring-info
2025-05-26 04:30:05,819 - layer.main - INFO - [run.py:427] - _run_api_layer() -   docs: http://127.0.0.1:8000/api/docs
2025-05-26 04:30:05,820 - layer.main - INFO - [run.py:427] - _run_api_layer() -   upload: http://127.0.0.1:8000/api/upload
2025-05-26 04:30:05,820 - layer.main - INFO - [run.py:429] - _run_api_layer() - ============================================================
2025-05-26 04:30:05,821 - layer.main - INFO - [run.py:430] - _run_api_layer() - Main Dashboard: http://127.0.0.1:8000/dashboard
2025-05-26 04:30:05,822 - layer.main - INFO - [run.py:431] - _run_api_layer() - API Documentation: http://127.0.0.1:8000/api/docs
2025-05-26 04:30:07,869 - layer.main - INFO - [run.py:172] - static_analysis_callback() - Static analysis request for: capture\temp_storage\incoming\20250526_043007_814646_mock_file.txt
