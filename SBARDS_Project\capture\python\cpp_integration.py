#!/usr/bin/env python3
"""
C++ Integration Module for True File Interceptor
وحدة التكامل مع C++ للمُعترض الحقيقي

هذا الملف يوفر التكامل بين مكون C++ (الاعتراض على مستوى النواة)
ومكون Python (المعالجة والتنسيق)

الوظائف:
✅ تحميل مكتبة C++ المترجمة
✅ تعيين callbacks للتكامل
✅ تمرير الأحداث بين C++ و Python
✅ إدارة دورة حياة المكونات
"""

import os
import sys
import ctypes
import platform
from typing import Dict, Any, Optional, Callable
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.logger import get_global_logger

class CppInterceptorBridge:
    """جسر التكامل مع مكون C++"""
    
    def __init__(self):
        self.logger = get_global_logger().get_layer_logger("capture")
        self.cpp_lib = None
        self.python_callback = None
        self.is_initialized = False
        
        # تحديد مسار المكتبة حسب نظام التشغيل
        self.lib_path = self._get_library_path()
        
    def _get_library_path(self) -> Optional[str]:
        """تحديد مسار مكتبة C++"""
        base_path = Path(__file__).parent.parent / "cpp"
        
        if platform.system() == "Windows":
            lib_file = base_path / "true_file_interceptor.dll"
        elif platform.system() == "Darwin":  # macOS
            lib_file = base_path / "libtrue_file_interceptor.dylib"
        else:  # Linux
            lib_file = base_path / "libtrue_file_interceptor.so"
        
        if lib_file.exists():
            return str(lib_file)
        else:
            self.logger.warning(f"C++ library not found: {lib_file}")
            return None
    
    def initialize(self) -> bool:
        """تهيئة التكامل مع C++"""
        if self.is_initialized:
            return True
        
        if not self.lib_path:
            self.logger.error("C++ library path not available")
            return False
        
        try:
            # تحميل المكتبة
            self.cpp_lib = ctypes.CDLL(self.lib_path)
            
            # تعريف أنواع الدوال
            self._define_function_types()
            
            # إنشاء مثيل المُعترض
            if not self.cpp_lib.create_interceptor():
                self.logger.error("Failed to create C++ interceptor instance")
                return False
            
            # تعيين callback
            self._setup_callback()
            
            self.is_initialized = True
            self.logger.info("C++ integration initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize C++ integration: {e}")
            return False
    
    def _define_function_types(self):
        """تعريف أنواع دوال C++"""
        # دالة إنشاء المُعترض
        self.cpp_lib.create_interceptor.restype = ctypes.c_bool
        
        # دالة تشغيل المُعترض
        self.cpp_lib.start_interceptor.restype = ctypes.c_bool
        
        # دالة إيقاف المُعترض
        self.cpp_lib.stop_interceptor.restype = None
        
        # دالة تدمير المُعترض
        self.cpp_lib.destroy_interceptor.restype = None
        
        # دالة تعيين callback
        self.cpp_lib.set_python_callback.argtypes = [ctypes.CFUNCTYPE(
            ctypes.c_bool,  # return type
            ctypes.c_char_p,  # file_path
            ctypes.c_char_p,  # intended_path
            ctypes.POINTER(ctypes.c_uint8),  # file_data
            ctypes.c_size_t   # file_size
        )]
        self.cpp_lib.set_python_callback.restype = None
    
    def _setup_callback(self):
        """إعداد callback للتكامل"""
        # تعريف نوع callback
        CALLBACK_TYPE = ctypes.CFUNCTYPE(
            ctypes.c_bool,
            ctypes.c_char_p,
            ctypes.c_char_p,
            ctypes.POINTER(ctypes.c_uint8),
            ctypes.c_size_t
        )
        
        # إنشاء callback function
        def cpp_callback(file_path_ptr, intended_path_ptr, data_ptr, data_size):
            """Callback يتم استدعاؤه من C++"""
            try:
                # تحويل البيانات من C++ إلى Python
                file_path = file_path_ptr.decode('utf-8') if file_path_ptr else ""
                intended_path = intended_path_ptr.decode('utf-8') if intended_path_ptr else ""
                
                # تحويل البيانات الثنائية
                file_data = bytes((data_ptr[i] for i in range(data_size))) if data_ptr and data_size > 0 else b""
                
                # إنشاء معلومات الحدث
                event_info = {
                    'file_path': file_path,
                    'intended_path': intended_path,
                    'file_data': file_data,
                    'file_size': data_size,
                    'source': 'cpp_interceptor'
                }
                
                # استدعاء Python callback
                if self.python_callback:
                    return self.python_callback(event_info)
                else:
                    self.logger.warning("No Python callback registered")
                    return False
                    
            except Exception as e:
                self.logger.error(f"Error in C++ callback: {e}")
                return False
        
        # تعيين callback في C++
        self.cpp_callback_func = CALLBACK_TYPE(cpp_callback)
        self.cpp_lib.set_python_callback(self.cpp_callback_func)
        
        self.logger.info("C++ callback setup completed")
    
    def set_python_callback(self, callback: Callable[[Dict[str, Any]], bool]):
        """تعيين Python callback"""
        self.python_callback = callback
        self.logger.info("Python callback registered")
    
    def start(self) -> bool:
        """تشغيل المُعترض C++"""
        if not self.is_initialized:
            self.logger.error("C++ integration not initialized")
            return False
        
        try:
            success = self.cpp_lib.start_interceptor()
            if success:
                self.logger.info("C++ interceptor started successfully")
            else:
                self.logger.error("Failed to start C++ interceptor")
            return success
        except Exception as e:
            self.logger.error(f"Error starting C++ interceptor: {e}")
            return False
    
    def stop(self):
        """إيقاف المُعترض C++"""
        if not self.is_initialized:
            return
        
        try:
            self.cpp_lib.stop_interceptor()
            self.logger.info("C++ interceptor stopped")
        except Exception as e:
            self.logger.error(f"Error stopping C++ interceptor: {e}")
    
    def cleanup(self):
        """تنظيف الموارد"""
        if not self.is_initialized:
            return
        
        try:
            self.stop()
            self.cpp_lib.destroy_interceptor()
            self.is_initialized = False
            self.logger.info("C++ integration cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

class MockCppInterceptor:
    """مُعترض وهمي للاختبار عندما لا تكون مكتبة C++ متاحة"""
    
    def __init__(self):
        self.logger = get_global_logger().get_layer_logger("capture")
        self.python_callback = None
        self.running = False
        
    def initialize(self) -> bool:
        self.logger.info("Mock C++ interceptor initialized")
        return True
    
    def set_python_callback(self, callback: Callable[[Dict[str, Any]], bool]):
        self.python_callback = callback
        self.logger.info("Mock Python callback registered")
    
    def start(self) -> bool:
        self.running = True
        self.logger.info("Mock C++ interceptor started")
        
        # محاكاة اعتراض ملف للاختبار
        if self.python_callback:
            import threading
            import time
            
            def mock_intercept():
                time.sleep(2)  # انتظار قصير
                if self.running:
                    mock_event = {
                        'file_path': '/tmp/mock_intercepted_file.txt',
                        'intended_path': '/home/<USER>/Downloads/mock_file.txt',
                        'file_data': b'Mock file content for testing',
                        'file_size': 28,
                        'source': 'mock_cpp_interceptor'
                    }
                    self.python_callback(mock_event)
            
            threading.Thread(target=mock_intercept, daemon=True).start()
        
        return True
    
    def stop(self):
        self.running = False
        self.logger.info("Mock C++ interceptor stopped")
    
    def cleanup(self):
        self.stop()
        self.logger.info("Mock C++ interceptor cleaned up")

def create_cpp_bridge(use_mock: bool = False) -> CppInterceptorBridge:
    """
    إنشاء جسر التكامل مع C++
    
    Args:
        use_mock: استخدام المُعترض الوهمي للاختبار
    
    Returns:
        CppInterceptorBridge instance
    """
    if use_mock:
        return MockCppInterceptor()
    else:
        return CppInterceptorBridge()

# اختبار التكامل
if __name__ == "__main__":
    print("Testing C++ Integration Bridge")
    print("=" * 40)
    
    # إنشاء جسر التكامل
    bridge = create_cpp_bridge(use_mock=True)  # استخدام mock للاختبار
    
    # تعيين callback للاختبار
    def test_callback(event_info: Dict[str, Any]) -> bool:
        print(f"Received event from C++:")
        print(f"  File path: {event_info['file_path']}")
        print(f"  Intended path: {event_info['intended_path']}")
        print(f"  File size: {event_info['file_size']}")
        print(f"  Source: {event_info['source']}")
        print(f"  Data preview: {event_info['file_data'][:50]}...")
        return True
    
    bridge.set_python_callback(test_callback)
    
    # تهيئة وتشغيل
    if bridge.initialize():
        print("Bridge initialized successfully")
        
        if bridge.start():
            print("Bridge started successfully")
            print("Waiting for events... (Press Enter to stop)")
            
            try:
                input()
            except KeyboardInterrupt:
                pass
            
            bridge.stop()
        else:
            print("Failed to start bridge")
        
        bridge.cleanup()
    else:
        print("Failed to initialize bridge")
