<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBARDS v2.0 - Enhanced Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
            color: #fff !important;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .threat-high { color: #dc3545; }
        .threat-medium { color: #fd7e14; }
        .threat-low { color: #ffc107; }
        .threat-safe { color: #198754; }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active { background-color: #198754; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .status-disabled { background-color: #6c757d; }
        .layer-card {
            margin-bottom: 15px;
        }
        .progress-custom {
            height: 8px;
            border-radius: 4px;
        }
        .alert-item {
            border-left: 4px solid;
            margin-bottom: 10px;
            padding: 10px 15px;
            border-radius: 0 5px 5px 0;
        }
        .alert-critical { border-left-color: #dc3545; background-color: #f8d7da; }
        .alert-warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .alert-info { border-left-color: #0dcaf0; background-color: #d1ecf1; }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .header-stats {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                SBARDS v2.0 Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </span>
                <span class="navbar-text">
                    <span class="status-indicator status-active"></span>
                    System Active
                </span>
            </div>
        </div>
    </nav>

    <!-- Header Stats -->
    <div class="header-stats">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number" id="total-scans">-</div>
                        <div class="stat-label">Total Scans</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number" id="files-scanned">-</div>
                        <div class="stat-label">Files Scanned</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number threat-high" id="threats-detected">-</div>
                        <div class="stat-label">Threats Detected</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number" id="system-uptime">-</div>
                        <div class="stat-label">System Uptime</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <!-- Layer Status -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-layer-group me-2"></i>
                            Layer Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="layer-status">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i> Loading...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Performance -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            System Performance
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>CPU Usage</span>
                                <span id="cpu-usage">-</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar bg-primary" id="cpu-progress" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Memory Usage</span>
                                <span id="memory-usage">-</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar bg-warning" id="memory-progress" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Disk Usage</span>
                                <span id="disk-usage">-</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar bg-info" id="disk-progress" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Scans & Alerts -->
            <div class="col-lg-8">
                <!-- Recent Scans -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-search me-2"></i>
                            Recent Scans
                        </h5>
                        <button class="btn btn-primary btn-sm" onclick="startNewScan()">
                            <i class="fas fa-play me-1"></i>
                            New Scan
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Scan ID</th>
                                        <th>Path</th>
                                        <th>Files</th>
                                        <th>Threats</th>
                                        <th>Status</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-scans-table">
                                    <tr>
                                        <td colspan="6" class="loading">
                                            <i class="fas fa-spinner fa-spin"></i> Loading recent scans...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Threat Alerts -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Threat Alerts
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="threat-alerts">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i> Loading alerts...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="refreshDashboard()" title="Refresh Dashboard">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // API Base URL
        const API_BASE = window.location.origin;

        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        }

        // Fetch and update statistics
        async function updateStatistics() {
            try {
                const response = await fetch(`${API_BASE}/api/statistics`);
                const data = await response.json();
                
                document.getElementById('total-scans').textContent = data.total_scans || 0;
                document.getElementById('files-scanned').textContent = data.files_scanned || 0;
                document.getElementById('threats-detected').textContent = data.threats_detected || 0;
                
                // Update performance metrics
                if (data.performance_metrics) {
                    const cpu = data.performance_metrics.cpu_usage || 0;
                    const memory = data.performance_metrics.memory_usage || 0;
                    const disk = data.performance_metrics.disk_usage || 0;
                    
                    document.getElementById('cpu-usage').textContent = `${cpu}%`;
                    document.getElementById('memory-usage').textContent = `${memory}%`;
                    document.getElementById('disk-usage').textContent = `${disk}%`;
                    
                    document.getElementById('cpu-progress').style.width = `${cpu}%`;
                    document.getElementById('memory-progress').style.width = `${memory}%`;
                    document.getElementById('disk-progress').style.width = `${disk}%`;
                }
            } catch (error) {
                console.error('Error fetching statistics:', error);
            }
        }

        // Fetch and update system status
        async function updateSystemStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/status`);
                const data = await response.json();
                
                if (data.uptime) {
                    document.getElementById('system-uptime').textContent = data.uptime;
                }
            } catch (error) {
                console.error('Error fetching system status:', error);
            }
        }

        // Fetch and update layer status
        async function updateLayerStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/layer-status`);
                const data = await response.json();
                
                let html = '';
                for (const [layer, status] of Object.entries(data)) {
                    const statusClass = status.status === 'active' ? 'status-active' : 'status-disabled';
                    html += `
                        <div class="layer-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>
                                    <span class="status-indicator ${statusClass}"></span>
                                    ${layer.charAt(0).toUpperCase() + layer.slice(1)}
                                </span>
                                <span class="badge bg-${status.status === 'active' ? 'success' : 'secondary'}">
                                    ${status.status}
                                </span>
                            </div>
                        </div>
                    `;
                }
                
                document.getElementById('layer-status').innerHTML = html;
            } catch (error) {
                console.error('Error fetching layer status:', error);
                document.getElementById('layer-status').innerHTML = '<div class="text-danger">Error loading layer status</div>';
            }
        }

        // Fetch and update recent scans
        async function updateRecentScans() {
            try {
                const response = await fetch(`${API_BASE}/api/recent-scans?limit=10`);
                const data = await response.json();
                
                let html = '';
                if (data.length === 0) {
                    html = '<tr><td colspan="6" class="text-center text-muted">No recent scans</td></tr>';
                } else {
                    data.forEach(scan => {
                        const threatClass = scan.threats_found > 0 ? 'text-danger' : 'text-success';
                        const statusBadge = scan.status === 'completed' ? 'bg-success' : 'bg-warning';
                        
                        html += `
                            <tr>
                                <td><code>${scan.scan_id}</code></td>
                                <td>${scan.path}</td>
                                <td>${scan.files_scanned || 0}</td>
                                <td class="${threatClass}">${scan.threats_found || 0}</td>
                                <td><span class="badge ${statusBadge}">${scan.status}</span></td>
                                <td>${new Date(scan.timestamp).toLocaleString()}</td>
                            </tr>
                        `;
                    });
                }
                
                document.getElementById('recent-scans-table').innerHTML = html;
            } catch (error) {
                console.error('Error fetching recent scans:', error);
                document.getElementById('recent-scans-table').innerHTML = 
                    '<tr><td colspan="6" class="text-danger">Error loading scans</td></tr>';
            }
        }

        // Fetch and update threat alerts
        async function updateThreatAlerts() {
            try {
                const response = await fetch(`${API_BASE}/api/threat-alerts?limit=5`);
                const data = await response.json();
                
                let html = '';
                if (data.length === 0) {
                    html = '<div class="text-center text-muted">No recent alerts</div>';
                } else {
                    data.forEach(alert => {
                        const alertClass = alert.severity === 'critical' ? 'alert-critical' : 
                                         alert.severity === 'warning' ? 'alert-warning' : 'alert-info';
                        
                        html += `
                            <div class="alert-item ${alertClass}">
                                <div class="d-flex justify-content-between">
                                    <strong>${alert.title}</strong>
                                    <small>${new Date(alert.timestamp).toLocaleString()}</small>
                                </div>
                                <div>${alert.description}</div>
                            </div>
                        `;
                    });
                }
                
                document.getElementById('threat-alerts').innerHTML = html;
            } catch (error) {
                console.error('Error fetching threat alerts:', error);
                document.getElementById('threat-alerts').innerHTML = 
                    '<div class="text-danger">Error loading alerts</div>';
            }
        }

        // Start a new scan
        function startNewScan() {
            const path = prompt('Enter path to scan:', '/path/to/scan');
            if (path) {
                fetch(`${API_BASE}/api/start-scan`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        path: path,
                        type: 'full'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.scan_id) {
                        alert(`Scan started with ID: ${data.scan_id}`);
                        setTimeout(updateRecentScans, 2000);
                    } else {
                        alert('Error starting scan: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error starting scan:', error);
                    alert('Error starting scan');
                });
            }
        }

        // Refresh all dashboard data
        function refreshDashboard() {
            updateStatistics();
            updateSystemStatus();
            updateLayerStatus();
            updateRecentScans();
            updateThreatAlerts();
        }

        // Initialize dashboard
        function initDashboard() {
            updateTime();
            refreshDashboard();
            
            // Set up periodic updates
            setInterval(updateTime, 1000);
            setInterval(refreshDashboard, 30000); // Refresh every 30 seconds
        }

        // Start dashboard when page loads
        document.addEventListener('DOMContentLoaded', initDashboard);
    </script>
</body>
</html>
